import request from '@/utils/request'

// ==================== 活动管理相关接口 ====================

// 获取活动列表
export function getActivityList(data) {
  return request({
    url: '/act-manage/activity/getActivityList',
    method: 'post',
    data
  })
}

// 获取活动详情
export function getActivityDetail(id) {
  return request({
    url: '/act-manage/activity/getActivityDetail',
    method: 'get',
    params: { id }
  })
}

// 新增活动
export function addActivity(data) {
  return request({
    url: '/act-manage/activity/addActivity',
    method: 'post',
    data
  })
}

// 修改活动
export function modifyActivity(data) {
  return request({
    url: '/act-manage/activity/modifyActivity',
    method: 'post',
    data
  })
}

// 上架活动
export function onSaleActivity(id) {
  return request({
    url: '/act-manage/activity/onSale',
    method: 'get',
    params: { id }
  })
}

// 下架活动
export function downSaleActivity(id) {
  return request({
    url: '/act-manage/activity/downSale',
    method: 'get',
    params: { id }
  })
}

// 删除活动
export function deleteActivity(id) {
  return request({
    url: '/act-manage/activity/deleteActivity',
    method: 'get',
    params: { id }
  })
}

// 导出活动表格
export function exportActivityExcel(data) {
  return request({
    url: '/act-manage/activity/actExcel',
    method: 'post',

    data
  })
}

// ==================== 旧接口保留 ====================

export function actSearch(data) {
  return request({
    url: '/homePage/search',
    method: 'post',
    data
  })
}

// 相亲活动新增或编辑
export function actAddOrEdit(data, url) {
  return request({
    url: `/blindDate/${url}`,
    method: 'post',
    data
  })
}

// 相亲活动提交审核
export function submitAudit(data) {
  return request({
    url: '/blindDate/submitAudit',
    method: 'post',
    data
  })
}

// 相亲活动上下线
export function actUpDown(data) {
  return request({
    url: '/blindDate/upDown',
    method: 'post',
    data
  })
}

// 相亲活动审核
export function actAudit(data) {
  return request({
    url: '/blindDate/audit',
    method: 'post',
    data
  })
}

// 获取商户列表
export function businessList() {
  return request({
    url: '/business/list',
    method: 'post',
    data: {
      'pageNum': '1',
      'pageSize': '999'
    }
  })
}

// 报名信息导出
export function signExport(data) {
  return request({
    url: '/blindDate/export',
    responseType: 'blob',
    method: 'post',
    data
  })
}

// 商户新增或编辑
export function businessAddOrEdit(data, url) {
  return request({
    url: `/business/${url}`,
    method: 'post',
    data
  })
}

// ==================== 活动类型管理相关接口 ====================

// 获取活动类型信息
export function getActivityType(data) {
  return request({
    url: '/act-manage/activityType/getActivityType',
    method: 'post',
    data
  })
}

// 添加活动类型信息
export function addActivityType(data) {
  return request({
    url: '/act-manage/activityType/addType',
    method: 'post',
    data
  })
}

// 获取活动类型详细信息
export function getActivityTypeDetail(data) {
  return request({
    url: '/act-manage/activityType/getTypeDetail',
    method: 'get',
    params: data
  })
}

// 修改活动类型信息
export function modifyActivityType(data) {
  return request({
    url: '/act-manage/activityType/modifyType',
    method: 'post',
    data
  })
}

// 删除活动类型信息（逻辑删除）
export function deleteActivityType(data) {
  return request({
    url: '/act-manage/activityType/modifyType',
    method: 'post',
    data
  })
}
