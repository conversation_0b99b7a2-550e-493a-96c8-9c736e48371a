<template>
  <div>
    <RouteTitle />
    <div class="main-body">
      <div class="main-body-bottom">
        <div v-if="addPermission || deleteMultiplePermission" class="main-body-bottom-btn">
          <div class="main-body-bottom-btn-left">
            <el-button
              v-if="addPermission"
              type="primary"
              @click="onOperate('singleFile')"
            >单文件上传</el-button>
            <el-button
              v-if="addPermission"
              type="primary"
              @click="onOperate('singleImage')"
            >单图片上传</el-button>
            <el-upload
              style="display: inline-block;margin-left: 10px;"
              action="fakeaction"
              :show-file-list="false"
              :http-request="uploadForeverFile"
            >
              <el-button
                v-preventReClick
                type="primary"
              >永久文件上传</el-button>
            </el-upload>
          </div>
        </div>
        <div class="table">
          <el-table
            ref="tableRef"
            :data="mainObj.list"
            fit
            border
            highlight-current-row
            :header-cell-style="{'background': '#F9F9F9'}"
          >
            <el-table-column
              type="selection"
              width="55"
            />
            <el-table-column
              prop="id"
              label="文件id"
              min-width="180px"
            />
            <el-table-column
              prop="name"
              label="文件名"
              min-width="300px"
            />
            <!--
            <el-table-column
              prop="createTime"
              label="创建时间"
              width="180px"
            />
            -->
            <el-table-column
              prop="updateTime"
              label="更新时间"
              width="180px"
            />
            <el-table-column label="操作" width="280">
              <template slot-scope="scope">
                <el-button
                  v-if="updatePermission"
                  v-preventReClick
                  type="text"
                  @click="onOperate('download', scope.row)"
                >下载</el-button>
                <el-button
                  type="text"
                  @click="onOperate('downloadStream', scope.row)"
                >文件流下载</el-button>
                <el-button
                  v-if="deleteSinglePermission"
                  type="text"
                  @click="onOperate('delete', scope.row)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination
            :current-page="mainObj.currentPage"
            :page-size="mainObj.pageSize"
            :page-sizes="mainObj.pageSizes"
            :total="mainObj.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <!-- 单文件上传弹窗 -->
    <el-dialog
      :title="singleFileDialogObj.title"
      :visible.sync="singleFileDialogObj.visible"
      destroy-on-close
      width="800px"
      @close="singleFileDialogObj.visible = false"
    >
      <el-form
        ref="singleFileFormRef"
        :model="singleFileDialogObj.form"
        :rules="singleFileDialogObj.rules"
        label-width="80px"
        label-position="left"
      >
        <el-form-item label="访问权限" prop="access">
          <el-radio-group v-model="singleFileDialogObj.form.access">
            <el-radio label="1">公开</el-radio>
            <el-radio label="2">私有</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="文件上传"
          prop="fileList.length"
          :rules="singleFileDialogObj.rules.fileList"
        >
          <el-upload
            ref="upload"
            action="fakeaction"
            :file-list="singleFileDialogObj.form.fileList"
            :auto-upload="false"
            :on-change="onChangeFile"
            :on-remove="onRemoveFile"
          >
            <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
          </el-upload>
          <el-input v-model="singleFileDialogObj.form.fileList.length" style="display: none;" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button
          v-preventReClick
          type="primary"
          @click="onOperate('singleFileSubmit')"
        >确定</el-button>
        <el-button @click="singleFileDialogObj.visible = false">取消</el-button>
      </div>
    </el-dialog>
    <!-- 单图片上传弹窗 -->
    <el-dialog
      :title="singleImageDialogObj.title"
      :visible.sync="singleImageDialogObj.visible"
      destroy-on-close
      width="800px"
      @close="singleImageDialogObj.visible = false"
    >
      <el-form
        ref="singleImageFormRef"
        :model="singleImageDialogObj.form"
        :rules="singleImageDialogObj.rules"
        label-width="80px"
        label-position="left"
      >
        <el-form-item label="访问权限" prop="access">
          <el-radio-group v-model="singleImageDialogObj.form.access">
            <el-radio label="1">公开</el-radio>
            <el-radio label="2">私有</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="图片上传"
          prop="fileList.length"
          :rules="singleImageDialogObj.rules.fileList"
        >
          <el-upload
            ref="upload"
            action="fakeaction"
            :file-list="singleImageDialogObj.form.fileList"
            :auto-upload="false"
            :on-change="onChangeImage"
            list-type="picture"
            drag
          >
            <i class="el-icon-upload" />
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
          </el-upload>
          <el-input v-model="singleImageDialogObj.form.fileList.length" style="display: none;" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button
          v-preventReClick
          type="primary"
          @click="onOperate('singleImageSubmit')"
        >确定</el-button>
        <el-button @click="singleImageDialogObj.visible = false">取消</el-button>
      </div>
    </el-dialog>
    <!-- 图片裁剪 -->
    <el-dialog
      title="图片裁剪"
      :visible.sync="dialogVisible"
      append-to-body
    >
      <div class="cropper-content">
        <div class="cropper" style="text-align:center">
          <vueCropper
            ref="cropper"
            :img="option.img"
            :output-size="option.size"
            :output-type="option.outputType"
            :info="true"
            :full="option.full"
            :can-move="option.canMove"
            :can-move-box="option.canMoveBox"
            :original="option.original"
            :auto-crop="option.autoCrop"
            :fixed="option.fixed"
            :fixed-number="option.fixedNumber"
            :center-box="option.centerBox"
            :info-true="option.infoTrue"
            :fixed-box="option.fixedBox"
          />
        </div>
      </div>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="finish">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import sysManageApi from '@/api/sysManageApi'
import checkPermission from '@/utils/permission'
import { obj2form, downloadFile } from '@/utils'
import RouteTitle from '@/components/RouteTitle'

const defaultSearchForm = {
  fileName: ''
}

const defaultSingleFileForm = {
  access: '1',
  fileList: []
}

const defaultSingleImageForm = {
  access: '1',
  fileList: []
}
export default {
  components: {
    RouteTitle
  },
  data() {
    const validateIds = (rule, value, callback) => {
      if (value === 0) {
        callback(new Error('不能为空'))
      } else {
        callback()
      }
    }
    return {
      addPermission: checkPermission(['sysUser_add']),
      deleteSinglePermission: checkPermission(['sysUser_delete']),
      deleteMultiplePermission: checkPermission(['sysUser_batchDelete']),
      updatePermission: checkPermission(['sysUser_modify']),
      resetPermission: checkPermission(['sysUser_resetPwd']),
      roleEnums: [],
      mainObj: {
        list: [],
        pageSize: 10,
        pageSizes: [10, 30, 50],
        currentPage: 1,
        total: 0,
        searchForm: { ...defaultSearchForm }
      },
      // 单文件上传
      singleFileDialogObj: {
        visible: false,
        title: '',
        form: Object.assign({}, defaultSingleFileForm),
        rules: {
          access: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          fileList: [
            { required: true, validator: validateIds, trigger: 'change' }
          ]
        }
      },
      // 单图片上传
      singleImageDialogObj: {
        visible: false,
        title: '',
        form: Object.assign({}, defaultSingleImageForm),
        rules: {
          access: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          fileList: [
            { required: true, validator: validateIds, trigger: 'change' }
          ]
        }
      },
      dialogVisible: false,
      picsList: [], // 页面显示的数组
      fileinfo: null,
      option: {
        img: '', // 裁剪图片的地址
        info: true, // 裁剪框的大小信息
        outputSize: 0.8, // 裁剪生成图片的质量
        outputType: 'jpeg', // 裁剪生成图片的格式
        canScale: true, // 图片是否允许滚轮缩放
        autoCrop: true, // 是否默认生成截图框
        // autoCropWidth: 300, // 默认生成截图框宽度
        // autoCropHeight: 200, // 默认生成截图框高度
        fixedBox: false, // 固定截图框大小 不允许改变
        fixed: true, // 是否开启截图框宽高固定比例
        fixedNumber: [16, 9], // 截图框的宽高比例
        full: true, // 是否输出原图比例的截图
        canMoveBox: true, // 截图框能否拖动
        original: false, // 上传图片按照原始比例渲染
        centerBox: true, // 截图框是否被限制在图片里面
        infoTrue: true // true 为展示真实输出图片宽高 false 展示看到的截图框宽高
      }
    }
  },
  created() {
    this.getValidRole()
    this.onSearch()
  },
  methods: {
    checkPermission,
    getValidRole() {
      sysManageApi.getValidRole().then((res) => {
        this.roleEnums = res.data
      })
    },
    onSearch() {
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    onReset() {
      this.mainObj.searchForm = { ...defaultSearchForm }
      this.fetchData()
    },
    handleSizeChange(val) {
      this.mainObj.pageSize = val
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.mainObj.currentPage = val
      this.fetchData()
    },
    fetchData() {
      const params = {
        pageNum: this.mainObj.currentPage,
        pageSize: this.mainObj.pageSize
      }
      sysManageApi.getFileList(params).then((res) => {
        this.mainObj.list = res.data.list
        this.mainObj.total = res.data.total
      })
    },
    onOperate(type, row) {
      if (type === 'singleFile') {
        this.singleFileDialogObj.visible = true
        this.singleFileDialogObj.title = '单文件上传'
        this.singleFileDialogObj.form = Object.assign({}, defaultSingleFileForm)
        this.$nextTick(() => {
          this.$refs.singleFileFormRef.clearValidate()
        })
      } else if (type === 'singleFileSubmit') {
        this.$refs.singleFileFormRef.validate((valid) => {
          if (valid) {
            const params = {
              // access: this.singleFileDialogObj.form.access,
              file: this.singleFileDialogObj.form.fileList[0].raw
            }
            sysManageApi.uploadSingleFile(obj2form(params)).then((res) => {
              this.$message({
                type: 'success',
                message: res.msg
              })
              this.fetchData()
              this.singleFileDialogObj.visible = false
            })
          }
        })
      } else if (type === 'singleImage') {
        this.singleImageDialogObj.visible = true
        this.singleImageDialogObj.title = '单图片上传'
        this.singleImageDialogObj.form = Object.assign({}, defaultSingleImageForm)
        this.$nextTick(() => {
          this.$refs.singleImageFormRef.clearValidate()
        })
      } else if (type === 'singleImageSubmit') {
        this.$refs.singleImageFormRef.validate((valid) => {
          if (valid) {
            const params = {
              // access: this.singleImageDialogObj.form.access,
              image: this.singleImageDialogObj.form.fileList[0].raw
            }
            sysManageApi.uploadSingleImage(obj2form(params)).then((res) => {
              this.$message({
                type: 'success',
                message: res.msg
              })
              this.fetchData()
              this.singleImageDialogObj.visible = false
            })
          }
        })
      } else if (type === 'download') {
        window.open(`${process.env.VUE_APP_BASE_API}/demo/download?id=${row.id}`)
      } else if (type === 'downloadStream') {
        this.downloadFile(row)
      } else if (type === 'delete') {
        this.$confirm('确定删除该文件吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const params = {
            id: row.id
          }
          sysManageApi.deleteFile(params).then((res) => {
            this.$message({
              type: 'success',
              message: res.msg
            })
            this.fetchData()
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
      }
    },
    uploadForeverFile(fileObj) {
      const file = fileObj.file
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const params = {
        file: file
      }
      sysManageApi.uploadForeverFile(obj2form(params)).then((res) => {
        this.$message({
          type: 'success',
          message: res.msg
        })
        this.fetchData()
      }).finally(() => {
        loading.close()
      })
    },
    onChangeFile(file, fileList) {
      if (fileList.length > 1) {
        fileList.splice(0, 1)
      }
      this.singleFileDialogObj.form.fileList = fileList
    },
    onRemoveFile(file, fileList) {
      this.singleFileDialogObj.form.fileList = []
    },
    downloadFile(fileObj) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const params = {
        id: fileObj.id
      }
      sysManageApi.downloadFileStream(params).then(res => {
        loading.close()
        downloadFile(res.data, fileObj.name)
        this.$message({
          type: 'success',
          message: '下载成功!'
        })
      }).catch(() => {
        loading.close()
        this.$message({
          type: 'error',
          message: '下载失败!'
        })
      })
    },
    onChangeImage(file, fileList) {
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.error('上传文件大小不能超过 5MB!')
        return false
      }
      this.fileinfo = file
      // 上传成功后将图片地址赋值给裁剪框显示图片
      this.$nextTick(() => {
        this.option.img = window.URL.createObjectURL(file.raw)
        this.dialogVisible = true
      })
    },
    // 点击裁剪，这一步是可以拿到处理后的地址
    finish() {
      this.$refs.cropper.getCropBlob((data) => {
        const fileObj = {
          ...this.fileinfo,
          raw: new File(
            [data], // 将Blob类型转化成File类型
            this.fileinfo.name, // 设置File类型的文件名称
            { type: this.fileinfo.raw.type } // 设置File类型的文件类型
          ),
          url: window.URL.createObjectURL(data)
        }
        this.singleImageDialogObj.form.fileList = [fileObj]
        this.dialogVisible = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.cropper-content {
  .cropper {
    width: auto;
    height: 300px;
  }
}
</style>
