import request from '@/utils/request'

export function actSearch(data) {
  return request({
    url: '/act-manage/homePage/search',
    method: 'post',
    data
  })
}

// 配置新增或者编辑
export function homePageAddOrEdit(data, url) {
  return request({
    url: `/act-manage/homePage/${url}`,
    method: 'post',
    data
  })
}

// 坑位 banner 生效 失效接口
export function effective(data) {
  return request({
    url: `/act-manage/homePage/effective`,
    method: 'post',
    data
  })
}

// 获取首页Banner列表
export function getHomePageList(data) {
  return request({
    url: '/act-manage/homePage/getHomePageList',
    method: 'post',
    data
  })
}

// 启用或停用Banner
export function modifyStatus(data) {
  return request({
    url: '/act-manage/homePage/modifyStatus',
    method: 'get',
    params: data
  })
}

// 添加首页Banner信息
export function addHomePage(data) {
  return request({
    url: '/act-manage/homePage/addHomePage',
    method: 'post',
    data
  })
}

// 修改首页Banner信息
export function modifyHomePage(data) {
  return request({
    url: '/act-manage/homePage/modifyHomePage',
    method: 'post',
    data
  })
}

