import request from '@/utils/request'

// 获取角色列表
export function getRoleList(data) {
  return request({
    url: '/sys/role/search',
    method: 'post',
    data
  })
}

// 创建角色
export function createRole(data) {
  return request({
    url: '/sys/role/add',
    method: 'post',
    data
  })
}

// 更新角色
export function updateRole(data) {
  return request({
    url: '/sys/role/modify',
    method: 'post',
    data
  })
}

// 删除角色
export function deleteRole(roleId) {
  console.log(roleId, 'roleId1111')
  return request({
    url: '/sys/role/delete',
    method: 'post',
    params: { roleId }

  })
}

// 获取权限树
export function getPermissionTree() {
  return request({
    url: '/sys/role/getPermTree',
    method: 'get'
  })
}

// 检查角色关联
export function checkRoleAssociation(roleId) {
  return request({
    url: `/system/role/${roleId}/association`,
    method: 'get'
  })
}
