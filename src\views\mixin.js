import { getTableList } from '@/api/common'
export default {
  data() {
    return {
      tableLoading: false,
      queryData: {}
    }
  },
  created() { },
  mounted() {
    this.getList()
  },
  mixins: [],
  methods: {
    saveSuccess() {
      this.dialogVisible = false
      this.getList()
    },
    /**
		 * 查询条件更改
		 * @param {Object} queryData
		 */
    handleQueryDataChanged: function(queryData) {
      // console.log('demo queryChanged...', queryData);
      this.queryData = queryData
    },

    /**
		 * 条件查询时触发该事件
		 */
    handleSearch: function(query) {
      // console.log('demo search...', query);
      const _this = this
      _this.queryData = query

      // 请求页数 修改为1
      _this.tableData.pageNum = 1
      // 获取列表，分页数据
      _this.getList()
    },
    // 获取列表
    getList: function() {
      const _this = this
      _this.tableLoading = true
      // 参数赋值，重写部分参数+拼接分页数据
      const params = {
        ...this.queryData,
        pageNum: this.tableData.pageNum,
        pageSize: this.tableData.pageSize,
        delFlag: '0' // 是否删除 - 必填参数，0表示未删除
      }
      // 社区首页
      if (_this.homePageType) {
        params.type = _this.homePageType
      }
      // 活动审核
      if (_this.auditPage) {
        params.listType = 'audit'
      }
      // 时间等特殊参数处理
      if (params.createTimeArr) {
        params.createTimeStart = params.createTimeArr[0]
        params.createTimeEnd = params.createTimeArr[1]
        delete params.createTimeArr
      }
      if (params.userDateArr) {
        params.registerTimeBegin = params.userDateArr[0]
        params.registerTimeEnd = params.userDateArr[1]
        delete params.userDateArr
      }
      if (params.logOffDateArr) {
        params.cancelStartTime = params.logOffDateArr[0]
        params.cancelEndTime = params.logOffDateArr[1]
        delete params.logOffDateArr
      }
      // 图片机审
      if (params.exampleDateArr) {
        params.auditStartTime = params.exampleDateArr[0]
        params.auditEndTime = params.exampleDateArr[1]
        delete params.exampleDateArr
      }
      // 报名列表
      if (params.signUpDateArr) {
        params.signUpStartTime = params.signUpDateArr[0]
        params.signUpEndTime = params.signUpDateArr[1]
        delete params.signUpDateArr
      }
      getTableList(_this.queryUrl, params).then(res => {
        res = res.data
        this.tableData.list = res.list || []
        this.tableData.pageTotal = res.total
      }).finally(() => {
        _this.tableLoading = false
      })
    },

    /**
		 * 分页条页码/每页条数改变
		 * @param {Object} obj { page: 当前页码, limit: 每页条数 }
		 */
    handlePageChange: function(obj) {
      const _this = this
      // TODO 重写页码和size
      this.tableData.pageSize = obj.limit
      this.tableData.pageNum = obj.page
      // 获取列表
      _this.getList()
    },
    handleClose() {
      this.dialogVisible = false
    }
  }
}
