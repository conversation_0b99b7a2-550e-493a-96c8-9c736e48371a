import request from '@/utils/request'

// 获取公钥
export function getPublicKey() {
  return request({
    url: '/common/publicKey',
    method: 'get'
  })
}

// 登录
export function login(data) {
  return request({
    url: '/login',
    method: 'post',
    data
  })
}

// 登出
export function logout() {
  return request({
    url: '/logout',
    method: 'post'
  })
}

// 获取用户信息
export function getInfo() {
  return request({
    url: '/get/curr/user',
    method: 'get'
  })
}
// 获取菜单树
export function getMenu() {
  return request({
    url: '/sys/menu/getMenuTree',
    method: 'get'
  })
}

// 图形验证码
export function getImgCode() {
  return request({
    url: '/verification/sendImgValue',
    method: 'get',
    responseType: 'blob'

  })
}
export default {
  getPublicKey,
  login,
  logout,
  getInfo,
  getImgCode
}
