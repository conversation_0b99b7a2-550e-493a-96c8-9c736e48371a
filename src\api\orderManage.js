import request from '@/utils/request'

// 获取活动订单列表
export function getRegisterInfoList(data) {
  return request({
    url: '/act-manage/registerInfo/getRegisterInfoList',
    method: 'post',
    data
  })
}

// 订单签到
export function signOrder(id) {
  return request({
    url: '/act-manage/registerInfo/sign',
    method: 'get',
    params: { id }
  })
}

// 获取订单详情
export function getOrderDetail(id) {
  return request({
    url: '/act-manage/registerInfo/detail',
    method: 'get',
    params: { id }
  })
}

// 导出订单列表
export function exportOrderList(data) {
  return request({
    url: '/act-manage/registerInfo/orderExcel',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 获取活动统计报表
export function getActivityReport(data) {
  return request({
    url: '/act-manage/activity/getActivityReport',
    method: 'post',
    data
  })
}

// 导出活动统计报表
export function exportActivityReport(data) {
  return request({
    url: '/act-manage/activity/exportActivityReport',
    method: 'post',
    data,
    responseType: 'blob'
  })
}
