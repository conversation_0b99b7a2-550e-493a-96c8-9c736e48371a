import Vue from 'vue'
// // 防止按钮重复点击
// Vue.directive('preventReClick', {
//   bind(el, binding, vnode, oldVnode) {
//     el.addEventListener('click', () => {
//       if (!el.disabled) {
//         el.disabled = true
//         setTimeout(() => {
//           el.disabled = false
//         }, binding.value || 3000)
//       }
//     })
//   }
// })
const preventReClick = Vue.directive('preventReClick', {
  inserted(el, binding) {
    el.addEventListener('click', () => {
      // console.log(11111)
      // console.log(el)
      if (!el.disabled) {
        el.disabled = true
        el.style.opacity = '0.5'
        el.style.cursor = 'not-allowed'
        setTimeout(() => {
          el.disabled = false
          el.style.opacity = '1'
          el.style.cursor = ''
        }, binding.value || 1500)
      }
    })
  }
})
export default preventReClick
