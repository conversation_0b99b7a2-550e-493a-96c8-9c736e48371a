<template>
  <div>
    <RouteTitle>
      <div class="header-tab">
        <span
          class="pointer"
          :class="{ active: homePageType == 1 }"
          @click="
            homePageType = 1;
            getList();
          "
        >头图Banner</span>
        <span class="tab-line">|</span>
        <span
          class="pointer"
          :class="{ active: homePageType == 2 }"
          @click="
            homePageType = 2;
            getList();
          "
        >瀑布流坑位</span>
        <span class="tab-line">|</span>
        <span
          class="pointer"
          :class="{ active: homePageType == 3 }"
          @click="
            homePageType = 3;
            getList();
          "
        >弹窗贴片</span>
      </div>
    </RouteTitle>
    <!-- 条件查询 -->
    <div class="main-body">
      <div class="main-body-top">
        <!-- 页面条件搜索 -->
        <dynamic-query
          :data="queryConfig"
          @queryChanged="handleQueryDataChanged"
          @search="handleSearch"
        >
          <template #addBtn>
            <el-button
              type="primary"
              icon="el-icon-plus"
              @click="handleAddOrEdit('add')"
            >新增配置</el-button>
          </template>
        </dynamic-query>
        <!-- 页面列表数据 + 分页条 -->
        <dynamic-table
          :config="tableConfig"
          :data="tableData"
          :is-loading.sync="tableLoading"
          @pagination="handlePageChange"
        >
          <!-- S 处理表格自定义插槽 -->
          <template #contentTypeText="{ rowData, index }">
            {{ contentTypeMap[rowData.contentType] }}
          </template>
          <template #statusText="{ rowData, index }">
            {{ statusMap[rowData.status] }} </template><template #upShelfStatusText="{ rowData, index }">
            {{ upShelfStatusMap[rowData.upShelfStatus] }}
          </template>
          <template v-slot:operate="{ rowData, index }">
            <el-button
              type="text"
              @click.stop="handleAddOrEdit('edit', rowData)"
            >编辑</el-button>
            <el-button
              v-if="rowData.status == 1"
              type="text"
              @click.stop="handleChangeStatus(rowData.id, 0)"
            >置为失效</el-button>
            <el-button
              v-if="rowData.status == 0"
              type="text"
              @click.stop="handleChangeStatus(rowData.id, 1)"
            >置为生效</el-button>
          </template>
        </dynamic-table>
      </div>
    </div>
    <!-- 配置dialog -->
    <DialogForm
      v-if="dialogVisible"
      :dialog-visible="dialogVisible"
      :content-type-map="contentTypeMap"
      :config="dialogConfig"
      :home-page-type="homePageType"
      @saveSuccess="saveSuccess"
      @handleClose="handleClose"
    />
  </div>
</template>

<script>
import RouteTitle from '@/components/RouteTitle'
import DynamicQuery from '@/components/dynamic/Query.vue'
import DynamicTable from '@/components/dynamic/Table.vue'
import DialogForm from './components/dialogForm.vue'
import { effective } from '@/api/homePage'

// const contentTypeMap = {
//   1: '活动',
//   2: '话题',
//   3: '用户名片',
//   4: '外部广告'
// }
const statusMap = {
  0: '失效',
  1: '生效中'
}
const upShelfStatusMap = {
  0: '待上架',
  1: '已上架',
  2: '已下架'
}
import mixin from '../mixin'
export default {
  components: {
    RouteTitle,
    DynamicQuery,
    DynamicTable,
    DialogForm
  },
  mixins: [mixin],
  data() {
    return {
      dialogVisible: false,
      dialogConfig: {},
      homePageType: 1,
      queryUrl: '/homePage/list',
      queryConfig: {
        queryItem: [
          {
            type: 'select',
            label: '内容类型:',
            model: 'contentType',
            optionValue: 'id',
            optionLabel: 'value',
            // optionType: "list",
            optionList: {},
            filterable: true
          },
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '内容ID:',
            model: 'contentId',
            clearable: true, // 默认 true
            size: 'small' // 默认 small
          },
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '内容名称:',
            model: 'contentTitle',
            clearable: true, // 默认 true
            size: 'small' // 默认 small
          },
          {
            type: 'select',
            label: '配置生效状态:',
            model: 'status',
            optionValue: 'id',
            optionLabel: 'value',
            optionList: statusMap
          },
          {
            type: 'select',
            label: '上架状态:',
            model: 'upShelfStatus',
            optionValue: 'id',
            optionLabel: 'value',
            optionList: upShelfStatusMap
          },
          {
            type: 'option'
          },
          {
            type: 'option',
            slotName: 'addBtn'
          }
        ]
      },
      // 列表字段
      tableConfig: {
        tableColumn: [
          {
            prop: '',
            label: '序号',
            isIndex: true,
            width: '100'
          },
          {
            prop: 'contentType', // 字段名
            label: '内容类型', // 列标记名
            slotName: 'contentTypeText'
          },
          {
            prop: 'contentId',
            label: '内容ID'
          },
          {
            prop: 'contentTitle',
            label: '内容名称'
          },
          {
            prop: 'sort',
            label: '排序'
          },
          {
            prop: 'upShelfStatus',
            label: '上架状态',
            slotName: 'upShelfStatusText'
          },
          {
            prop: 'upShelfStartTime',
            label: '上架开始时间'
          },
          {
            prop: 'upShelfEndTime',
            label: '上架结束时间'
          },
          {
            prop: 'status',
            label: '生效状态',
            slotName: 'statusText'
          },
          {
            prop: 'updateTime',
            label: '更新时间'
          },
          {
            prop: 'operator',
            label: '操作者'
          },
          {
            prop: '',
            label: '操作',
            slotName: 'operate', // 操作 - 自定义操作，和operateList二选一
            fixed: 'right',
            minWidth: 160
          }
        ]
      },

      // 列表数据
      tableData: {
        list: [],
        // 分页数据
        pageTotal: 0, // 列表总数
        pageSize: 10, // 每页条数
        pageNum: 1 // 当前页码
      },
      contentTypeMap: {
        1: '活动',
        2: '话题',
        4: '外部广告'
      },
      statusMap: statusMap,
      upShelfStatusMap: upShelfStatusMap
    }
  },
  watch: {
    homePageType: function(val) {
      if (val === 1) {
        this.contentTypeMap = {
          1: '活动',
          2: '话题',
          4: '外部广告'
        }
      } else {
        this.contentTypeMap = {
          1: '活动',
          2: '话题',
          3: '用户名片',
          4: '外部广告'
        }
      }
      this.queryConfig.queryItem[0].optionList = this.contentTypeMap
    }
  },
  created() {
    this.queryConfig.queryItem[0].optionList = this.contentTypeMap
  },
  methods: {
    handleAddOrEdit(type, rowData) {
      this.dialogVisible = true
      if (type === 'add') {
        this.dialogConfig = {
          title: '新增配置',
          url: 'add'
        }
      } else if (type === 'edit') {
        this.dialogConfig = {
          title: '编辑',
          url: 'edit',
          formData: rowData
        }
      }
    },

    handleChangeStatus(id, status) {
      const data = {
        id,
        status
      }
      effective(data).then(res => {
        this.$message.success(res.msg)
        this.getList()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.header-tab {
  display: inline-block;
  margin-left: 20px;
  font-weight: normal;
  font-size: 16px;
  .active {
    color: #1890ff;
    padding-bottom: 5px;
    border-bottom: 3px solid #1890ff;
  }
  .tab-line {
    margin: 0 10px;
  }
}
</style>
