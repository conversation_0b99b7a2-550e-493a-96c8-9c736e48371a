<template>
  <div>
    <RouteTitle />
    <div class="main-body">
      <div class="main-body-bottom">
        <div class="body-top-form">
          <el-form
            ref="searchForm"
            :model="queryFrom"
            label-width="75px"
            class="left"
            label-position="left"
            inline
          >
            <el-form-item label="奖品名称">
              <el-input v-model.trim="queryFrom.awardName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="关联话题">
              <el-select v-model="queryFrom.channelUid" filterable placeholder="请选择" clearable>
                <el-option
                  v-for="item in topicList"
                  :key="item.id"
                  :label="item.topicName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="search">查询</el-button>
              <el-button @click="reset">重置</el-button>
            </el-form-item>
          </el-form>
          <div class="right">
            <div class="main-body-bottom-btn-left">
              <el-button type="primary" @click="onOperate('add')">新增</el-button>
            </div>
          </div>
        </div>
        <div class="table">
          <el-table
            ref="tableRef"
            :data="tableData"
            fit
            border
            highlight-current-row
            :header-cell-style="{ background: '#F9F9F9' }"
          >
            <el-table-column prop="id" label="奖品编号" />
            <el-table-column prop="awardName" label="奖品名称" />
            <el-table-column prop="topicName" label="关联话题">
              <template slot-scope="scope">
                {{ scope.row.bindStatus === 'UNBIND' ? "-/空" : scope.row.topicName }}
              </template>
            </el-table-column>
            <el-table-column label="库存比例">
              <template slot-scope="scope">
                {{ scope.row.awardStock }}/{{ scope.row.awardTotalStock }}
              </template>
            </el-table-column>
            <el-table-column prop="createdAt" label="创建时间">
              <template slot-scope="scope">
                {{ scope.row.createdAt|timeFomt }}
              </template>
            </el-table-column>
            <el-table-column prop="createUser" label="创建人" />
            <el-table-column label="操作" fixed="right" width="180">
              <template slot-scope="scope">
                <el-button
                  v-preventReClick
                  type="text"
                  @click="onOperate('update', scope.row)"
                >编辑</el-button>
                <el-button v-preventReClick type="text" @click="onOperate('see', scope.row)">查看</el-button>
                <el-button type="text" @click="handdelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination">
          <Pagination
            :total="Number(total)"
            :page.sync="queryFrom.pageNo"
            :limit.sync="queryFrom.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
    </div>
    <el-dialog :title="dialogTitle" :visible.sync="visible" destroy-on-close width="600px" @close="visible = false">
      <el-form
        ref="addFrom"
        :model="rowFrom"
        :rules="rules"
        label-width="100px"
        label-position="left"
        class="full-width"
      >
        <el-form-item label="奖品名称：" prop="awardName">
          <el-input v-if="type !== 'see'" v-model="rowFrom.awardName" maxlength="20" placeholder="请输入" clearable />
          <span v-else>{{ rowFrom.awardName }}</span>
        </el-form-item>
        <template v-if="type !== 'update'">
          <el-form-item label="奖品形式：" prop="awardType">
            <el-select v-if="type !== 'see'" v-model="rowFrom.awardType" clearable @change="changeAwardType">
              <el-option
                v-for="item in awardTypelist"
                :key="item.awardType"
                :label="item.desc"
                :value="item.awardType"
              />
            </el-select>
            <span v-else>{{ Number(rowFrom.awardType) === 20 ? '电商券' : Number(rowFrom.awardType) ===30 ? '好物卡' : '实物奖品' }}</span>
          </el-form-item>
          <el-form-item v-if="Number(rowFrom.awardType) === 20" label="券码：" prop="awardBatchCode">
            <el-input
              v-if="type !== 'see'"
              v-model="rowFrom.awardBatchCode"
              maxlength="200"
              placeholder="请输入"
              clearable
            />
            <span v-else>{{ rowFrom.awardBatchCode }}</span>
          </el-form-item>
          <el-form-item label="奖品数量：" prop="awardStock">
            <el-input
              v-if="type !== 'see'"
              v-model="rowFrom.awardStock"
              maxlength="5"
              placeholder="请输入"
              clearable
            />
            <span v-else>{{ rowFrom.awardStock }}</span>
          </el-form-item>
        </template>

        <el-form-item v-if="type === 'see'" label="奖品关联：">
          <span>{{ rowFrom.bindStatus === 'UNBIND' ? "-/空" : rowFrom.topicName }}</span>
        </el-form-item>
        <el-form-item v-if="type === 'see'" label="创建时间：">
          <span>{{ rowFrom.createdAt|timeFomt }}</span>
        </el-form-item>
        <el-form-item v-if="type === 'see'" label="创建人：">
          <span>{{ rowFrom.createUser }}</span>
        </el-form-item>
      </el-form>
      <div v-if="type !== 'see'" slot="footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button v-preventReClick type="primary" :disabled="isloading" :loading="isloading" @click="save">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import sysManageApi from '@/api/sysManageApi'
// import checkPermission from '@/utils/permission'
import RouteTitle from '@/components/RouteTitle'
import Pagination from '@/components/Pagination'
import { searchPrize, prizeAdd, prizeAwardType, prizeEdit, prizeDelete, prizedTopicType } from '@/api/prizeManager'
const defaultFrom = {
  awardName: '',
  awardType: '',
  awardBatchCode: '',
  awardStock: ''
}
export default {
  components: {
    RouteTitle,
    Pagination
  },
  filters: {
    timeFomt(val) {
      if (val) {
        return val.replace(/T/g, ' ').replace(/.[\d]{3}Z/, ' ')
      }
    }
  },
  data() {
    return {
      isloading: false,
      awardTypelist: [],
      topicList: [],
      queryFrom: {
        bindAwardLabel: true,
        awardName: '',
        channelUid: '',
        pageNo: 1,
        pageSize: 10
      },
      tableData: [],
      total: 0,
      visible: false,
      rowFrom: Object.assign({}, defaultFrom),
      rules: {
        awardName: [{ required: true, message: '请输入奖品名称最多20个字', trigger: 'change' }],
        awardType: [{ required: true, message: '请选择奖品形式', trigger: 'change' }],
        awardBatchCode: [{ required: true, message: '请填写券码', trigger: 'change' }],
        awardStock: [
          { required: true, pattern: /^[1-9]\d{0,4}$/, message: '请输入正整数，最多五位数', trigger: 'change' }
        ]
      },
      type: '',
      dialogTitle: ''
    }
  },
  created() {
    this.getList()
    prizedTopicType({ topicType: 2 }).then(res => {
      this.topicList = res.data
    })
    prizeAwardType().then(res => {
      this.awardTypelist = res.data
    })
  },
  methods: {
    search() {
      this.queryFrom.pageNo = 1
      this.queryFrom.pageSize = 10
      this.getList()
    },
    getList() {
      searchPrize(this.queryFrom).then(res => {
        this.tableData = res.data.data
        this.total = res.data.total
      })
    },
    changeAwardType() {
      this.rowFrom.awardBatchCode = ''
    },
    reset() {
      this.queryFrom = {
        bindAwardLabel: true,
        awardName: '',
        channelUid: '',
        pageNo: 1,
        pageSize: 10
      }
      this.getList()
    },
    onOperate(type, row) {
      this.type = type
      this.visible = true
      this.$nextTick(() => {
        this.$refs.addFrom.clearValidate()
      })
      if (row && row.id) {
        this.rowFrom = Object.assign({}, row)
        this.rowFrom.awardType = row.awardType.toString()
      } else {
        this.rowFrom = Object.assign({}, defaultFrom)
      }
      if (type === 'add') {
        this.dialogTitle = '新增'
      } else if (type === 'update') {
        this.dialogTitle = '编辑'
      } else if (type === 'see') {
        this.dialogTitle = '查看'
      }
    },

    save() {
      this.$refs.addFrom.validate((valid) => {
        if (valid) {
          this.isloading = true
          if (this.type === 'add') {
            prizeAdd(this.rowFrom).then(res => {
              this.queryFrom.pageNo = 1
              this.getList()
              this.visible = false
              this.isloading = false
            }).catch(() => {
              this.isloading = false
            })
          } else if (this.type === 'update') {
            prizeEdit({ awardId: this.rowFrom.id, awardName: this.rowFrom.awardName }).then(res => {
              this.getList()
              this.visible = false
              this.isloading = false
            }).catch(() => {
              this.isloading = false
            })
          }
        }
      })
    },

    handdelete(row) {
      if (row.bindStatus === 'BIND') {
        this.$alert('奖品已关联活动/话题，请先取消关联后再删除奖品', {
          confirmButtonText: '确定',
          callback: action => {

          }
        })
        return false
      }
      this.$confirm('是否删除该奖品?', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning'
      })
        .then(() => {
          prizeDelete({ awardId: row.id }).then(res => {
            this.getList()
          })
        })
        .catch(() => { })
    }
  }
}
</script>

<style lang="scss" scoped>
.main-body-bottom {
    box-sizing: border-box;
    padding: 20px;
}

.right {
    float: right;
    padding: 10px;
}

.left {
    float: left;
}
</style>
