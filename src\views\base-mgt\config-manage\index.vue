<template>
  <div>

    <RouteTitle />
    <div class="main-body">
      <div class="main-body-bottom">
        <div class="body-top-form">
          <el-form
            ref="searchForm"
            class="left"
            :model="mainObj.searchForm"
            label-width="75px"
            label-position="left"
            inline
          >
            <el-form-item label="参数类型">
              <el-select
                v-model="mainObj.searchForm.configType"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in configTypeObj"
                  :key="item.code"
                  :label="item.text"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="参数代码">
              <el-input
                v-model.trim="mainObj.searchForm.configCode"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
            <el-form-item label="参数名称" clearable>
              <el-input
                v-model.trim="mainObj.searchForm.configName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="onSearch"
              >查询</el-button>
              <el-button @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
          <div v-if="addPermission" class="right">
            <div class="main-body-bottom-btn-left">
              <el-button
                v-if="addPermission"
                type="primary"
                @click="onOperate('add')"
              >新增</el-button>
            </div>
          </div>
        </div>
        <div class="table">
          <el-table
            ref="tableRef"
            :data="mainObj.list"
            fit
            border
            highlight-current-row
            :header-cell-style="{'background': '#F9F9F9'}"
          >
            <el-table-column
              prop="configType"
              label="参数类型"
            />
            <el-table-column
              prop="configCode"
              label="参数代码"
            />
            <el-table-column
              prop="configName"
              label="参数名称"
            />
            <el-table-column
              prop="configValue"
              label="参数值"
            />
            <!-- <el-table-column
              show-overflow-tooltip
              prop="configValue2"
              label="参数值2"
            /> -->
            <el-table-column
              prop="configRemark"
              label="备注信息"
            />
            <el-table-column
              prop="updateTime"
              label="更新时间"
            />
            <el-table-column label="操作" width="180">
              <template slot-scope="scope">
                <el-button
                  v-if="updatePermission && scope.row.configType.code === '1'"
                  v-preventReClick
                  type="text"
                  @click="onOperate('update', scope.row)"
                >编辑</el-button>
                <!-- <el-button
                  v-if="scope.row.configType.code !== '0' && scope.row.configType.code !== '1'"
                  type="text"
                  @click="onOperate('delete', scope.row)"
                >删除</el-button> -->
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination
            :current-page="mainObj.currentPage"
            :page-size="mainObj.pageSize"
            :page-sizes="mainObj.pagesizes"
            :total="mainObj.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <el-dialog
      :title="configDialogObj.title"
      :visible.sync="configDialogObj.visible"
      destroy-on-close
      width="800px"
      @close="configDialogObj.visible = false"
    >
      <el-form
        ref="configFormRef"
        :model="configDialogObj.form"
        :rules="configDialogObj.type === 'view' ? null : configDialogObj.rules"
        :disabled="configDialogObj.type === 'view'"
        label-width="80px"
        label-position="left"
        class="full-width"
      >
        <el-form-item label="参数类型" prop="configType">
          <el-select
            v-model="configDialogObj.form.configType"
            placeholder="请选择"
            :disabled="configDialogObj.type !=='add'"
          >
            <el-option
              v-for="item in configTypeAddObj"
              :key="item.code"
              :label="item.text"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="参数代码" prop="configCode">
          <el-input
            v-model="configDialogObj.form.configCode"
            maxlength="50"
            placeholder="请输入"
            clearable
            :disabled="configDialogObj.type !=='add'"
          />
        </el-form-item>
        <el-form-item label="参数名称" prop="configName">
          <el-input
            v-model="configDialogObj.form.configName"
            maxlength="50"
            placeholder="请输入"
            clearable
          />
        </el-form-item>
        <el-form-item label="参数值" prop="configValue">
          <el-input
            v-model="configDialogObj.form.configValue"
            maxlength="50"
            placeholder="请输入"
            clearable
          />
        </el-form-item>
        <el-form-item label="备注信息" prop="configRemark">
          <el-input
            v-model="configDialogObj.form.configRemark"
            maxlength="50"
            placeholder="请输入"
            clearable
          />
        </el-form-item>
      </el-form>
      <div v-if="configDialogObj.type !== 'view'" slot="footer">
        <el-button @click="configDialogObj.visible = false">取消</el-button>
        <el-button
          v-preventReClick
          type="primary"
          :loading="isloading"
          @click="onOperate('submit')"
        >确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import sysManageApi from '@/api/sysManageApi'
import checkPermission from '@/utils/permission'
import { obj2form } from '@/utils'
// import { Row } from 'element-ui'
import RouteTitle from '@/components/RouteTitle'

const defaultSearchForm = {
  configCode: '',
  configName: '',
  configType: ''
}

const defaultConfigForm = {
  id: '',
  configCode: '',
  configName: '',
  configType: '',
  configRemark: '',
  configValue: ''
}
export default {
  components: {
    RouteTitle
  },
  data() {
    return {
      isloading: false,
      addPermission: checkPermission(['glConfig_add']),
      deletePermission: checkPermission(['glConfig_delete']),
      updatePermission: checkPermission(['glConfig_modify']),
      configTypeObj: [],
      configTypeAddObj: [],
      mainObj: {
        list: [],
        pageSize: 10,
        pagesizes: [10, 30, 50],
        currentPage: 1,
        total: 0,
        searchForm: { ...defaultSearchForm }
      },
      configDialogObj: {
        visible: false,
        tile: '',
        type: '',
        form: Object.assign({}, defaultConfigForm),
        rules: {
          configCode: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          configName: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          configValue: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          configType: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ]
          // configRemark: [
          //   { required: true, message: '不能为空', trigger: 'blur' }
          // ]
        }
      },
      treeData: [],
      treeDisableData: [],
      defaultProps: {
        children: 'subPermDetailDtoList',
        label: 'permName',
        disabled: 'disabled'
      }
    }
  },
  created() {
    this.getConfigTypeEnum()
    this.getConfigTypeAddEnum()
    this.onSearch()
  },
  methods: {
    checkPermission,
    getConfigTypeEnum() {
      sysManageApi.getTypeEnum({ enumName: 'ConfigTypeEnum' }).then(res => {
        this.configTypeObj = res.data
      })
    },
    getConfigTypeAddEnum() {
      sysManageApi.getTypeEnum({ enumName: 'ConfigTypeAddEnum' }).then(res => {
        this.configTypeAddObj = res.data
      })
    },
    onSearch() {
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    onReset() {
      this.mainObj.searchForm = { ...defaultSearchForm }
      this.fetchData()
    },
    handleSizeChange(val) {
      this.mainObj.pageSize = val
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.mainObj.currentPage = val
      this.fetchData()
    },
    fetchData() {
      const params = {
        configCode: this.mainObj.searchForm.configCode,
        configType: this.mainObj.searchForm.configType,
        configName: this.mainObj.searchForm.configName,
        pageNum: this.mainObj.currentPage,
        pageSize: this.mainObj.pageSize
      }
      sysManageApi.getConfigList(params).then((res) => {
        this.mainObj.list = res.data.list
        this.mainObj.total = res.data.total
      })
    },
    onOperate(type, row) {
      if (type === 'add') {
        this.configDialogObj.visible = true
        this.configDialogObj.type = type
        this.configDialogObj.title = '新增'
        this.configDialogObj.form = Object.assign({}, defaultConfigForm)
        // console.log(this.configDialogObj.roleType !== '3' && this.configDialogObj.roleType.length>0 )
        this.$nextTick(() => {
          // this.$refs.treeRef.setCheckedKeys([])
          this.$refs.configFormRef.clearValidate()
        })
      } else if (type === 'update') {
        this.configDialogObj.visible = true
        this.configDialogObj.type = type
        this.configDialogObj.title = '修改'
        this.configDialogObj.form = {
          configCode: row.configCode,
          configType: row.configType.text,
          configName: row.configName,
          configValue: row.configValue,
          configRemark: row.configRemark,
          id: row.id
        }
      } else if (type === 'view') {
        this.configDialogObj.visible = true
        this.configDialogObj.type = type
        this.configDialogObj.title = '查看'
      } else if (type === 'submit') {
        this.$refs.configFormRef.validate((valid) => {
          if (valid) {
            this.isloading = true
            const params = {
              configCode: this.configDialogObj.form.configCode,
              configType: this.configDialogObj.form.configType,
              configName: this.configDialogObj.form.configName,
              configRemark: this.configDialogObj.form.configRemark,
              configValue: this.configDialogObj.form.configValue
            }
            if (this.configDialogObj.type === 'update') {
              params.id = this.configDialogObj.form.id
              sysManageApi.modifyConfig(params).then(e => {
                this.fetchData()
                this.configDialogObj.visible = false
                this.isloading = false
              })
            } else {
              sysManageApi.addConfig(params).then(e => {
                this.fetchData()
                this.configDialogObj.visible = false
                this.isloading = false
              })
            }
          }
        })
      } else if (type === 'delete') {
        this.$confirm('确定删除该参数吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const params = {
            configId: row.id
          }
          sysManageApi.deleteConfig(obj2form(params)).then((res) => {
            this.$message({
              type: 'success',
              message: res.msg
            })
            this.fetchData()
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.main-body-bottom {
  box-sizing: border-box;
  padding: 20px;
}

.right {
  float: right;
}

.left {
  float: left;
}

</style>

