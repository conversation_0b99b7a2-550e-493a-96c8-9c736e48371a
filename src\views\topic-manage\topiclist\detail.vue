<template>
  <div>
    <el-dialog
      :title="
        type === 'view' ? '话题详情' : type === 'edit' ? '编辑话题' : '新增话题'
      "
      :visible.sync="dialogVisible"
      width="820px"
      top="4vh"
      :before-close="handleClose"
    >
      <el-dialog
        title="选择商品"
        :visible.sync="goodsList.visible"
        width="820px"
        top="4vh"
        append-to-body
        :before-close="handleCloseGoodsList"
        @close="closeGoodsList(0)"
      >
        <!-- <div class="main-body">
        <div class="main-body-bottom">
          <div class="body-top-form"> -->
        <el-form
          ref="searchForm"
          class="left"
          :model="goodsList.searchForm"
          label-position="left"
          inline
        >
          <el-form-item label="商品ID：">
            <el-input
              v-model="goodsList.searchForm.goodsId"
              placeholder="请输入"
              maxlength="50"
            />
          </el-form-item>
          <el-form-item label="商品名称：">
            <el-input
              v-model="goodsList.searchForm.goodsName"
              placeholder="请输入"
              maxlength="50"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchGoodsList">查询</el-button>
            <el-button @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="table">
          <el-table
            ref="goodChooseTableRef"
            max-height="500px"
            :data="goodsList.list"
            fit
            border
            highlight-current-row
            size="small"
            :header-cell-style="{ background: '#F9F9F9' }"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              type="selection"
              width="55"
              label-class-name="DisabledSelection"
            />
            <el-table-column prop="goodsId" label="商品ID" />
            <el-table-column prop="goodsName" label="商品名称" />
            <!-- <el-table-column prop="goodsCostPrice" label="下单价(单位分)" />  -->
            <el-table-column prop="goodsCostPrice" label="售价(元)" />
          </el-table>
        </div>
        <!-- </div>
        </div>
      </div> -->

        <div class="pagination">
          <el-pagination
            :current-page="goodsList.currentPage"
            :page-size="goodsList.pageSize"
            :page-sizes="goodsList.pageSizes"
            :total="goodsList.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="closeGoodsList(0)">取 消</el-button>
          <el-button type="primary" @click="closeGoodsList(1)">确 定</el-button>
        </span>
      </el-dialog>
      <div v-loading="loading" class="detail">
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          label-width="120px"
          class="demo-ruleForm"
          label-position="right"
          size="small"
          :disabled="type === 'view'"
        >
          <el-form-item label="话题名称：" prop="topicName">
            <el-input
              v-model="ruleForm.topicName"
              placeholder="请输入话题名称"
              maxlength="20"
            />
          </el-form-item>
          <el-form-item label="是否置顶：" prop="isTop">
            <el-radio-group v-model="ruleForm.isTop">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="话题分类：" prop="classificationType">
            <el-select
              v-model="ruleForm.classificationType"
              placeholder="请选择话题分类"
              style="width:240px"
            >
              <el-option
                v-for="item in classificationTypeObj"
                :key="item.dictCode"
                :label="item.dictName"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="话题属性：" prop="topicType">
            <el-select
              v-model="ruleForm.topicType"
              placeholder="请选择话题类型"
              style="width:240px"
            >
              <el-option
                v-for="item in topicTypeObj"
                :key="item.code"
                :label="item.text"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="话题开展周期：" prop="cycleDate">
            <el-date-picker
              v-model="ruleForm.cycleDate"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </el-form-item>

          <el-form-item
            v-if="ruleForm.topicType === '2'"
            label="排行结束时间："
            prop="rankEndTime"
          >
            <el-date-picker
              v-model="ruleForm.rankEndTime"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              placeholder="选择日期"
              :picker-options="setDateRange"
            />
          </el-form-item>

          <el-form-item label="话题描述：" prop="content">
            <el-input
              v-model="ruleForm.content"
              type="textarea"
              resize="none"
              rows="5"
              maxlength="100"
              show-word-limit
              placeholder="请输入话题描述"
            />
          </el-form-item>
          <el-form-item label="话题封面：">
            <div style="display: flex;">
              <template>
                <ImgUpload
                  v-model="imageUrl"
                  style="margin-right: 30px;"
                  :file-size="5"
                  :proportion="1"
                  :tips="''"
                  :img-txt="'img1(1:1)'"
                  @changeId="changeImg1Id"
                  @delImg="changeImg1Id"
                >
                  <!-- <template #tips>
                支持：jpg、jpeg、bmp、png的格式。附件大小不超过5M。
              </template> -->
                </ImgUpload>
                <el-input
                  v-show="false"
                  v-model="imageUrl"
                  placeholder="请输入"
                />
              </template>
              <template>
                <ImgUpload
                  v-model="imageUrl2"
                  :file-size="5"
                  :proportion="375/225"
                  :tips="''"
                  :img-txt="'img2(375/225)'"
                  @changeId="changeImg2Id"
                  @delImg="changeImg2Id"
                />
                <el-input
                  v-show="false"
                  v-model="imageUrl2"
                  placeholder="请输入"
                />
              </template>
            </div>
            <div class="el-upload__tip">
              支持：jpg、jpeg、bmp、png的格式。附件大小不超过5M。
            </div>
          </el-form-item>
          <el-form-item
            v-if="ruleForm.topicType === '1'"
            label="正方观点："
            prop="positiveSide"
          >
            <el-input
              v-model="ruleForm.positiveSide"
              placeholder="请输入正方观点"
              maxlength="15"
            />
          </el-form-item>
          <el-form-item
            v-if="ruleForm.topicType === '1'"
            label="反方观点："
            prop="negativeSide"
          >
            <el-input
              v-model="ruleForm.negativeSide"
              placeholder="请输入反方观点"
              maxlength="15"
            />
          </el-form-item>
          <el-form-item label="虚拟浏览量：" props="virtualViews">
            <el-input
              v-model="ruleForm.virtualViews"
              type="text"
              oninput="value=value.replace(/[^\d]/g,'')"
              placeholder="请输入虚拟浏览量"
              maxlength="10"
            />
          </el-form-item>
          <el-form-item label="话题规则描述：" prop="topicActivityRule">
            <el-input
              v-model="ruleForm.topicActivityRule"
              type="textarea"
              resize="none"
              rows="6"
              maxlength="1000"
              show-word-limit
              placeholder="请输入话题规则描述"
            />
          </el-form-item>
          <el-form-item label="商品渠道号：" props="goodsChannelNo">
            <el-input
              v-model="ruleForm.goodsChannelNo"
              type="text"
              placeholder="请输入商品渠道号"
              maxlength="50"
            />
          </el-form-item>
          <el-form-item label="关联商品：" prop="name">
            <el-button type="primary" @click="chooseGoods">选择商品</el-button>
          </el-form-item>
          <el-table :data="tableData" border style="width:100%;" size="small">
            <el-table-column prop="ipAddress" label="序号" width="50">
              <template slot-scope="scope">
                <span>{{ scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="goodsId" label="商品ID" width="60" />
            <el-table-column prop="goodsName" label="商品名称" />
            <el-table-column prop="goodsCostPrice" label="售价(元)" />
            <el-table-column fixed="right" label="操作">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  @click="handleClick(scope.row)"
                >移除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-form-item
            label="弹窗跳转设置："
            prop="hasActivity"
            style="margin-top: 20px;"
          >
            <el-radio-group v-model="ruleForm.hasActivity">
              <el-radio label="0">否</el-radio>
              <el-radio label="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
          <div v-if="ruleForm.hasActivity === '1'">
            <el-form-item label="活动标题：" prop="activityTitle">
              <el-input
                v-model="ruleForm.activityTitle"
                placeholder="请输入活动标题"
                maxlength="20"
              />
            </el-form-item>
            <el-form-item label="活动时间：" prop="activityTime">
              <el-date-picker
                v-model="ruleForm.activityTime"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                :picker-options="setDateRange"
              />
            </el-form-item>
            <el-form-item label="内容描述：" prop="activityContent">
              <el-input
                v-model="ruleForm.activityContent"
                placeholder="请输入内容描述"
                type="textarea"
                resize="none"
                rows="6"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="按钮名称：" prop="buttonName">
              <el-input
                v-model="ruleForm.buttonName"
                placeholder="请输入按钮名称"
                maxlength="20"
              />
            </el-form-item>
            <el-form-item label="跳转链接：" prop="activityUrl">
              <el-input
                v-model="ruleForm.activityUrl"
                placeholder="请输入跳转链接"
              />
            </el-form-item>
          </div>
        </el-form>
      </div>
      <span v-if="type !== 'view'" slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button
          type="primary"
          :loading="isloading"
          @click="submitForm"
        >确 定</el-button>
      </span>
      <span v-else slot="footer" class="dialog-footer">
        <el-button @click="handleClose">返 回</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ImgUpload from '@/components/ImgUpload'
import community from '@/api/community'
import sysManageApi from '@/api/sysManageApi'
const defaultSearchForm = {
  goodsName: '',
  goodsId: ''
}

const ruleForm = {
  id: '',
  isTop: '0',
  positiveSide: '',
  negativeSide: '',
  virtualViews: '',
  topicActivityRule: '',
  goodsChannelNo: '',
  cycleDate: [],
  topicName: '',
  topicType: '',
  classificationType: '',
  content: '',
  coverImgId: '',
  bigCoverImgId: '',
  status: '',
  hasActivity: '0', // 关联活动设置
  activityTitle: '', // 活动标题
  activityTime: [], // 活动时间
  rankEndTime: '', // 排行结束时间
  activityContent: '', // 内容描述
  buttonName: '', // 按钮名称
  activityUrl: '' // 跳转链接
}

export default {
  name: 'LightcommunityManagerDetail',
  components: {
    ImgUpload
  },
  props: {
    type: {
      type: String,
      default: ''
    },
    row: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isloading: false,
      imgView: false,
      loading: false,
      fileList: [],
      tableData: [],
      topicTypeObj: [],
      classificationTypeObj: [],
      imageUrl: '',
      imageUrl2: '',
      dialogVisible: false,
      ruleForm: { ...ruleForm },
      rules: {
        topicName: [{ required: true, message: '不能为空', trigger: 'change' }],
        topicType: [{ required: true, message: '不能为空', trigger: 'change' }],
        classificationType: [
          { required: true, message: '不能为空', trigger: 'change' }
        ],
        content: [{ required: true, message: '不能为空', trigger: 'change' }],
        isTop: [{ required: true, message: '不能为空', trigger: 'change' }],
        cycleDate: [{ required: true, message: '不能为空', trigger: 'change' }],
        rankEndTime: [
          { required: true, message: '不能为空', trigger: 'change' }
        ],
        positiveSide: [
          { required: true, message: '不能为空', trigger: 'change' }
        ],
        negativeSide: [
          { required: true, message: '不能为空', trigger: 'change' }
        ],
        hasActivity: [
          { required: true, message: '不能为空', trigger: 'change' }
        ],
        activityTitle: [
          { required: true, message: '不能为空', trigger: 'change' }
        ],
        activityTime: [
          { required: true, message: '不能为空', trigger: 'change' }
        ],
        activityContent: [
          { required: true, message: '不能为空', trigger: 'change' }
        ]
      },
      goodsList: {
        searchForm: Object.assign({}, defaultSearchForm),
        visible: false,
        list: [],
        pageSize: 10,
        pageSizes: [10, 30, 50],
        currentPage: 1,
        total: 0
      },
      selectedGoodsList: []
      // setDateRange: {
      //   disabledDate: time => {
      //     const { cycleDate } = this.ruleForm
      //     if (cycleDate.length > 1) {
      //       var time1 = (new Date(cycleDate[0].slice(0, 10)).getTime()) - 8.64e7 // 当天可选
      //       var time2 = new Date(cycleDate[1].slice(0, 10)).getTime()
      //       return time.getTime() < time1 || time.getTime() > time2
      //     }
      //     return
      //   }
      // }
    }
  },

  computed: {
    setDateRange() {
      return {
        disabledDate: time => {
          const { cycleDate } = this.ruleForm
          if (cycleDate.length > 1) {
            var time1 = new Date(cycleDate[0].slice(0, 10)).getTime() - 8.64e7 // 当天可选
            var time3 = cycleDate[1].slice(0, 10) + ' 23:59:59'
            var time2 = new Date(time3).getTime()
            return time.getTime() < time1 || time.getTime() > time2
          } else {
            return
          }
        }
      }
    }
  },
  watch: {},

  mounted() {
    this.getTopicTypeEnum()
    this.getClassificationList()
  },
  methods: {
    getTopicTypeEnum() {
      sysManageApi.getTypeEnum({ enumName: 'TopicTypeEnum' }).then(res => {
        this.topicTypeObj = res.data
      })
    },
    getClassificationList() {
      community.getClassificationList().then(res => {
        this.classificationTypeObj = res.data
      })
    },
    handlePictureCardPreview() {
      this.imgView = true
    },
    /**
     * @method: 限制上传文件格式和大小
     */
    beforeUpload(file) {
      const isJPEG = file.type === 'image/jpeg'
      const isJPG = file.type === 'image/jpg'
      const isPNG = file.type === 'image/png'
      // const isGIF = file.type === 'image/gif'
      const isBMP = file.type === 'image/bmp'
      const isLt5M = file.size / 1024 / 1024 < 6
      if (!isJPEG && !isJPG && !isPNG && !isBMP) {
        this.$message.error('上传文件只能是 JPG, JPEG, PNG, BMP 格式!')
        this.clearFile()
        return false
      }
      if (!isLt5M) {
        this.$message.error('上传文件大小不能超过 5MB!')
        this.clearFile()
        return false
      }
    },
    // 获取详情
    getdetail() {
      if (this.type === 'add') {
        this.tableData = []
        this.ruleForm = { ...ruleForm }
        this.$nextTick(() => {
          this.$refs['ruleForm'].resetFields()
        })
      } else {
        this.loading = true
        community
          .detailTopic({
            id: this.row.id
          })
          .then(res => {
            this.loading = false
            const obj = JSON.parse(JSON.stringify(res.data))
            this.ruleForm = obj
            this.$set(
              this.ruleForm,
              'hasActivity',
              obj.hasActivity ? '1' : '0'
            )
            if (this.ruleForm.hasActivity === '1') {
              const {
                activityTitle,
                startTime,
                endTime,
                content,
                buttonName,
                activityUrl
              } = obj.topicActivity
              this.$set(this.ruleForm, 'activityTitle', activityTitle || '')
              this.$set(
                this.ruleForm,
                'activityTime',
                startTime ? [startTime, endTime] : []
              )
              this.$set(this.ruleForm, 'activityContent', content || '')
              this.$set(this.ruleForm, 'buttonName', buttonName || '')
              this.$set(this.ruleForm, 'activityUrl', activityUrl || '')
            }
            this.imageUrl = res.data.fileUrl
            this.imageUrl2 = res.data.bigCoverImg
            this.tableData = res.data.goodsList
            this.$set(this.ruleForm, 'cycleDate', [obj.beginTime, obj.endTime])
          })
      }
    },
    handleSelectionChange(val) {
      this.selectedGoodsList = val
    },
    closeGoodsList(type) {
      if (type === 1) {
        // this.tableData = this.selectedGoodsList
        if (this.tableData) {
          this.tableData = [...this.tableData, ...this.selectedGoodsList]
          this.tableData = this.uniqueFunc(this.tableData, 'goodsId')
        } else {
          this.tableData = this.selectedGoodsList
        }
      }
      this.$refs.goodChooseTableRef.clearSelection()
      this.goodsList.visible = false
    },
    searchGoodsList() {
      this.goodsList.currentPage = 1
      this.goodsList.pageSize = 10
      this.getGoodsList()
    },
    getGoodsList() {
      const goodsIdList = []
      if (this.goodsList.searchForm.goodsId) {
        goodsIdList.push(this.goodsList.searchForm.goodsId)
      }

      const params = {
        goodsIdList: goodsIdList,
        goodsName: this.goodsList.searchForm.goodsName,
        // 'goodsStatus': this.goodsList.searchForm.goodsStatus,
        pageNum: this.goodsList.currentPage,
        pageSize: this.goodsList.pageSize
      }
      community.getGoodsList(params).then(res => {
        this.goodsList.list = res.data.list
        this.goodsList.total = Number(res.data.total)
      })
    },
    uniqueFunc(arr, uniId) {
      const res = new Map()
      return arr.filter(
        item => !res.has(item[uniId]) && res.set(item[uniId], 1)
      )
    },
    onReset() {
      this.goodsList.searchForm = { ...defaultSearchForm }
      this.goodsList.currentPage = 1
      this.goodsList.pageSize = 10
      this.getGoodsList()
    },
    handleSizeChange(val) {
      this.goodsList.pageSize = val
      this.goodsList.currentPage = 1
      this.getGoodsList()
    },
    handleCurrentChange(val) {
      this.goodsList.currentPage = val
      this.getGoodsList()
    },
    handleCloseGoodsList() {
      this.goodsList.visible = false
    },
    handleClose() {
      this.imageUrl = ''
      this.imageUrl2 = ''
      this.dialogVisible = false
    },
    // getRowkey(row) {
    //   return row.goodsId
    // },
    handleClick(row) {
      const index = this.tableData.indexOf(row)
      this.tableData.splice(index, 1)
      // this.selectedGoodsList.splice(index, 1)
    },
    chooseGoods() {
      this.goodsList.searchForm = { ...defaultSearchForm }
      this.goodsList.visible = false
      this.goodsList.list = []
      this.goodsList.pageSize = 10
      this.goodsList.pageSizes = [10, 30, 50]
      this.goodsList.currentPage = 1
      this.goodsList.total = 0
      this.getGoodsList()
      this.goodsList.visible = true
    },
    changeImg1Id(val) {
      this.ruleForm.coverImgId = val
    },
    changeImg2Id(val) {
      this.ruleForm.bigCoverImgId = val
    },
    submitForm() {
      this.$refs['ruleForm'].validate(async valid => {
        if (valid) {
          const { cycleDate } = this.ruleForm
          this.isloading = true
          const form = JSON.parse(JSON.stringify(this.ruleForm))
          const obj = {
            id: form.id,
            isTop: form.isTop,
            positiveSide: form.positiveSide,
            negativeSide: form.negativeSide,
            virtualViews: form.virtualViews,
            topicActivityRule: form.topicActivityRule,
            goodsChannelNo: form.goodsChannelNo,
            cycleDate: form.cycleDate,
            rankEndTime: form.rankEndTime,
            topicName: form.topicName,
            topicType: form.topicType,
            classificationType: form.classificationType,
            content: form.content,
            coverImgId: form.coverImgId,
            bigCoverImgId: form.bigCoverImgId,
            status: form.status,
            hasActivity: form.hasActivity === '1' // 关联活动设置
          }
          if (form.hasActivity === '1') {
            const { activityTime } = this.ruleForm
            const actStartTime = new Date(activityTime[0]).getTime()
            const actEndTime = new Date(activityTime[1]).getTime()
            const cycleStartTime = new Date(cycleDate[0]).getTime()
            const cycleEndTime = new Date(cycleDate[1]).getTime()
            if (
              (cycleStartTime < actStartTime ||
                cycleStartTime === actStartTime) &&
              (actEndTime < cycleEndTime || actEndTime === cycleEndTime)
            ) {
              obj['activityDTO'] = {
                activityTitle: form.activityTitle, // 活动标题
                startTime: form.activityTime[0],
                endTime: form.activityTime[1],
                content: form.activityContent, // 内容描述
                buttonName: form.buttonName, // 按钮名称
                activityUrl: form.activityUrl // 跳转链接
              }
            } else {
              this.$message.error('活动时间必须在话题时间范围内！')
              this.isloading = false
              return
            }
          }
          if (!this.imageUrl) {
            obj.coverImgId = ''
          }
          if (!this.imageUrl2) {
            obj.bigCoverImgId = ''
          }
          if (obj.cycleDate !== '') {
            obj.beginTime = obj.cycleDate[0]
            obj.endTime = obj.cycleDate[1]
          }
          delete obj.cycleDate
          obj.goodsList = this.tableData

          if (obj.goodsList && obj.goodsList.length > 10) {
            this.$alert('话题关联的商品不能超过10条！')
            this.isloading = false
            return
          }

          if (
            obj.id != null &&
            this.ruleForm.isTop === '1' &&
            obj.status === '5'
          ) {
            this.$confirm(`该话题已下架，不可被置顶！`, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
          } else {
            if (this.ruleForm.isTop === '1') {
              try {
                const data = await community.getIsTopFlag()
                obj.topId = data.data
              } catch (e) {
                this.isloading = false
              }
              if (obj.topId != null && obj.id !== obj.topId) {
                this.$confirm(
                  `是否确定将该话题置顶，如果确定，则之前置顶的话题将被取消置顶。`,
                  '提示',
                  {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                  }
                )
                  .then(async() => {
                    try {
                      await community.saveTopic(obj)
                      this.$message({
                        type: 'success',
                        message: `${
                          this.type === 'add' ? '新增' : '编辑'
                        }成功！`
                      })
                      this.handleClose()
                      this.isloading = false
                      this.$emit('addlist')
                    } catch (e) {
                      this.isloading = false
                    }
                  })
                  .catch(() => {
                    this.isloading = false
                  })
              } else {
                try {
                  await community.saveTopic(obj)
                  this.$message({
                    type: 'success',
                    message: `${this.type === 'add' ? '新增' : '编辑'}成功！`
                  })
                  this.handleClose()
                  this.isloading = false
                  this.$emit('addlist')
                } catch (e) {
                  this.isloading = false
                }
              }
            } else {
              try {
                await community.saveTopic(obj)
                this.$message({
                  type: 'success',
                  message: `${this.type === 'add' ? '新增' : '编辑'}成功！`
                })
                this.handleClose()
                this.isloading = false
                this.$emit('addlist')
              } catch (e) {
                this.isloading = false
              }
            }
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__footer {
  text-align: right;
}

.detail {
  width: 100%;
  box-sizing: border-box;
  padding: 0 60px 0 0;
  min-height: 100%;
}

.line {
  text-align: center;
}

::v-deep .avatar {
  width: 120px;
  height: 120px;
  display: block;
  object-fit: cover;
}

.el-table >>> .DisabledSelection .cell .el-checkbox__inner {
  display: none;
  position: relative;
}

::v-deep .el-loading-spinner {
  top: 40%;
}
</style>
