# 菜单删除接口修改说明

## 修改概述

根据接口规范要求，菜单删除接口应该使用查询参数而不是JSON传字段的方式。已将删除接口从POST请求改为GET请求，并使用查询参数传递menuId。

## 接口规范

### 修改后的接口格式
- **接口地址**: `/sys/menu/delMenu?menuId={menuId}`
- **请求方式**: GET
- **参数传递**: 查询参数 (Query Parameters)
- **参数名**: `menuId`
- **参数类型**: String
- **示例**: `/sys/menu/delMenu?menuId=/user-manage`

## 修改内容

### 1. API接口修改 (`src/api/menuApi.js`)

#### 修改前
```javascript
// 删除菜单
export function deleteMenu(data) {
  return request({
    url: '/sys/menu/delMenu',
    method: 'post',
    data
  })
}
```

#### 修改后
```javascript
// 删除菜单
export function deleteMenu(menuId) {
  return request({
    url: '/sys/menu/delMenu',
    method: 'get',
    params: { menuId }
  })
}
```

### 2. 页面调用修改 (`src/views/system-setting/menu-manage/index.vue`)

#### 修改前
```javascript
handleDelete(row) {
  this.$confirm(`确定对【${row.menuName}】进行删除操作吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const params = {
      menuId: row.menuId
    }
    menuApi.deleteMenu(params).then((res) => {
      // 处理响应
    })
  })
}
```

#### 修改后
```javascript
handleDelete(row) {
  this.$confirm(`确定对【${row.menuName}】进行删除操作吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 直接传递 menuId 参数，接口使用查询参数
    menuApi.deleteMenu(row.menuId).then((res) => {
      // 处理响应
    })
  })
}
```

## 主要变更点

### 1. 请求方式变更
- **从**: POST 请求 + JSON 数据
- **到**: GET 请求 + 查询参数

### 2. 参数传递方式变更
- **从**: `data: { menuId: 'xxx' }`
- **到**: `params: { menuId: 'xxx' }`

### 3. 函数参数简化
- **从**: `deleteMenu(data)` - 传递对象
- **到**: `deleteMenu(menuId)` - 直接传递字符串

## 技术细节

### 1. Axios 参数配置
```javascript
// GET 请求使用 params 配置
request({
  url: '/sys/menu/delMenu',
  method: 'get',
  params: { menuId }  // 会自动转换为查询参数
})
```

### 2. 最终请求URL
当调用 `deleteMenu('/user-manage')` 时，实际发送的请求为：
```
GET /sys/menu/delMenu?menuId=/user-manage
```

### 3. 后端接收方式
后端可以通过以下方式接收参数：
```java
// Spring Boot 示例
@GetMapping("/delMenu")
public Result deleteMenu(@RequestParam String menuId) {
    // 处理删除逻辑
}
```

## 兼容性说明

### 1. 前端兼容性
- ✅ 支持所有现代浏览器
- ✅ 与现有的请求拦截器兼容
- ✅ 错误处理机制保持不变

### 2. 后端兼容性
- ⚠️ 需要后端同步修改接收方式
- ⚠️ 从 `@RequestBody` 改为 `@RequestParam`
- ⚠️ 请求方式从 POST 改为 GET

## 测试验证

### 1. 功能测试
1. 在菜单管理页面选择一个菜单项
2. 点击"删除"按钮
3. 确认删除操作
4. 验证菜单是否成功删除

### 2. 接口测试
1. 打开浏览器开发者工具
2. 切换到 Network 面板
3. 执行删除操作
4. 检查请求格式是否为：`GET /sys/menu/delMenu?menuId=xxx`

### 3. 参数验证
确认传递的 menuId 参数值正确，例如：
- `/user-manage`
- `/system-setting`
- 其他菜单ID

## 错误处理

### 1. 前端错误处理
保持原有的错误处理逻辑：
```javascript
menuApi.deleteMenu(row.menuId).then((res) => {
  this.$message({
    type: 'success',
    message: res.msg
  })
  this.fetchData()
}).catch((error) => {
  // 自动由请求拦截器处理
})
```

### 2. 常见错误情况
- **404错误**: 菜单ID不存在
- **403错误**: 没有删除权限
- **500错误**: 服务器内部错误

## 注意事项

### 1. 后端同步修改
确保后端接口同步修改为GET请求并使用查询参数接收menuId。

### 2. 参数编码
如果menuId包含特殊字符，axios会自动进行URL编码。

### 3. 缓存问题
GET请求可能被浏览器缓存，如果需要避免缓存，可以添加时间戳参数。

### 4. 安全考虑
虽然改为GET请求，但仍需要在后端进行权限验证。

## 总结

此次修改将菜单删除接口从POST请求改为GET请求，使用查询参数传递menuId，符合RESTful API的设计规范。修改后的接口更加简洁，参数传递更加直观。

修改涉及的文件：
- `src/api/menuApi.js` - API接口定义
- `src/views/system-setting/menu-manage/index.vue` - 页面调用逻辑

请确保后端接口同步进行相应的修改。
