import request from '@/utils/request'

// 获取账号列表
export function getAccountList(data) {
  return request({
    url: '/sys/user/search',
    method: 'post',
    data
  })
}

// 新增账号
export function addAccount(data) {
  return request({
    url: '/sys/user/add',
    method: 'post',
    data
  })
}

// 修改账号
export function modifyAccount(data) {
  return request({
    url: '/sys/user/modify',
    method: 'post',
    data
  })
}

// 删除账号
export function deleteAccount(userId) {
  return request({
    url: '/sys/user/delete',
    method: 'post',
    params: { userId }
  })
}

// 获取角色列表
export function getRoleList() {
  return request({
    url: '/sys/role/search',
    method: 'post'
  })
}

export default {
  getAccountList,
  addAccount,
  modifyAccount,
  deleteAccount,
  getRoleList
}
