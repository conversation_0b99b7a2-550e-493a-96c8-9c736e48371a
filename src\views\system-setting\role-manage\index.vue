<template>
  <div>
    <RouteTitle />
    <div class="main-body">
      <div class="main-body-bottom">
        <div class="body-top-form">
          <el-form
            ref="searchForm"
            :model="mainObj.searchForm"
            label-width="75px"
            label-position="left"
            class="left"
            inline
          >
            <el-form-item label="角色名称">
              <el-input
                v-model.trim="mainObj.searchForm.roleName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
            <!-- <el-form-item label="角色编码">
            <el-input
              v-model.trim="mainObj.searchForm.roleCode"
              placeholder="请输入"
              clearable
            /> -->
            <!-- </el-form-item> -->
            <el-form-item>
              <el-button
                type="primary"
                @click="onSearch"
              >查询</el-button>
              <el-button @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
          <div v-if="addPermission" class="right">
            <div class="main-body-bottom-btn-left">
              <el-button
                type="primary"
                @click="onOperate('add')"
              >新增</el-button>
            </div>
          </div>
        </div>
        <div class="table">
          <el-table
            ref="tableRef"
            :data="mainObj.list"
            fit
            border
            highlight-current-row
            :header-cell-style="{'background': '#F9F9F9'}"
          >
            <el-table-column
              prop="roleName"
              label="角色名称"
            />
            <!-- <el-table-column
              show-overflow-tooltip
              prop="roleTypeName"
              label="角色类型"
            /> -->
            <!-- <el-table-column
              show-overflow-tooltip
              prop="roleCode"
              label="角色编码"
            /> -->
            <!-- <el-table-column
              show-overflow-tooltip
              prop="roleStatusName"
              label="角色状态"
            /> -->
            <el-table-column
              prop="updateTime"
              label="更新时间"
            />
            <el-table-column label="操作" width="180">
              <template slot-scope="scope">
                <el-button
                  v-if="updatePermission"
                  v-preventReClick
                  type="text"
                  @click="onOperate('update', scope.row)"
                >编辑</el-button>
                <el-button
                  v-if="sysRole_details"
                  type="text"
                  @click="onOperate('view', scope.row)"
                >查看</el-button>
                <el-button
                  v-if="deletePermission && checkIsSuperAdmin(scope.row)"
                  type="text"
                  @click="onOperate('delete', scope.row)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination
            :current-page="mainObj.currentPage"
            :page-size="mainObj.pageSize"
            :page-sizes="mainObj.pagesizes"
            :total="mainObj.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <!-- 角色新增/编辑弹窗 -->
    <el-dialog
      :title="roleDialogObj.title"
      :visible.sync="roleDialogObj.visible"
      destroy-on-close
      width="800px"
      @close="roleDialogObj.visible = false"
    >
      <el-form
        ref="roleFormRef"
        :model="roleDialogObj.form"
        :rules="roleDialogObj.type === 'view' ? null : roleDialogObj.rules"
        :disabled="roleDialogObj.type === 'view' ||(roleDialogObj.roleType === '1' && roleDialogObj.roleType.length>0)"
        label-width="80px"
        label-position="left"
        class="full-width"
      >
        <el-form-item label="角色名称" prop="roleName">
          <el-input
            v-model="roleDialogObj.form.roleName"
            maxlength="20"
            placeholder="请输入"
            clearable
            :disabled="roleDialogObj.roleType === '1' && roleDialogObj.roleType.length>0 "
          />
        </el-form-item>
        <!-- <el-form-item label="角色编码" prop="roleCode">
          <el-input
            v-model="roleDialogObj.form.roleCode"
            maxlength="50"
            placeholder="请输入"
            clearable
            :disabled="roleDialogObj.roleType === '1' && roleDialogObj.roleType.length>0 "
          />
        </el-form-item> -->
        <!-- <el-form-item label="角色状态" prop="roleStatus">
          <el-select
            v-model="roleDialogObj.form.roleStatus"
            placeholder="请选择"
            :disabled="roleDialogObj.roleType === '1' && roleDialogObj.roleType.length>0"
          >
            <el-option
              v-for="item in roleStatusEnums"
              :key="item.code"
              :label="item.text"
              :value="item.code"
            />
          </el-select>
        </el-form-item> -->
        <el-tree
          ref="treeRef"
          :data="treeData"
          :props="defaultProps"
          :default-checked-keys="roleDialogObj.form.permIdList"
          node-key="id"
          show-checkbox
          default-expand-all
          highlight-current
        />
      </el-form>
      <div v-if="!(roleDialogObj.roleType === '1' && roleDialogObj.roleType.length>0) && roleDialogObj.type !== 'view'" slot="footer">
        <el-button @click="roleDialogObj.visible = false">取消</el-button>
        <el-button
          v-preventReClick
          type="primary"
          :loading="isloading"
          @click="onOperate('submit')"
        >确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import sysManageApi from '@/api/sysManageApi'
import checkPermission from '@/utils/permission'
import { obj2form } from '@/utils'
import RouteTitle from '@/components/RouteTitle'

const defaultSearchForm = {
  roleName: ''
  // roleCode: ''
}

const defaultUserForm = {
  roleName: '',
  roleCode: '',
  roleStatus: '',
  roleStatusDefault: '',
  permIdList: []
}
export default {
  components: {
    RouteTitle
  },
  data() {
    return {
      isloading: false,
      addPermission: checkPermission(['sysRole_add']),
      deletePermission: checkPermission(['sysRole_delete']),
      updatePermission: checkPermission(['sysRole_modify']),
      sysRole_details: checkPermission(['sysRole_details']),
      roleStatusEnums: [
        {
          code: '1',
          name: 'ENABLE',
          text: '启用'
        },
        {
          code: '2',
          name: 'DISABLE',
          text: '停用'
        }
      ],
      mainObj: {
        list: [],
        pageSize: 10,
        pagesizes: [10, 30, 50],
        currentPage: 1,
        total: 0,
        searchForm: { ...defaultSearchForm }
      },
      roleDialogObj: {
        visible: false,
        roleType: '',
        tile: '',
        type: '',
        form: Object.assign({}, defaultUserForm),
        rules: {
          roleName: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ]
          // roleCode: [
          //   { required: true, message: '不能为空', trigger: 'blur' }
          // ]
          // roleStatus: [
          //   { required: true, message: '不能为空', trigger: 'blur' }
          // ]
        }
      },
      treeData: [],
      treeDisableData: [],
      defaultProps: {
        children: 'subPermDetailDtoList',
        label: 'permName',
        disabled: 'disabled'
      }
    }
  },
  created() {
    this.getPermissionTree()
    this.onSearch()
    console.log(checkPermission(['sysRole_add']),)
  },
  methods: {

    checkIsSuperAdmin(index) {
      return index.roleType.code === '3'
    },

    checkPermission,
    getPermissionTree() {
      sysManageApi.getPermissionTree().then((res) => {
        // res.data[0].disabled = true;
        console.log('treeData', res.data)

        this.treeData = res.data
        // this.treeDisableData = res.data
        // this.treeDisableData.forEach(e => {
        //   // e. = true
        //   // this.setTreeDisable(e.subPermDetailDtoList)
        // })
        // console.log('treeDisableData',this.treeDisableData)
      })
    },

    setTreeDisable(data) {
      if (data && data.length > 0) {
        data.forEach(e => {
          e.disabled = true
          this.setTreeDisable(e.subPermDetailDtoList)
        })
      }
    },

    onSearch() {
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    onReset() {
      this.mainObj.searchForm = { ...defaultSearchForm }
      this.fetchData()
    },
    handleSizeChange(val) {
      this.mainObj.pageSize = val
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.mainObj.currentPage = val
      this.fetchData()
    },
    fetchData() {
      const params = {
        roleName: this.mainObj.searchForm.roleName,
        roleCode: this.mainObj.searchForm.roleCode,
        pageNum: this.mainObj.currentPage,
        pageSize: this.mainObj.pageSize
      }
      sysManageApi.getRoleList(params).then((res) => {
        this.mainObj.list = res.data.list
        this.mainObj.total = res.data.total
      })
    },
    recursionTreeForCheckedKeys(tree) {
      const arr = []
      const rev = data => {
        data.forEach(e => {
          if (Array.isArray(e.subPermDetailDtoList) && e.subPermDetailDtoList.length > 0) {
            rev(e.subPermDetailDtoList)
          } else {
            arr.push(e.id)
          }
        })
      }
      rev(tree)
      return arr
    },
    onOperate(type, row) {
      if (type === 'add') {
        this.roleDialogObj.visible = true
        this.roleDialogObj.type = type
        this.roleDialogObj.title = '新增'
        this.roleDialogObj.form = Object.assign({}, defaultUserForm)
        // console.log(this.roleDialogObj.roleType !== '3' && this.roleDialogObj.roleType.length>0)
        this.$nextTick(() => {
          this.$refs.treeRef.setCheckedKeys([])
          this.$refs.roleFormRef.clearValidate()
        })
      } else if (type === 'update') {
        this.roleDialogObj.visible = true
        this.roleDialogObj.type = type
        this.roleDialogObj.title = '修改'
        const params = {
          roleId: row.id
        }
        sysManageApi.getRoleInfo(params).then((res) => {
          this.roleDialogObj.roleType = res.data.roleType
          this.roleDialogObj.form = {
            id: res.data.id,
            roleName: res.data.roleName,
            roleCode: res.data.roleCode,
            roleStatusDefault: res.data.roleStatus,
            roleStatus: res.data.roleStatusName,
            permIdList: this.recursionTreeForCheckedKeys(res.data.permDetailDtoList)
          }
          this.$nextTick(() => {
            this.$refs.treeRef.setCheckedKeys(this.roleDialogObj.form.permIdList)
            this.$refs.roleFormRef.clearValidate()
          })
        })
      } else if (type === 'view') {
        this.roleDialogObj.visible = true
        this.roleDialogObj.type = type
        this.roleDialogObj.title = '查看'
        const params = {
          roleId: row.id
        }
        sysManageApi.getRoleInfo(params).then((res) => {
          this.roleDialogObj.roleType = res.data.roleType
          this.roleDialogObj.form = {
            id: res.data.id,
            roleName: res.data.roleName,
            // roleCode: res.data.roleCode,
            // roleStatus: res.data.roleStatusName,
            permIdList: this.recursionTreeForCheckedKeys(res.data.permDetailDtoList)
          }
          this.$nextTick(() => {
            this.$refs.treeRef.setCheckedKeys(this.roleDialogObj.form.permIdList)
            this.$refs.roleFormRef.clearValidate()
          })
        })
      } else if (type === 'submit') {
        this.$refs.roleFormRef.validate((valid) => {
          if (valid) {
            this.isloading = true
            const checkedKeys = this.$refs.treeRef.getCheckedKeys()
            const halfKeys = this.$refs.treeRef.getHalfCheckedKeys()
            // let roleStatus = this.roleDialogObj.form.roleStatusDefault
            // const status = parseInt(this.roleDialogObj.form.roleStatus)
            // if (!isNaN(status)) {
            //   roleStatus = this.roleDialogObj.form.roleStatus
            // }
            const params = {
              roleName: this.roleDialogObj.form.roleName,
              roleRemark: this.roleDialogObj.form.roleName,
              permIdList: [...checkedKeys, ...halfKeys]
            }
            if (this.roleDialogObj.type === 'update') {
              params.roleId = this.roleDialogObj.form.id
            }
            if (params.permIdList.length <= 0) {
              this.$message({
                type: 'warning',
                message: '所选权限不能为空!'
              })
              return
            }
            sysManageApi.saveRole(params, this.roleDialogObj.type).then((res) => {
              this.$message({
                type: 'success',
                message: res.msg
              })
              this.$refs.treeRef.setCheckedKeys([])
              this.fetchData()
              this.roleDialogObj.visible = false
              this.isloading = false
            })
          }
        })
      } else if (type === 'delete') {
        this.$confirm('确定删除该角色吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const params = {
            roleId: row.id
          }
          sysManageApi.deleteRole(obj2form(params)).then((res) => {
            this.$message({
              type: 'success',
              message: res.msg
            })
            if ((this.mainObj.total - 1) === ((this.mainObj.currentPage - 1) * this.mainObj.pageSize)) {
              this.mainObj.currentPage--
            }
            this.fetchData()
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.main-body-bottom {
  box-sizing: border-box;
  padding: 20px;
}

.right {
  float: right;
}

.left {
  float: left;
}

</style>
