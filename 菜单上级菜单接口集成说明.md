# 菜单上级菜单接口集成说明

## 修改概述

根据新的接口规范，已将菜单管理页面的上级菜单选择功能改为通过接口动态获取，而不是从当前页面数据中筛选。

## 新接口规范

### 接口信息
- **接口地址**: `/sys/menu/menuListByType`
- **请求方式**: POST
- **功能**: 根据菜单类型查找菜单列表

### 请求参数
| 参数名 | 类型 | 描述 | 是否必填 | 说明 |
|--------|------|------|----------|------|
| permType | string | 菜单类型 | 是 | CATALOG-目录<br>MENU-菜单<br>FUNCTION-按钮 |

### 响应参数
| 参数名 | 类型 | 描述 | 是否必填 | 说明 |
|--------|------|------|----------|------|
| menuId | string | 菜单id | 是 | |
| menuName | string | 菜单名称 | 是 | |

## 修改内容

### 1. API接口添加

在 `src/api/menuApi.js` 中添加新接口：

```javascript
// 根据菜单类型查询上级菜单列表
export function getMenuListByType(data) {
  return request({
    url: '/sys/menu/menuListByType',
    method: 'post',
    data
  })
}
```

### 2. 页面数据结构修改

在 `src/views/system-setting/menu-manage/index.vue` 的 data 中添加：

```javascript
data() {
  return {
    parentMenuOptions: [], // 上级菜单选项
    // ... 其他数据
  }
}
```

### 3. 模板修改

修改上级菜单选择的数据源：

```vue
<!-- 修改前 -->
<el-option
  v-for="item in getParentOptions()"
  :key="item.menuId"
  :label="item.menuName"
  :value="item.menuId"
/>

<!-- 修改后 -->
<el-option
  v-for="item in parentMenuOptions"
  :key="item.menuId"
  :label="item.menuName"
  :value="item.menuId"
/>
```

### 4. 核心方法实现

#### 新增 loadParentMenuOptions 方法
```javascript
// 加载上级菜单选项
async loadParentMenuOptions() {
  if (this.dialogObj.form.type === 'directory') {
    // 目录没有上级菜单
    this.parentMenuOptions = []
    return
  }

  try {
    let permType = ''
    if (this.dialogObj.form.type === 'menu') {
      // 菜单的上级只能是目录
      permType = 'CATALOG'
    } else if (this.dialogObj.form.type === 'button') {
      // 按钮的上级只能是菜单
      permType = 'MENU'
    }

    if (permType) {
      const res = await menuApi.getMenuListByType({ permType })
      if (res.code === 200) {
        this.parentMenuOptions = res.data || []
      } else {
        this.$message.error(res.message || '获取上级菜单失败')
        this.parentMenuOptions = []
      }
    }
  } catch (error) {
    console.error('加载上级菜单失败:', error)
    this.$message.error('加载上级菜单失败，请重试')
    this.parentMenuOptions = []
  }
}
```

#### 修改 onTypeChange 方法
```javascript
// 类型改变事件
onTypeChange() {
  this.dialogObj.form.parentId = ''
  this.dialogObj.form.icon = ''
  this.dialogObj.form.sort = 1
  this.dialogObj.form.frontendUrl = ''
  this.dialogObj.form.backendUrl = ''
  
  // 根据类型加载对应的上级菜单选项
  this.loadParentMenuOptions()
}
```

#### 修改 handleAdd 和 handleEdit 方法
在这两个方法中都添加了 `this.loadParentMenuOptions()` 调用，确保打开对话框时加载正确的上级菜单选项。

### 5. 删除旧方法

删除了不再使用的 `getParentOptions` 方法，因为现在通过接口动态获取上级菜单。

## 业务逻辑

### 1. 菜单层级关系
- **目录 (CATALOG)**: 顶级菜单，没有上级菜单
- **菜单 (MENU)**: 上级菜单只能是目录
- **按钮 (FUNCTION)**: 上级菜单只能是菜单

### 2. 动态加载逻辑
1. 用户选择菜单类型时触发 `onTypeChange`
2. 根据选择的类型确定需要查询的上级菜单类型
3. 调用接口获取对应类型的菜单列表
4. 更新上级菜单选项供用户选择

### 3. 类型映射关系
| 当前选择类型 | 上级菜单类型 | 接口查询参数 |
|-------------|-------------|-------------|
| directory   | 无          | 不查询      |
| menu        | 目录        | CATALOG     |
| button      | 菜单        | MENU        |

## 修复的问题

### 修改前的问题
1. **数据来源不准确**: 从当前页面数据中筛选上级菜单，可能不完整
2. **实时性差**: 无法获取最新的菜单数据
3. **逻辑复杂**: 需要复杂的筛选逻辑来判断菜单类型

### 修改后的优势
1. ✅ **数据准确**: 通过专门接口获取最新的菜单数据
2. ✅ **逻辑清晰**: 根据类型直接查询对应的上级菜单
3. ✅ **实时更新**: 每次打开对话框都会获取最新数据
4. ✅ **性能优化**: 只查询需要的菜单类型，减少数据传输

## 使用流程

### 1. 新增菜单
1. 点击"新增"按钮
2. 选择菜单类型（目录/菜单/按钮）
3. 系统自动加载对应的上级菜单选项
4. 用户从下拉列表中选择上级菜单
5. 填写其他信息并保存

### 2. 编辑菜单
1. 点击"修改"按钮
2. 系统根据当前菜单类型加载上级菜单选项
3. 显示当前的上级菜单选择
4. 用户可以修改上级菜单选择
5. 保存修改

### 3. 类型切换
1. 在对话框中切换菜单类型
2. 系统自动清空当前的上级菜单选择
3. 重新加载适合新类型的上级菜单选项
4. 用户重新选择上级菜单

## 注意事项

1. **网络异常处理**: 添加了完善的错误处理和用户提示
2. **数据验证**: 确保接口返回的数据格式正确
3. **用户体验**: 类型切换时自动清空并重新加载选项
4. **性能考虑**: 只在需要时才调用接口，避免不必要的请求

## 测试建议

1. **功能测试**
   - 测试新增不同类型菜单时的上级菜单选项
   - 测试编辑菜单时的上级菜单选项显示
   - 测试类型切换时的选项更新

2. **接口测试**
   - 验证接口调用参数是否正确
   - 检查接口返回数据格式是否符合预期

3. **异常测试**
   - 测试网络异常时的错误处理
   - 测试接口返回错误时的用户提示
