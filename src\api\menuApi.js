import request from '@/utils/request'

// 获取菜单列表
export function getMenuList(data) {
  return request({
    url: '/sys/menu/menuListQuery',
    method: 'post',
    data
  })
}

// 新增菜单
export function addMenu(data) {
  return request({
    url: '/sys/menu/addMenu',
    method: 'post',
    data
  })
}

// 修改菜单
export function modifyMenu(data) {
  return request({
    url: '/sys/menu/modifyMenu',
    method: 'post',
    data
  })
}

// 删除菜单
export function deleteMenu(menuId) {
  return request({
    url: '/sys/menu/delMenu',
    method: 'post',
    params: { menuId }
  })
}

// 获取菜单详情
export function getMenuDetail(params) {
  return request({
    url: '/sys/menu/getMenuDetail',
    method: 'get',
    params
  })
}

// 获取图标选项
export function getIconOptions() {
  return request({
    url: '/sys/menu/getIconOptions',
    method: 'get'
  })
}

// 根据菜单类型查询上级菜单列表
export function getMenuListByType(data) {
  return request({
    url: '/sys/menu/menuListByType',
    method: 'post',
    data
  })
}

export default {
  getMenuList,
  addMenu,
  modifyMenu,
  deleteMenu,
  getMenuDetail,
  getIconOptions,
  getMenuListByType
}
