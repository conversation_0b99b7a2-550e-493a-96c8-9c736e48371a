# 文件上传接口修改说明

## 修改概述

已将项目中的文件上传接口统一修改为新的接口规范。

## 新接口规范

### 接口地址
```
/activity-manager-api/file/upload/file
```

### 请求方式
```
POST（表单提交）
```

### 请求参数
| 参数名 | 类型 | 描述 | 是否必填 | 说明 |
|--------|------|------|----------|------|
| file   | 文件 | 文件 | 是       |      |

### 响应参数
| 参数名 | 类型   | 描述     | 是否必填 | 说明 |
|--------|--------|----------|----------|------|
| id     | int    | 文件id   | 是       |      |
| url    | string | 文件路径 | 是       |      |

### URL返回示例
```
"/activity/2025/08/26/68ad16e7e4b02c2e71ce03e6.jpg"
```

### 完整访问地址
```
前端域名地址 + url
示例：http://************:18188/activity/2025/08/26/68ad16e7e4b02c2e71ce03e6.jpg
```

## 修改的文件

### 1. API接口文件
- **文件**: `src/api/community.js`
- **修改内容**: 
  - 更新 `uploadFile` 函数的接口地址
  - 添加 `Content-Type: multipart/form-data` 请求头

```javascript
export function uploadFile(data) {
  return request({
    url: '/activity-manager-api/file/upload/file',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data
  })
}
```

### 2. 图片管理组件
- **文件**: `src/views/base-mgt/image-manage/fileUpload.vue`
- **修改内容**: 更新上传URL地址

```javascript
url: process.env.VUE_APP_BASE_API + '/activity-manager-api/file/upload/file'
```

### 3. 通用工具函数
- **文件**: `src/utils/common.js`
- **新增内容**: 添加文件URL处理函数

```javascript
// 获取文件完整访问URL
export function getFileUrl(url) {
  if (!url) return ''
  
  // 如果已经是完整URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }
  
  // 拼接前端域名地址
  const baseUrl = 'http://************:18188'
  return baseUrl + url
}
```

### 4. 图片上传组件
- **文件**: `src/components/ImgUpload/index.vue`
- **修改内容**: 
  - 引入 `getFileUrl` 函数
  - 在上传成功后处理返回的URL

```javascript
import { getFileUrl } from '@/utils/common'

// 在上传成功回调中
uploadFile(formData)
  .then(res => {
    if (res.code === 200) {
      // 返回完整的访问URL
      const fullUrl = getFileUrl(res.data.url)
      that.$emit('input', fullUrl)
      if (res.data.id) {
        that.$emit('changeId', res.data.id)
      }
    }
  })
```

### 5. 富文本编辑器组件
- **文件**: `src/components/Editor/index.vue`
- **修改内容**: 
  - 引入 `getFileUrl` 函数
  - 在图片上传成功后处理返回的URL

```javascript
import { getFileUrl } from '@/utils/common'

// 在上传成功回调中
uploadFile(formData)
  .then(res => {
    // 返回完整的访问URL
    const fullUrl = getFileUrl(res.data.url)
    insertFn(fullUrl)
  })
```

### 6. 上传混入文件
- **文件**: `src/mixin/upload_mixin.js`
- **修改内容**:
  - 引入 `getFileUrl` 函数
  - 在上传成功后处理返回的URL

```javascript
import { getFileUrl } from '@/utils/common'

// 在上传成功回调中
community.uploadFile(formData).then(res => {
  // 返回完整的访问URL
  this.imageUrl = getFileUrl(res.data.url)
  this.coverImgId = res.data.id
})
```

### 7. 其他上传相关文件
- **文件**: `src/api/hobbyAndPic.js` - 兴趣爱好图片上传接口
- **文件**: `src/views/community-manage/user-info/components/MoreDialog.vue` - 用户信息上传组件
- **文件**: `src/views/system-setting/banner-manage/components/BannerDialog.vue` - Banner管理上传组件
- **修改内容**: 统一更新上传接口地址为新的规范

## 功能特点

1. **统一接口**: 所有文件上传功能都使用统一的接口地址
2. **自动URL处理**: 自动将相对路径转换为完整的访问URL
3. **向后兼容**: 如果返回的已经是完整URL，则直接使用
4. **表单提交**: 使用 `multipart/form-data` 格式提交文件
5. **错误处理**: 保持原有的错误处理机制

## 使用说明

### 在组件中使用文件上传
```javascript
import { uploadFile } from '@/api/community'
import { getFileUrl } from '@/utils/common'

// 上传文件
const formData = new FormData()
formData.append('file', file)

uploadFile(formData).then(res => {
  if (res.code === 200) {
    const fileId = res.data.id
    const fullUrl = getFileUrl(res.data.url)
    console.log('文件ID:', fileId)
    console.log('完整访问URL:', fullUrl)
  }
})
```

### 直接处理文件URL
```javascript
import { getFileUrl } from '@/utils/common'

// 处理相对路径
const relativePath = '/activity/2025/08/26/68ad16e7e4b02c2e71ce03e6.jpg'
const fullUrl = getFileUrl(relativePath)
// 结果: http://************:18188/activity/2025/08/26/68ad16e7e4b02c2e71ce03e6.jpg

// 处理完整URL（不会重复拼接）
const fullPath = 'http://example.com/image.jpg'
const processedUrl = getFileUrl(fullPath)
// 结果: http://example.com/image.jpg
```

## 注意事项

1. 确保后端接口已经更新为新的地址和参数格式
2. 前端域名地址 `http://************:18188` 可能需要根据实际部署环境调整
3. 所有使用文件上传的组件都已经更新，无需额外修改
4. 如果有新的文件上传需求，请使用 `src/api/community.js` 中的 `uploadFile` 函数
