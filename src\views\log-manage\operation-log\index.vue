<template>
  <div>
    <RouteTitle />
    <div class="main-body">
      <div class="main-body-top">
        <el-form
          ref="searchForm"
          :model="mainObj.searchForm"
          label-position="left"
          inline
        >
          <el-form-item label="选择日期：">
            <el-date-picker
              v-model="mainObj.searchForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时刻"
              value-format="yyyy-MM-dd HH:mm:ss"
              end-placeholder="结束时刻"
              :clearable="false"
              :default-time="['00:00:00', '23:59:59']"
            />
          </el-form-item>

          <el-form-item>

            <el-button
              type="primary"
              @click="onSearch"
            >查询</el-button>
            <el-button @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="main-body-bottom">

        <div class="table">
          <el-table
            ref="tableRef"
            :data="mainObj.list"
            fit
            border
            highlight-current-row
            :header-cell-style="{'background': '#F9F9F9'}"
          >
            <el-table-column
              prop="ipAddress"
              label="IP地址"
            />
            <el-table-column
              prop="methodNote"
              label="调用方法"
            />
            <el-table-column
              prop="executionTime"
              label="执行时长（单位毫秒）"
            />
            <el-table-column
              prop="operationTime"
              label="操作时间"
            />
            <el-table-column
              prop="operatorAccount"
              label="操作人账号"
            />
            <el-table-column
              prop="operatorUserName"
              label="操作人姓名"
            />
            <el-table-column
              prop="requestStatusStr"
              label="请求状态"
            />
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button
                  v-if="details"
                  v-preventReClick
                  type="text"
                  @click="getDetailInfo(scope.row)"
                >详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination
            :current-page="mainObj.currentPage"
            :page-size="mainObj.pageSize"
            :page-sizes="mainObj.pagesizes"
            :total="mainObj.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <el-dialog :visible.sync="dialogTableVisible" width="800px" append-to-body title="详情" @close="dialogTableVisible=false">
      <DetailView ref="child" :operation-i-d="infoId" />
    </el-dialog>
  </div>
</template>

<script>
import sysManageApi from '@/api/sysManageApi'
import checkPermission from '@/utils/permission'

import RouteTitle from '@/components/RouteTitle'
import DetailView from './detail/index'

const defaultSearchForm = {
  dateRange: '',
  customerName: '',
  type: ''

}

const defaultUserForm = {
  userName: '',
  mobile: '',
  roleIdList: []
}
export default {
  components: {
    RouteTitle,
    DetailView
  },
  data() {
    return {
      // token: getToken(),
      details: (checkPermission(['requestLog_details'])),

      infoId: '',
      dialogTableVisible: false,
      typeEnums: [],
      mainObj: {
        list: [],
        pageSize: 10,
        pagesizes: [10, 30, 50],
        currentPage: 1,
        total: 0,
        searchForm: { ...defaultSearchForm }
      },
      userDialogObj: {
        visible: false,
        title: '',
        type: '',
        form: Object.assign({}, defaultUserForm)

      },
      value: 1
    }
  },
  created() {
    this.onSearch()
  },
  methods: {
    checkPermission,
    onSearch() {
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    onReset() {
      this.mainObj.searchForm = { ...defaultSearchForm }
      this.fetchData()
    },
    handleSizeChange(val) {
      this.mainObj.pageSize = val
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.mainObj.currentPage = val
      this.fetchData()
    },
    getDetailInfo(row) {
      this.infoId = row.id.toString()
      this.dialogTableVisible = true
      if (this.$refs.child) {
        this.$refs.child.updatedInfo(row.id)
      }
    },
    fetchData() {
      const params = {
        operationStartTime: this.mainObj.searchForm.dateRange ? this.mainObj.searchForm.dateRange[0] : '',
        operationEndTime: this.mainObj.searchForm.dateRange ? this.mainObj.searchForm.dateRange[1] : '',
        pageNum: this.mainObj.currentPage,
        pageSize: this.mainObj.pageSize
      }
      sysManageApi.getOperationLogList(params).then((res) => {
        this.mainObj.list = res.data.list
        this.mainObj.total = res.data.total
      })
    }

  }
}
</script>

<style lang="scss" scoped>

</style>
