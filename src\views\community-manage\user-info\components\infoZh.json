{"basicInfo": "基本信息", "name": "真实姓名", "idCard": "身份证号", "phoneNo": "手机号", "gender": "性别", "age": "年龄", "constellation": "星座", "height": "身高", "weight": "体重", "nativePlace": "籍贯", "presentAddress": "现居地", "mbtiCode": "MBTI", "identityInfo": "身份信息", "score": "钱江分", "emotionStatus": "情感状况", "marrierStatus": {"label": "婚姻状况", "options": ["单身", "", "离异", "已婚"]}, "childStatus": {"label": "子女状况", "options": ["无孩", "有孩"]}, "houseStatus": {"label": "住房情况", "options": ["有房", "无房", "计划购房"]}, "carStatus": {"label": "是否有车", "options": ["无车", "有车", "计划购车"]}, "educationStatus": {"label": "学历", "options": ["专科以下", "专科", "本科", "硕士", "博士"]}, "workUnit": "工作单位", "field": "职业行业", "annualIncome": {"label": "年收入", "options": ["5W以下", "5~10W", "10~20W", "20~30W", "30~50W", "50~100W", "100W以上"]}, "selfIntroduction": "自我介绍", "interestTags": "兴趣爱好", "mateInfo": "择偶要求", "ageOpt": "年龄", "heightOpt": "身高", "educationOpt": "学历", "nativeOpt": "籍贯", "presentAddressOpt": "现居地", "marrierStatusOpt": {"label": "婚姻状况", "options": ["不限", "", "未婚", "离异"]}, "houseStatusOpt": {"label": "住房情况", "options": ["有房", "无房", "计划购房", "不限"]}, "childStatusOpt": "子女状况", "carStatusOpt": "是否有车", "annualIncomeOpt": {"label": "年收入", "options": ["不限", "5W以下", "5~10W", "10~20W", "20~30W", "30~50W", "50~100W", "100W以上"]}, "moreInfomation": "更多说明"}