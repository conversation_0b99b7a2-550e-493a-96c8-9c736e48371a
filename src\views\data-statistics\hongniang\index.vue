<template>
  <div>
    <RouteTitle />
    <div class="main-body">
      <div class="main-body-top">
        <el-form
          ref="searchForm"
          :model="mainObj.searchForm"
          label-position="left"
          inline
        >
          <el-form-item label="选择日期：">
            <el-date-picker
              v-model="mainObj.searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              value-format="yyyy-MM-dd"
              end-placeholder="结束日期"
              :clearable="true"
			  :picker-options="pickerOptions"
			  @change="onSearch"
            />
          </el-form-item>

          <el-form-item>

            <el-button
              type="primary"
              @click="onSearch"
            >查询</el-button>
            <el-button @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="main-body-bottom">
		<div class="total-text">
			<span v-show="!this.mainObj.searchForm.dateRange">默认数据统计开始时间：2024年1月26日。</span> 
			累计已同步数：{{totalCount}},  累计已同步去重用户数：{{totalCountGroupByMobile}}。
		</div>
        <div class="table">
          <el-table
            ref="tableRef"
            :data="mainObj.list"
            fit
            border
            highlight-current-row
            :header-cell-style="{'background': '#F9F9F9'}"
          >
			<el-table-column
				label="序号"
				type="index"
				width="50" />
            <el-table-column
              prop="countDate"
              label="日期"
            />
            <el-table-column
              prop="userInfoCount"
              label="用户更新资料同步数"
            />
            <el-table-column
              prop="activityInfoCount"
              label="用户报名同步数"
            />
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination
            :current-page="mainObj.currentPage"
            :page-size="mainObj.pageSize"
            :page-sizes="mainObj.pagesizes"
            :total="mainObj.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { dataSyncDateCount, dataSyncCount } from '@/api/community'
import RouteTitle from '@/components/RouteTitle'

const defaultSearchForm = {
  dateRange: ''
}

export default {
  components: {
    RouteTitle
  },
  data() {
    return {
      totalCount:0,
	  totalCountGroupByMobile:0,
      mainObj: {
        list: [],
        pageSize: 10,
        pagesizes: [10, 30, 50],
        currentPage: 1,
        total: 0,
        searchForm: { ...defaultSearchForm }
      },
	  pickerOptions: {
	  	disabledDate(date) {
			let time = date.getTime()
	  		return (time < new Date("2024,1,26").getTime() || time > new Date().getTime())
	  	}
	  }
    }
  },
  created() {
    this.onSearch()
  },
  methods: {
    onSearch() {
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    onReset() {
      this.mainObj.searchForm = { ...defaultSearchForm }
      this.fetchData()
    },
    handleSizeChange(val) {
      this.mainObj.pageSize = val
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.mainObj.currentPage = val
      this.fetchData()
    },
    fetchData() {
	  let pageNum = this.mainObj.currentPage
      const params = {
        startDate: this.mainObj.searchForm.dateRange ? this.mainObj.searchForm.dateRange[0] : '',
        endDate: this.mainObj.searchForm.dateRange ? this.mainObj.searchForm.dateRange[1] : '',
        pageNum,
        pageSize: this.mainObj.pageSize,
		syncSystem:'hongniang'
      }
	  if(pageNum == 1) {
		dataSyncCount(params).then(res => {
			if(res.data){
				let {totalCount, totalCountGroupByMobile } = res.data
				this.totalCount = totalCount
				this.totalCountGroupByMobile = totalCountGroupByMobile
			}
		})  
	  }
      dataSyncDateCount(params).then((res) => {
        this.mainObj.list = res.data.list
        this.mainObj.total = res.data.total
      })
    }

  }
}
</script>

<style lang="scss" scoped>
	.table {
		margin: 0 20px;
	}
	.total-text {
		line-height: 60px;
		padding-left: 24px;
	}
</style>
