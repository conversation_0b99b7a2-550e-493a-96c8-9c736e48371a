<template>
  <div>
    <el-dialog
      title="审核记录"
      :visible.sync="dialogVisible"
      width="1000px"
      top="4vh"
      :before-close="handleClose"
    >
      <div v-loading="loading" class="detail" style="min-height:360px;">
        <div>
          <div class="detail-head">
            <div style="font-size:16px;">
              <span style="font-weight: bold;">#{{ row.topicName }}#</span>
              <span>&nbsp;&nbsp;{{ row.title }}</span>
            </div>
          </div>
          <div v-if="(applyList.length > 0)" class="content">
            <el-tabs v-model="activeName" type="card">
              <el-tab-pane v-for="item in applyList" :key="item.lid" :label="item.applUserId === 'robot' ? '机器审核' : '人工审核'" :name="item.id">
                <div v-if="item.applUserId === 'robot'">
                  申请编号：<span>{{ item.applId }}</span>
                  <span v-if="item.applRemark">&nbsp;&nbsp;&nbsp;&nbsp;审核备注：{{ item.applRemark }}</span>
                  <span>&nbsp;&nbsp;&nbsp;审核时间：{{ item.applTime }}</span>
                  <span>&nbsp;&nbsp;&nbsp;审核用户id：{{ item.applUserId }}</span>
                  <span>&nbsp;&nbsp;&nbsp;审核状态：{{ transStatus(item.status) }}</span>
                </div>
                <div v-else>
                  <p>申请编号：<span>{{ item.applId }}</span></p>
                  <p v-if="item.applRemark">审核备注：{{ item.applRemark }}</p>
                  <p v-if="item.applUserId">审核用户id：{{ item.applUserId }}</p>
                  <p>审核时间：{{ item.applTime }}</p>
                  <p>审核状态：{{ transStatus(item.status) }}</p>
                </div>
                <el-table
                  v-if="item.resp"
                  :data="item.resp"
                  border
                  style="width: 100%;margin-top: 10px;"
                  max-height="500"
                >
                  <el-table-column
                    prop="content"
                    label="审核内容"
                    width="180"
                  >
                    <template slot-scope="scope">
                      <span>
                        <p style="text-align: left">内容：{{ scope.row.content }}</p>
                        <p v-if="scope.row.fileUrl" style="border-top: 1px solid #ececec;padding-top: 15px;text-align: left">文件url：{{ scope.row.fileUrl }}</p>
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="createTime"
                    label="请求时间"
                    width="180"
                  />
                  <el-table-column
                    prop="id"
                    label="机审流水id"
                  />
                  <el-table-column
                    prop="resp"
                    label="请求响应"
                    width="250"
                  >
                    <template slot-scope="scope">
                      <span>
                        {{ scope.row.resp }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="respStatus"
                    label="检测状态"
                    width="120"
                  />
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </div>
          <div v-else>
            暂无审核记录！
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getPostDetailApplys } from '@/api/community'
export default {
  name: 'PostsRecord',
  props: {
    row: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isloading: false,
      imgIndex: 0,
      showViewer: false,
      loading: true,
      postObj: null,
      tableData: [],
      dialogVisible: false,
      ruleForm: {
        applRemark: ''
      },
      applyList: [],
      activeName: ''
    }
  },
  computed: {
    transImg() {
      return imgList => {
        return imgList.map(item => item.url)
      }
    },
    transStatus() {
      return status => {
        return {
          12: '已上架',
          5: '已下架',
          10: '待审核',
          11: '审核拒绝',
          99: '已删除'
        }[status]
      }
    }
  },

  mounted() {},

  methods: {
    chnageImg(index) {
      this.imgIndex = index
      this.showViewer = true
      document.body.style.overflow = ''
    },
    onPreview() {
      this.showViewer = true
    },
    // 关闭查看器
    closeViewer() {
      this.showViewer = false
    },
    async getPostDetailApplys() {
      this.postObj = null
      this.loading = true
      const data = await getPostDetailApplys({ postId: this.row.id })
      const { data: { applyList }} = data
      console.log(applyList)
      this.applyList = applyList.length > 0 ? applyList : []
      this.activeName = applyList.length > 0 ? applyList[0].id : ''
      this.loading = false
    },
    handleClose() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__footer {
  text-align: right;
}

.detail {
  width: 100%;
  box-sizing: border-box;
  padding:10px 20px;
}

.line {
  text-align: center;
}

::v-deep .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
 ::v-deep .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
 ::v-deep .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 110px;
    line-height: 110px;
    text-align: center;
  }
 ::v-deep .avatar {
    width: 120px;
    height: 110px;
    display: block;
  }

  ::v-deep .el-upload__tip{
      color:#999;
      margin-top: -9px;
  }
  ::v-deep .el-tabs__nav-scroll {
    width: 950px !important;
    overflow-x: auto !important;
  }
  .detail-head{
    line-height: 2.4;
    border-bottom:1px solid #ddd;
  }

  ::v-deep .el-dialog__body{
    padding:0;
  }

  .content{
    padding:20px 20px 0;
    line-height: 1.2;
    font-size:14px;
  }

  .imgList {
    ul {
      padding:0;
      li {
      list-style:none;
      }
    }
  }

  .video{
    width:440px;
    height:240px;
    margin:0 auto;
  }

  .imgLi{
    height:140px;
    width:100%;
    object-fit: cover;
    margin-bottom:8px;
    border-radius: 4px;
  }

  .demo-ruleForm{
    margin-top:20px;
  }
</style>
