import request from '@/utils/request'

// 获取枚举值
export function getTypeEnum(data) {
  return request({
    url: '/common/get/enum',
    method: 'get',
    params: data
  })
}

// 用户-获取列表
export function getUserList(data) {
  return request({
    url: '/sys/user/search',
    method: 'post',
    data
  })
}

// 用户-新增/修改
export function saveUser(data, type) {
  const urlObj = {
    'add': 'sys/user/add',
    'update': 'sys/user/modify'
  }
  return request({
    url: urlObj[type],
    method: 'post',
    data
  })
}

// 用户-删除-单个
export function deleteUser(data) {
  return request({
    url: '/sys/user/delete',
    method: 'post',
    data
  })
}

// 用户-删除-多个
export function deleteUserBatch(data) {
  return request({
    url: '/sys/user/delete/batch',
    method: 'post',
    data
  })
}

// 用户-详情
export function getUserInfo(params) {
  return request({
    url: '/sys/user/getUserDetail',
    method: 'get',
    params
  })
}

// 用户-重置密码
export function resetUserPassword(data) {
  return request({
    url: '/sys/user/resetPwd',
    method: 'post',
    data
  })
}
// 用户-修改密码
export function modifyPassword(data) {
  return request({
    url: '/sys/user/modifyPwd',
    method: 'post',
    data
  })
}
// 用户-启用/停用
export function enableOrDisableUser(data) {
  return request({
    url: '/sys/user/disable/enable',
    method: 'post',
    data
  })
}

// 用户-解锁
export function unlockUser(data) {
  return request({
    url: '/sys/user/unlock',
    method: 'post',
    data
  })
}

// 角色-获取有效角色
export function getValidRole() {
  return request({
    url: '/sys/role/getEnabledRoles',
    method: 'get'
  })
}

// 角色-获取列表
export function getRoleList(data) {
  return request({
    url: '/sys/role/search',
    method: 'post',
    data
  })
}

// 角色-新增/修改
export function saveRole(data, type) {
  const urlObj = {
    'add': 'sys/role/add',
    'update': 'sys/role/modify'
  }
  return request({
    url: urlObj[type],
    method: 'post',
    data
  })
}

// 角色-删除
export function deleteRole(data) {
  return request({
    url: '/sys/role/delete',
    method: 'post',
    data
  })
}

// 角色-详情
export function getRoleInfo(params) {
  return request({
    url: '/sys/role/getRoleDetail',
    method: 'get',
    params
  })
}

// 获取权限树
export function getPermissionTree() {
  return request({
    url: '/sys/role/getPermTree',
    method: 'get'
  })
}

// ----------------------参数管理-------------------------
// 参数管理 - 获取参数列表
export function getConfigList(data) {
  return request({
    url: '/sys/config/search',
    method: 'post',
    data
  })
}
// 参数管理 - 新增参数
export function addConfig(data) {
  return request({
    url: '/sys/config/add',
    method: 'post',
    data
  })
}
// 参数管理 - 修改参数
export function modifyConfig(data) {
  return request({
    url: '/sys/config/modify',
    method: 'post',
    data
  })
}
// 参数管理 - 删除参数
export function deleteConfig(data) {
  return request({
    url: '/sys/config/delete',
    method: 'post',
    data
  })
}
// ----------------------参数管理-------------------------

// ----------------------字典管理-------------------------
// 获取字典列表
export function getDictList(data) {
  return request({
    url: '/sys/dict/search',
    method: 'post',
    data
  })
}
// 新增字典
export function addDict(data) {
  return request({
    url: '/sys/dict/add',
    method: 'post',
    data
  })
}
// 修改字典
export function modifyDict(data) {
  return request({
    url: '/sys/dict/modify',
    method: 'post',
    data
  })
}
// 字典删除
export function deleteDict(data) {
  return request({
    url: '/sys/dict/delete',
    method: 'post',
    params: data
  })
}
// ----------------------字典管理-------------------------

// 图片管理 - 查询/获取图片列表
export function getImageList(data) {
  return request({
    url: '/sys/image/search',
    method: 'post',
    data
  })
}
// 图片管理 - 新增修改图片
export function saveImage(data, type) {
  const urlObj = {
    'add': '/sys/image/add',
    'update': 'sys/image/modify'
  }
  return request({
    url: urlObj[type],
    method: 'post',
    data
  })
}
// 图片管理 - 删除图片
export function deleteImage(data) {
  return request({
    url: '/sys/image/delete',
    method: 'post',
    params: data
  })
}

// 图片管理 - 获取话题列表
export function getImageTopics() {
  return request({
    url: '/sys/image/getOnShelfTopics',
    method: 'post'
  })
}

// 图片管理 - 获取获取轮播图数量
export function getImageNum() {
  return request({
    url: '/sys/image/getImageNum',
    method: 'post'
  })
}

// 操作日志
export function getOperationLogList(data) {
  return request({
    url: '/request-log/list',
    method: 'post',
    data
  })
}
// 操作日志详情
export function getOperationLogDetail(data) {
  return request({
    url: '/request-log/detail',
    method: 'get',
    params: data
  })
}
// 展台列表查询
export function searchBoothList(data) {
  return request({
    url: '/communityBooth/searchBoothList',
    method: 'post',
    data
  })
}
// 查询当前展示展台
export function firstBooth(data) {
  return request({
    url: '/communityBooth/firstBooth',
    method: 'post',
    data
  })
}
// 下架当前展台
export function takeOffBooth(data) {
  return request({
    url: '/communityBooth/takeOffBooth',
    method: 'post',
    data
  })
}
// 更换展台
export function changeBooth(data) {
  return request({
    url: '/communityBooth/changeBooth',
    method: 'post',
    data
  })
}

// ----------------------菜单管理-------------------------

// 菜单列表查询
export function getMenuList(data) {
  return request({
    url: '/sys/menu/menuListQuery',
    method: 'post',
    data
  })
}

// 修改菜单
export function addOrModifyMenu(data, url) {
  if (url === 'modifyMenu') {
    // 使用新的修改菜单接口
    return request({
      url: '/activity-manager-api/sys/menu/modifyMenu',
      method: 'post',
      data
    })
  } else {
    // 新增菜单使用原接口
    return request({
      url: `/sys/menu/${url}`,
      method: 'post',
      data
    })
  }
}

// 删除菜单
export function delMenu(params) {
  return request({
    url: '/sys/menu/delMenu',
    method: 'post',
    params
  })
}

// 菜单详情查询
export function getMenuDetail(params) {
  return request({
    url: '/sys/menu/menuDetailQuery',
    method: 'get',
    params
  })
}

export default {
  getMenuList,
  addOrModifyMenu,
  delMenu,
  getMenuDetail,
  searchBoothList,
  firstBooth,
  takeOffBooth,
  changeBooth,
  getTypeEnum,
  //  getFileList,
  //  uploadSingleFile,
  //  uploadSingleImage,
  //  uploadForeverFile,
  //  deleteFile,
  //  downloadFileStream,
  getUserList,
  saveUser,
  deleteUser,
  deleteUserBatch,
  getUserInfo,
  resetUserPassword,
  enableOrDisableUser,
  unlockUser,
  getValidRole,
  getRoleList,
  saveRole,
  deleteRole,
  getRoleInfo,
  getPermissionTree,
  modifyPassword,
  getConfigList,
  addConfig,
  modifyConfig,
  deleteConfig,
  getDictList,
  addDict,
  modifyDict,
  deleteDict,
  getImageList,
  saveImage,
  deleteImage,
  getImageTopics,
  getImageNum,
  getOperationLogList,
  getOperationLogDetail
}
