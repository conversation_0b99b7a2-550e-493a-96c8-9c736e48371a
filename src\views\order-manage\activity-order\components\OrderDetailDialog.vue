<template>
  <el-dialog
    title="订单详情"
    :visible.sync="dialogVisible"
    width="800px"
    @close="handleClose"
  >
    <div v-loading="loading" class="order-detail">
      <table class="detail-table">
        <tr>
          <td class="label">订单号</td>
          <td class="value">{{ orderDetail.id || '-' }}</td>
          <td class="label">订单状态</td>
          <td class="value">
            <el-tag :type="getOrderStatusType(orderDetail.orderStatus)">
              {{ getOrderStatusText(orderDetail.orderStatus) }}
            </el-tag>
          </td>
        </tr>
        <tr>
          <td class="label">活动ID</td>
          <td class="value">{{ orderDetail.actId || '-' }}</td>
          <td class="label">活动名称</td>
          <td class="value">{{ orderDetail.actTitle || '-' }}</td>
        </tr>
        <tr>
          <td class="label">活动类型</td>
          <td class="value">{{ orderDetail.typeTitle || '-' }}</td>
          <td class="label">活动名额</td>
          <td class="value">{{ orderDetail.numRage === 0 ? '不限制' : (orderDetail.numRage || '-') }}</td>
        </tr>
        <tr>
          <td class="label">用户姓名</td>
          <td class="value">{{ orderDetail.registerName || '-' }}</td>
          <td class="label">手机号</td>
          <td class="value">{{ orderDetail.registerTel || '-' }}</td>
        </tr>
        <tr>
          <td class="label">行内客户号</td>
          <td class="value">{{ orderDetail.inBankNo || '-' }}</td>
          <td class="label">身份证号</td>
          <td class="value">{{ orderDetail.registerIdCard || '-' }}</td>
        </tr>
        <tr>
          <td class="label">报名人数</td>
          <td class="value">{{ orderDetail.registrantsNum || '-' }}</td>
          <td class="label">报名时间</td>
          <td class="value">{{ formatTime(orderDetail.registerTime) }}</td>
        </tr>
        <tr>
          <td class="label">活动时间</td>
          <td class="value" colspan="3">{{ formatActivityTime(orderDetail.actStartTime, orderDetail.actEndTime) }}</td>
        </tr>
      </table>

    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">返回</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getOrderDetail } from '@/api/orderManage'
import { parseTime } from '@/utils'

export default {
  name: 'OrderDetailDialog',
  filters: {
    parseTime
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    orderId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      orderDetail: {}
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val && this.orderId) {
        this.getDetail()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    getDetail() {
      if (!this.orderId) return

      this.loading = true
      getOrderDetail(this.orderId).then(response => {
        if (response.data) {
          this.orderDetail = response.data
          this.orderDetail = {
            ...this.orderDetail
          }
        }
        this.loading = false
      }).catch(error => {
        console.error('获取订单详情失败:', error)
        this.loading = false
        this.$message.error('获取订单详情失败')
      })
    },
    handleClose() {
      this.dialogVisible = false
      this.orderDetail = {}
    },
    formatTime(time) {
      if (!time) return '-'
      return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
    },
    formatActivityTime(startTime, endTime) {
      if (!startTime || !endTime) return '-'
      const start = parseTime(startTime, '{y}-{m}-{d} {h}:{i}:{s}')
      const end = parseTime(endTime, '{y}-{m}-{d} {h}:{i}:{s}')
      return `${start} ~ ${end}`
    },
    getOrderStatusType(status) {
      const statusMap = {
        '待签到': 'warning',
        '已取消': 'danger',
        '已签到': 'success'
      }
      return statusMap[status] || 'info'
    },
    getOrderStatusText(status) {
      // API直接返回中文状态，直接使用
      return status || '未知'
    },
    getFieldLabel(key) {
      const labelMap = {
        age: '年龄',
        gender: '性别',
        idType: '证件类型',
        community: '社区',
        address: '地址',
        height: '身高',
        weight: '体重',
        education: '学历',
        constellation: '星座',
        remark: '备注'
      }
      return labelMap[key] || key
    }
  }
}
</script>

<style scoped>
.order-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.detail-table td {
  padding: 12px 15px;
  border: 1px solid #EBEEF5;
  vertical-align: middle;
}

.detail-table .label {
  background-color: #FAFAFA;
  color: #909399;
  font-weight: bold;
  width: 120px;
  text-align: right;
}

.detail-table .value {
  color: #606266;
  background-color: #FFFFFF;
}

.detail-table tr:hover {
  background-color: #F5F7FA;
}
</style>
