<template>
  <div v-if="dialogVisible">
    <el-dialog
      title="用户详情"
      :visible.sync="dialogVisible"
      width="1150px"
      top="4vh"
      :before-close="handleClose"
    >
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="基本信息" name="first">
          <div class="box">
            <div class="box-left">
              <img :src="row.avatar ? row.avatar : require('../../../assets/pic_boy.png')" alt="">
            </div>
            <div class="box-right">
              <div>
                <span>用户ID：{{ row.id }}</span>
                <span v-if="row.status === '1'" style="display:inline-block;margin-left:100px;text-align:left;">拉黑时间： {{ row.banTime }}</span>
              </div>

              <div>
                用户状态：<span :style="row.status === '0' ? 'color:#056de8;': 'color:#ff5050;'">{{ row.statusName }}</span>
                <span v-if="row.status === '1'" style="display:inline-block;margin-left:55px;text-align:left;">拉黑理由：{{ row.reason }}</span>
              </div>

              <div>用户姓名：{{ row.name }} </div>

              <div>用户昵称：{{ row.nickName }} </div>

              <div>发布数：{{ row.postNum }}</div>

              <div>手机号：{{ row.mobile }}   </div>

              <!-- <div>身份证号：{{ row.idNo }}</div> -->
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="发布列表" name="second">
          <div>
            <div>
              <div class="body-top-form">
                <el-form
                  ref="searchForm"
                  class="left"
                  :model="mainObj.searchForm"
                  label-position="left"
                  inline
                >
                  <el-form-item label="发帖人：">
                    <el-select
                      v-model="mainObj.searchForm.userId"
                      placeholder="请选择"
                      clearable
                    >
                      <el-option label="全部" value="" />
                      <el-option v-for="(item, index) in userList" :key="index" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button @click="onReset">重置</el-button>
                  </el-form-item>
                </el-form>
              </div>
              <div class="table">
                <el-table
                  ref="tableRef"
                  :data="mainObj.postList"
                  fit
                  border
                  highlight-current-row
                  size="small"
                  :header-cell-style="{ background: '#F9F9F9' }"
                >
                  <el-table-column prop="ipAddress" label="序号" width="50">
                    <template slot-scope="scope">
                      <span>{{
                        mainObj.pageSize *
                          (mainObj.currentPage === 1 ? 0 : mainObj.currentPage) +
                          scope.$index +
                          1
                      }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="id" label="帖子ID" show-overflow-tooltip />
                  <el-table-column prop="status" label="状态" />
                  <el-table-column prop="title" label="帖子标题" width="200" show-overflow-tooltip />
                  <el-table-column prop="topicName" label="所属话题" width="200" show-overflow-tooltip />
                  <el-table-column prop="likeNum" label="点赞数" />
                  <el-table-column prop="commentNum" label="评论数" />
                  <el-table-column prop="viewNum" label="浏览量" />
                  <el-table-column prop="createUserName" label="发帖人" />
                  <el-table-column prop="createTime" label="发帖时间" width="150" />
                  <!-- <el-table-column label="操作" fixed="right" width="180">
                    <template slot-scope="scope">
                      <el-button v-if="scope.row.status === '1'" type="text" @click="operation('examine', scope.row)">解除封禁</el-button>
                      <el-button v-if="scope.row.status === '0'" type="text" style="color:#e6a23c;" @click="operation('block', scope.row)">拉黑</el-button>
                      <el-button type="text" @click="operation('view', scope.row)">查看</el-button>
                    </template>
                  </el-table-column> -->
                </el-table>
              </div>
              <div class="pagination">
                <el-pagination
                  :current-page="mainObj.currentPage"
                  :page-size="mainObj.pageSize"
                  :page-sizes="mainObj.pageSizes"
                  :total="mainObj.total"
                  layout="total, sizes, prev, pager, next, jumper"
                  background
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="handleClose">返 回</el-button> -->
      </span>
    </el-dialog>
  </div>
</template>

<script>
const defaultSearchForm = {
  userId: ''
}

import { getPostList } from '@/api/community'
import { getUserDict } from '@/api/communityUser'
export default {
  name: 'CommunityUserDetail',
  props: {
    type: {
      type: String,
      default: ''
    },
    row: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      userList: [],
      activeName: 'first',
      dialogVisible: false,
      mainObj: {
        postList: [],
        pageSize: 10,
        pageSizes: [10, 30, 50],
        currentPage: 1,
        total: 0,
        searchForm: { ...defaultSearchForm }
      }
    }
  },
  computed: {

  },
  mounted() {
  },

  methods: {
    getUserList(data) {
      getUserDict({ id: data.id }).then(res => {
        this.userList = res.data
        this.onSearch()
      })
    },
    handleClick() {

    },
    handleClose() {
      this.mainObj.searchForm = {
        userId: ''
      }
      this.dialogVisible = false
    },
    onSearch() {
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    onReset() {
      this.mainObj.searchForm = { ...defaultSearchForm }
      this.fetchData()
    },
    handleSizeChange(val) {
      this.mainObj.pageSize = val
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.mainObj.currentPage = val
      this.fetchData()
    },
    getDetailInfo(row) {
      this.infoId = row.id.toString()
      this.dialogTableVisible = true
      if (this.$refs.child) {
        this.$refs.child.updatedInfo(row.id)
      }
    },
    fetchData() {
      const params = {
        userId: this.mainObj.searchForm.userId,
        userList: this.userList,
        pageNum: this.mainObj.currentPage,
        pageSize: this.mainObj.pageSize
      }
      getPostList(params).then(res => {
        this.mainObj.postList = res.data.list
        this.mainObj.total = Number(res.data.total)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__footer {
  text-align: right;
}

.detail {
  width: 100%;
  box-sizing: border-box;
  padding: 0 70px 0 0;
}

.line {
  text-align: center;
}

::v-deep .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
 ::v-deep .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
 ::v-deep .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 110px;
    line-height: 110px;
    text-align: center;
  }
 ::v-deep .avatar {
    width: 120px;
    height: 110px;
    display: block;
  }

  ::v-deep .el-upload__tip{
      color:#999;
      margin-top: -9px;
  }

  ::v-deep .el-tabs__nav-wrap::after{
    background:#fff;
  }

  .box-right{
    line-height:2.6;
  }

  .box{
    display: flex;
    align-items: top;
    justify-content: flex-start;
    padding:20px 50px;
  }

  .box-left{
    img {
      width:140px;
      height:140px;
      margin-right:40px;
      border-radius: 50%;
    }
  }
</style>
