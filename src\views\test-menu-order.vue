<template>
  <div style="padding: 20px;">
    <h2>菜单排序和图标测试</h2>

    <div style="margin: 20px 0;">
      <h3>当前用户权限：</h3>
      <div style="background: #f5f5f5; padding: 10px; border-radius: 4px;">
        <pre>{{ JSON.stringify(roles, null, 2) }}</pre>
      </div>
    </div>

    <div style="margin: 20px 0;">
      <h3>后端菜单数据：</h3>
      <div style="background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto;">
        <pre>{{ JSON.stringify(menuList, null, 2) }}</pre>
      </div>
    </div>

    <div style="margin: 20px 0;">
      <h3>生成的路由：</h3>
      <div style="background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto;">
        <pre>{{ JSON.stringify(permissionRoutes, null, 2) }}</pre>
      </div>
    </div>

    <div style="margin: 20px 0;">
      <h3>测试结果：</h3>
      <ul>
        <li>菜单数据加载：{{ menuList.length > 0 ? '✅ 正常' : '❌ 异常' }}</li>
        <li>权限数据生成：{{ roles.length > 0 ? '✅ 正常' : '❌ 异常' }}</li>
        <li>路由生成：{{ permissionRoutes.length > 0 ? '✅ 正常' : '❌ 异常' }}</li>
      </ul>
    </div>

    <div style="margin: 20px 0;">
      <el-button @click="refreshData">刷新数据</el-button>
      <el-button @click="testIconDisplay">测试图标显示</el-button>
    </div>

    <div v-if="showIconTest" style="margin: 20px 0;">
      <h3>图标显示测试：</h3>
      <div style="display: flex; flex-wrap: wrap; gap: 20px;">
        <div
          v-for="route in permissionRoutes"
          :key="route.path"
          style="border: 1px solid #ddd; padding: 10px; border-radius: 4px;"
        >
          <div style="margin-bottom: 10px;">
            <strong>{{ route.meta?.title || route.name }}</strong>
          </div>
          <div style="display: flex; align-items: center; gap: 10px;">
            <svg-icon
              v-if="route.meta?.icon"
              :icon-class="route.meta.icon"
              style="font-size: 20px;"
            />
            <img
              v-else-if="route.meta?.icon_o"
              :src="route.meta.icon_o"
              style="width: 20px; height: 20px;"
              alt=""
            >
            <span v-else>无图标</span>
            <span>{{ route.meta?.icon || '前端图标' }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'TestMenuOrder',
  data() {
    return {
      showIconTest: false
    }
  },
  computed: {
    ...mapGetters([
      'roles',
      'permission_routes'
    ]),
    menuList() {
      return this.$store.state.user.menuList || []
    },
    permissionRoutes() {
      return this.permission_routes.filter(route => !route.hidden)
    }
  },
  methods: {
    refreshData() {
      this.$store.dispatch('user/getInfo').then(() => {
        this.$message.success('数据刷新成功')
      }).catch(error => {
        this.$message.error('数据刷新失败: ' + error)
      })
    },

    testIconDisplay() {
      this.showIconTest = !this.showIconTest
    }
  }
}
</script>
