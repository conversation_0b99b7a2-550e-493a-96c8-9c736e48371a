<!--
	<AUTHOR>
	@date 2022-09-18
	@desc 动态表单 - 列表条件查询
	写法↓
	<dynamic-form
		:data="数据"
		@handleClickFormBtn='@handleClickFormBtn'
	></dynamic-form>
-->
<template>
  <div class="dynamic-form-mod">
    <el-form ref="queryForm" :inline="true">
      <el-form-item
        v-for="(item, index) in data.queryItem"
        :key="index"
        :label="item.label"
        :label-width="item.labelWidth ? item.labelWidth : null"
      >
        <!-- 文本输入框 - 普通文字、数字等 -->
        <el-input
          v-if="item.type == 'text'"
          v-model="queryData[item.model]"
          type="text"
          :maxlength="item.maxlength ? Number(item.maxlength) : null"
          size="small"
          :placeholder="item.placeholder ? item.placeholder : '请输入'"
          clearable
        />

        <!-- 文本框 - 关键词过滤 -->
        <el-autocomplete
          v-if="item.type == 'textFilter'"
          v-model="queryData[item.model]"
          size="small"
          :value-key="item.filterLabel"
          :fetch-suggestions="(queryString, cb) => querySearchAsync(queryString, cb, item)"
          :placeholder="item.placeholder ? item.placeholder : '请输入并选择'"
          clearable
          @select="obj => handleTextSelect(obj, item)"
          @change="value => handleTextChange(value, item)"
        />

        <!-- 下拉框 - 支持选择过滤 -->
        <el-select
          v-if="item.type == 'select'"
          v-model="queryData[item.model]"
          size="small"
          :placeholder="item.placeholder ? item.placeholder : '请选择'"
          :filterable="item.filterable ? item.filterable : null"
          clearable
        >
          <!-- 下拉数据 - list -->
          <template v-if="item.optionType && item.optionType == 'list'">
            <el-option
              v-for="o in item.optionList"
              :key="o[item.optionValue]"
              :label="o[item.optionLabel]"
              :value="o[item.optionValue]"
            />
          </template>
          <!-- 默认 map传参 -->
          <template v-else>
            <el-option
              v-for="k in Object.keys(item.optionList)"
              :key="k"
              :label="item.optionList[k]"
              :value="k"
            />
          </template>
        </el-select>

        <!-- 时间区间 -->
        <el-date-picker
          v-if="item.type == 'date'"
          v-model="queryData[item.model]"
          size="small"
          :range-separator="item.config.separator ? item.config.separator : null"
          :value-format="item.config.valueFormat ? item.config.valueFormat : null"
          :type="item.config.type"
          :placeholder="item.placeholder ? item.placeholder : '请选择'"
          :start-placeholder="item.config.startPlaceholder ? item.config.startPlaceholder : null"
          :end-placeholder="item.config.endPlaceholder ? item.config.endPlaceholder : null"
          :picker-options="item.config.pickerOption ? item.config.pickerOption : null"
        />

        <!-- 操作按钮 - 支持插槽，默认为 查询、重置 -->
        <template v-else-if="item.type == 'option'">
          <!-- 自定义插槽 - 条件传参 -->
          <slot v-if="item.slotName" :name="item.slotName" :queryData="queryData" />

          <!-- 默认 -->
          <template v-else>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="handleSearch"
            >查询</el-button>
            <el-button
              type="plain"
              icon="el-icon-refresh"
              @click="handleReset"
            >重置</el-button>
          </template>
        </template>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'DynamicQuery',
  mixins: [],
  props: {
    // 条件表单字段
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      // 查询字段
      queryData: {},

      // 用于重置覆盖
      queryDataOrigin: {}
    }
  },
  watch: {
    queryData: {
      handler: function() {
        this.$emit('queryChanged', this.queryData)
      },
      deep: true
    }
  },
  mounted() {
    this.initQueryForm()
  },
  methods: {
    /**
		 * 初始化查询条件
		 */
    initQueryForm: function() {
	  let queryFlag = false
      this.queryData = this.data.queryItem.reduce((obj, item) => {
        // 除操作外均进行字段初始化
        if (item.type !== 'option') {
          obj[item.model] = item.defaultValue ? item.defaultValue : ''
		  if(item.defaultValue)queryFlag = true
        }
        return obj // 返回的是 {datePeriod:""}
      }, {})
      this.queryDataOrigin = JSON.parse(JSON.stringify(this.queryData))
	  if(queryFlag) {
		this.handleSearch()
	  }
    },

    /**
		 * 重置表单数据
		 */
    handleReset: function() {
      // this.$refs.queryForm.resetFields()
      // Object.assign(this.queryData, this.$options.data.call(this).queryData);
      this.queryData = { ...this.queryDataOrigin }
      this.$nextTick(() => {
        this.$emit('handleReset')
      })
      // this.handleSearch();
    },

    /**
		 * 表单搜索事件
		 */
    handleSearch: function() {
      this.$emit('search', this.queryData)
    },

    /**
		 * 过滤输入词
		 * @param {Object} queryString 输入词
		 * @param {Object} cb 回调方法
		 * @param {Object} item 相关配置
		 */
    querySearchAsync(queryString, cb, item) {
      const list = item.filterList
      // 存在输入词 -> 过滤列表
      const results = queryString ? list.filter(this.createStateFilter(queryString, item)) : list

      cb(results)
    },
    createStateFilter(queryString, item) {
      return state => {
        return state[item.filterLabel].indexOf(queryString) > -1
      }
    },

    /**
		 * 文本过滤选中
		 * @param {Object} sel   选中对象
		 * @param {Object} item  相关配置
		 */
    handleTextSelect: function(sel, item) {
      if (item.modelKey) {
        this.queryData[item.modelKey] = sel[item.filterValue]
      }
    },
    // 兼容关键词改变和删除
    handleTextChange: function(value, item) {
      this.queryData[item.modelKey] = ''
    }
  }
}
</script>
<style lang="scss" scoped></style>
