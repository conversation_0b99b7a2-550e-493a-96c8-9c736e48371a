<template>
  <el-dialog
    :title="pageType === 'add' ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    @close="dialogClose"
  >
    <el-form
      ref="dataForm"
      :model="dataForm"
      :rules="dataRule"
      label-width="80px"
      @keyup.enter.native="dataFormSubmit()"
    >
      <el-form-item label="菜单名称" prop="menuName">
        <el-input v-model="dataForm.menuName" placeholder="请输入菜单名称" />
      </el-form-item>
      <el-form-item label="上级菜单" prop="pid">
        <el-popover
          ref="menuListPopover"
          placement="bottom-start"
          trigger="click"
        >
          <el-tree
            ref="menuListTree"
            :data="menuList"
            :props="{
              label: 'menuName',
              value: 'menuId',
              children: 'children',
              checkStrictly: true
            }"
            node-key="menuId"
            :default-expand-all="true"
            :highlight-current="true"
            :expand-on-click-node="false"
            style="height: 200px; overflow: auto;"
            @current-change="menuListTreeCurrentChangeHandle"
          />
        </el-popover>
        <el-input
          v-model="dataForm.pMenuName"
          v-popover:menuListPopover
          :readonly="true"
          placeholder="点击选择上级菜单"
          class="menu-list__input"
        />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input v-model="dataForm.sort" placeholder="请输入菜单排序" />
      </el-form-item>
      <el-form-item v-if="pageType==='add'" label="菜单路径" prop="path">
        <el-input v-model="dataForm.path" placeholder="请输入菜单路径" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
// import { treeDataTranslate } from '@/utils'
import { addOrModifyMenu, getMenuDetail } from '@/api/sysManageApi'

export default {
  props: {
    treeData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    var validateUrl = (rule, value, callback) => {
      if (this.dataForm.type === 1 && !/\S/.test(value)) {
        callback(new Error('菜单URL不能为空'))
      } else {
        callback()
      }
    }
    return {
      visible: false,
      pageType: 'add',
      dataForm: {
        menuId: '',
        menuName: '',
        sort: '',
        pid: '',
        path: '',
        pMenuName: ''
      },
      menuList: [],
      dataRule: {
        name: [
          { required: true, message: '菜单名称不能为空', trigger: 'blur' }
        ],
        parentName: [
          { required: true, message: '上级菜单不能为空', trigger: 'change' }
        ],
        url: [{ validator: validateUrl, trigger: 'blur' }]
      }
    }
  },
  created() {},
  methods: {
    init(type, menuId) {
      this.menuList = [...this.treeData]
      this.menuList.unshift({
        menuId: '',
        menuName: '一级菜单',
        children: []
      })
      this.visible = true
      this.pageType = type
      if (menuId) {
        const params = {
          menuId
        }
        getMenuDetail(params).then(res => {
          res.data.pMenuName = res.data.pMenuName === '根' ? '一级菜单' : res.data.pMenuName
          this.dataForm = res.data
        })
      }
    },
    // 菜单树选中
    menuListTreeCurrentChangeHandle(data, node) {
      this.dataForm.pid = data.menuId
      this.dataForm.pMenuName = data.menuName
      this.$refs.menuListPopover.doClose()
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          const url = this.pageType === 'add' ? 'addMenu' : 'modifyMenu'
          let data = {}

          if (this.pageType === 'add') {
            // 新增菜单
            data = {
              ...this.dataForm,
              menuId: this.dataForm.path
            }
          } else {
            // 修改菜单 - 根据新的接口参数要求构造数据
            data = {
              oldMenuId: this.dataForm.menuId, // 修改前的前端地址
              newMenuId: this.dataForm.path || this.dataForm.menuId, // 修改后的前端地址
              menuName: this.dataForm.menuName,
              pid: this.dataForm.pid || '',
              sort: parseInt(this.dataForm.sort) || 1,
              permIcon: '', // 这个组件没有图标字段，设为空
              permDesc: '', // 这个组件没有描述字段，设为空
              serverPermPath: this.dataForm.path || '', // 后台权限地址
              permType: 'MENU' // 默认为菜单类型
            }
          }

          addOrModifyMenu(data, url).then(res => {
            this.$message.success(res.msg)
            this.visible = false
            this.$emit('refreshDataList')
          })
        }
      })
    },
    dialogClose() {
      this.dataForm.pMenuName = ''
      this.$refs['dataForm'].resetFields()
    }
  }
}
</script>

<style lang="scss">
.mod-menu {
  .menu-list__input,
  .icon-list__input {
    > .el-input__inner {
      cursor: pointer;
    }
  }
  &__icon-popover {
    width: 458px;
    overflow: hidden;
  }
  &__icon-inner {
    width: 478px;
    max-height: 258px;
    overflow-x: hidden;
    overflow-y: auto;
  }
  &__icon-list {
    width: 458px;
    padding: 0;
    margin: -8px 0 0 -8px;
    > .el-button {
      padding: 8px;
      margin: 8px 0 0 8px;
      > span {
        display: inline-block;
        vertical-align: middle;
        width: 18px;
        height: 18px;
        font-size: 18px;
      }
    }
  }
  .icon-list__tips {
    font-size: 18px;
    text-align: center;
    color: #e6a23c;
    cursor: pointer;
  }
}
</style>
