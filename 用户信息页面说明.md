# 用户信息管理页面

## 功能概述

用户信息管理页面用于记录和管理活动端的用户信息，支持多种查询条件和详情查看功能。

## 主要功能

### 1. 查询功能
支持以下查询条件：
- **用户ID**（精确搜索）：输入用户ID查询
- **手机号**（精确搜索）：支持输入报名的用户手机号查询
- **客户号**（精确搜索）：可输入南京银行客户号查询
- **姓名**（精确搜索）：支持输入姓名查询
- **身份证号**（精确搜索）：支持输入身份证号查询
- **首次报名时间**：支持按时间段查询

### 2. 列表展示
列表按时间倒序排列，包含以下字段：
- **序号**：自动编序，按照分页器每页所展示条数从1开始编序
- **用户ID**：用户首次登录成功后自动生成，具有唯一性
- **手机号**：用户登录使用的手机号（列表中脱敏显示）
- **参加活动次数**：指用户实际签到次数
- **行内客户号**：南京银行客户号
- **姓名**：南京银行用户实名信息里的姓名（列表中脱敏显示）
- **身份证号码**：南京银行用户实名信息里的身份证号（列表中脱敏显示）
- **首次报名时间**：该手机号第一次报名成功的时间点，格式：yyyy-mm-dd HH:mm:ss
- **操作**：查看详情按钮

### 3. 详情查看
点击"查看详情"可查看用户完整信息，包含：

#### 基本信息（不脱敏）
- 用户ID
- 用户头像
- 手机号
- 参加活动次数

#### 行内信息（红色标注）
- 姓名
- 身份证号
- 年龄
- 性别
- 证件类型

#### 用户填写信息（蓝色标注）
- 社区
- 地址
- 身高
- 体重
- 学历
- 星座

#### 其他信息
- 其它记录时留存的用户信息

## 数据脱敏规则

### 列表页面脱敏
- **手机号**：显示前3位和后4位，中间用****代替（如：188****5678）
- **姓名**：只显示第一个字符，其余用*代替（如：张*）
- **身份证号**：显示前6位和后4位，中间用********代替（如：320123********5678）

### 详情页面不脱敏
详情页面和导出功能中所有信息均不脱敏，显示完整数据。

## 用户信息展示规则

1. **手机号**：以登录手机号为准
2. **未报名成功前**：仅记录登录手机号和头像，其他信息不记录（包括姓名）
3. **报名成功后**：
   - 以行方返回信息为准（红色标注）
   - 行方未返回的以用户报名时自主填写为准（蓝色标注）
   - 用户未填写的显示为空

## 技术实现

### 文件结构
```
src/views/user-info/
├── index.vue                          # 主页面
└── components/
    └── UserDetailDialog.vue           # 用户详情弹窗组件

src/api/
└── custManageApi.js                   # 用户信息相关API接口

src/utils/
└── common.js                          # 数据脱敏工具函数
```

### API接口

#### 1. 查询用户信息列表
- **接口地址**：`/activity-manager-api/cust/manage/getList`
- **请求方式**：POST
- **入参**：
  - `custUserId`：用户id（可选）
  - `mobile`：手机号（可选）
  - `custNo`：行内客户号（可选）
  - `name`：姓名（可选）
  - `custIdentNo`：身份证（可选）
  - `startTime`：首次报名查询开始时间（可选）
  - `endTime`：首次报名查询结束时间（可选）
  - `pageNum`：分页参数（可选）
  - `pageSize`：分页参数（可选）

#### 2. 查看用户详情
- **接口地址**：`/activity-manager-api/cust/manage/getDetail`
- **请求方式**：POST
- **入参**：`userId`（用户id，必填）

### 路由配置
页面路由已添加到社区管理菜单下：
```javascript
{
  path: '/user-info/index',
  component: () => import('@/views/user-info'),
  name: 'ActivityUserInfo',
  meta: {
    title: '用户信息',
    icon_o: require('@/assets/menu/Group_s.svg'),
    icon_c: require('@/assets/menu/Group_n.svg')
  }
}
```

## 使用说明

1. **访问页面**：在系统菜单中找到"社区管理" -> "用户信息"
2. **查询用户**：在搜索表单中输入查询条件，点击"查询"按钮
3. **重置条件**：点击"重置"按钮清空所有查询条件
4. **查看详情**：点击列表中的"查看详情"按钮查看用户完整信息
5. **分页浏览**：使用页面底部的分页组件浏览不同页面的数据

## 注意事项

1. 列表中的敏感信息（手机号、姓名、身份证号）会进行脱敏处理
2. 详情弹窗中显示完整的用户信息，不进行脱敏
3. 时间查询支持精确到秒的时间范围选择
4. 所有查询条件均为精确匹配
5. 列表按首次报名时间倒序排列
