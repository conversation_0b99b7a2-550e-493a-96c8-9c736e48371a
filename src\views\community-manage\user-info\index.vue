<template>
  <div>
    <RouteTitle />
    <!-- 条件查询 -->
    <div class="main-body">
      <div class="main-body-top">
        <!-- 页面条件搜索 -->
        <dynamic-query
          :data="queryConfig"
          @queryChanged="handleQueryDataChanged"
          @search="handleSearch"
        />
        <!-- 页面列表数据 + 分页条 -->
        <dynamic-table
          :config="tableConfig"
          :data="tableData"
          :is-loading.sync="tableLoading"
          @pagination="handlePageChange"
        >
          <template #userRoleText="{ rowData, index }">
            {{ rowData.userRole == '2' ? '家长' : '本人' }}
          </template>

          <template v-slot:operate="{ rowData, index }">
            <el-button
              type="text"
              @click="handleMore(rowData)"
            >查看更多信息</el-button>
            <el-button
              type="text"
              @click="handleInfoEdit(rowData)"
            >认证信息变更</el-button>
          </template>
        </dynamic-table>
      </div>
    </div>
    <!-- 配置dialog -->
    <MoreDialog
      v-if="dialogVisible && dialogType != 2"
      :dialog-visible="dialogVisible"
      :config="dialogConfig"
      @handleClose="handleClose"
    />
    <more-dialog-parent
      v-if="dialogVisible && dialogType == 2"
      :dialog-visible="dialogVisible"
      :config="dialogConfig"
      @handleClose="handleClose"
    />
  </div>
</template>

<script>
import RouteTitle from '@/components/RouteTitle'
import DynamicQuery from '@/components/dynamic/Query.vue'
import DynamicTable from '@/components/dynamic/Table.vue'
import MoreDialog from './components/MoreDialog.vue'

import mixin from '../../mixin'
import MoreDialogParent from './components/MoreDialogParent.vue'
const statusMap = {
  1: '待审核',
  2: '审核通过',
  3: '审核不通过'
}
export default {
  components: {
    RouteTitle,
    DynamicQuery,
    DynamicTable,
    MoreDialog,
    MoreDialogParent
  },
  mixins: [mixin],
  data() {
    return {
      dialogVisible: false,
      dialogType: '',
      queryUrl: '/communityUser/getUserList',
      dialogConfig: {},
      queryConfig: {
        queryItem: [
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '用户ID:',
            model: 'userId',
            clearable: true, // 默认 true
            size: 'small' // 默认 small
          },
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '真实姓名:',
            model: 'name',
            clearable: true, // 默认 true
            size: 'small' // 默认 small
          },
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '身份证号:',
            model: 'idCard',
            clearable: true, // 默认 true
            size: 'small' // 默认 small
          },
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '手机号:',
            model: 'phoneNo',
            clearable: true, // 默认 true
            size: 'small' // 默认 small
          },
          {
            type: 'date', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '注册时间:',
            model: 'userDateArr',
            config: {
              type: 'datetimerange', // 时间区间 - 返回数组 需业务重写字段
              separator: '-',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
              format: 'yyyy-MM-dd HH:mm:ss'
            }
          },
          {
            type: 'option'
          }
        ]
      },
      statusMap: statusMap,
      userDateArr: [],
      // 列表字段
      tableConfig: {
        tableColumn: [
          {
            prop: '',
            label: '序号',
            isIndex: true
          },
          {
            prop: 'userId',
            label: '用户ID'
          },
          {
            prop: 'userRole',
            label: '用户身份',
            slotName: 'userRoleText'
          },
          {
            prop: 'name',
            label: '真实姓名'
          },
          {
            prop: 'idCard',
            label: '身份证号'
          },
          {
            prop: 'phoneNo',
            label: '手机号'
          },
          {
            prop: 'registerTime',
            label: '注册时间'
          },
          {
            prop: '',
            label: '操作',
            slotName: 'operate', // 操作 - 自定义操作，和operateList二选一
            fixed: 'right',
            minWidth: 160
          }
        ]
      },

      // 列表数据
      tableData: {
        list: [],
        // 分页数据
        pageTotal: 0, // 列表总数
        pageSize: 10, // 每页条数
        pageNum: 1 // 当前页码
      },
      exampleVisible: false,
      exampleData: {
        id: '',
        auditDetail: '',
        auditStatus: null
      }
    }
  },
  methods: {
    handleMore(rowData) {
      this.dialogVisible = true
      this.dialogType = rowData.userRole
      this.dialogConfig = {
        title: '查看更多',
        userId: rowData.userId
      }
    },
    handleInfoEdit(rowData) {
      this.dialogVisible = true
      this.dialogType = rowData.userRole
      this.dialogConfig = {
        title: '认证信息变更',
        userId: rowData.userId,
        isEdit: true
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
