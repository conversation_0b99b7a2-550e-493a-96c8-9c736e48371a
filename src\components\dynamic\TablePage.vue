<!--
	<AUTHOR>
	@date 2022-09-18
	@desc 动态表格列表 - 数据反显 & 分页集成
	写法↓
	<dynamic-table-page :data="数据"></dynamic-table-page>
-->
<template>
  <div class="demo-mod">
    <!-- 页面条件搜索 -->
    <dynamic-query
      :data="data.queryConfig"
      @queryChanged="handleQueryDataChanged"
      @search="handleSearch"
      @handleReset="handleReset"
    >
      <!-- 重渲染插槽 - 自定义按钮 -->
      <template
        v-for="(item, index) in data.queryConfig.queryItem.filter(o => o.slotName)"
        v-slot:[item.slotName]="{ queryData }"
      >
        <slot :name="item.slotName" :queryData="queryData" />
      </template>
    </dynamic-query>

    <!-- 页面规操作区 - 具名插槽 -->
    <slot name="pageOperate" />

    <!-- 页面列表数据 + 分页条 -->
    <dynamic-table
      :config="data.tableConfig"
      :data="data.tableData"
      :is-loading.sync="isLoading"
      @table-current-change="handleCurrentChange"
      @table-selection-change="handleSelectionChange"
      @pagination="handlePageChange"
    >
      <!-- 表头插槽 -->
      <template
        v-for="(item, index) in data.tableConfig.tableColumn.filter(o => o.slotName)"
        v-slot:[getHeaderSlot(item.slotName)]
      >
        <slot :name="item.slotName + 'Header'" />
      </template>

      <!-- 操作按钮插槽 -->
      <template
        v-for="(item, index) in data.tableConfig.tableColumn.filter(o => o.slotName)"
        v-slot:[item.slotName]="{ data, $index, rowData }"
      >
        <slot :name="item.slotName" :data="data" :index="$index" :rowData="rowData" />
      </template>
    </dynamic-table>
  </div>
</template>

<script>
import DynamicTable from './Table.vue'
import DynamicQuery from './Query.vue'
export default {
  name: 'DynamicTablePage',
  components: {
    DynamicTable,
    DynamicQuery
  },
  props: {
    // 页面数据
    data: {
      type: Object,
      default: () => {}
    },
    // 数据获取中
    isLoading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  mounted() {},
  methods: {
    /**
		 * 查询条件更改
		 * @param {Object} queryData
		 */
    handleQueryDataChanged: function(queryData) {
      this.$emit('queryChanged', queryData)
    },

    /**
		 * 条件查询时触发该事件
		 */
    handleSearch: function(queryData) {
      this.$emit('search', queryData)
    },

    handleReset: function() {
      this.$emit('handleReset')
    },

    /**
		 * 当表格的当前行发生变化的时候会触发该事件
		 * @param {Object} currentRow
		 */
    handleCurrentChange: function(currentRow) {
      this.$emit('table-current-change', currentRow)
    },

    /**
		 * 当选择项发生变化时会触发该事件
		 * @param {Object} selection
		 */
    handleSelectionChange: function(selection) {
      this.$emit('table-selection-change', selection)
    },

    /**
		 * 分页条页码/每页条数改变
		 * @param {Object} obj { page: 当前页码, limit: 每页条数 }
		 */
    handlePageChange: function(obj) {
      this.$emit('pagination', obj)
    },

    getHeaderSlot: function(name) {
      return name + 'Header'
    }
  }
}
</script>
<style lang="scss" scoped></style>
