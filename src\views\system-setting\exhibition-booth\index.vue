<template>
  <div>
    <RouteTitle />
    <div class="main-body">
      <div class="main-body-bottom">
        <div class="body-top-form">
          <el-form
            ref="showBox"
            style="max-width:800px"
            label-width="100px"
            label-position="left"
            class="left"
          >
            <el-form-item label="当前展台名称">
              <div class="show-box">{{ boothData.boothName }}</div>
            </el-form-item>
            <el-form-item label="当前展台链接">
              <div class="show-box" style="word-wrap: break-word;">{{ boothData.boothUrl }}</div>
            </el-form-item>
            <el-form-item class="right">
              <el-button
                @click="unBooth"
              >下架展台</el-button>
              <el-button
                type="primary"
                @click="updateBooth"
              >更换展台</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="body-search-form">
          <div class="title">历史展台</div>
          <el-form
            ref="searchForm"
            :model="mainObj.searchForm"
            label-width="90px"
            label-position="left"
            class="left"
            inline
          >
            <el-form-item label="展台名称">
              <el-input
                v-model.trim="mainObj.searchForm.boothName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
            <el-form-item label="上架时间：">
              <el-date-picker
                v-model="mainObj.searchForm.putOnAt"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择"
              />
            </el-form-item>
            <el-form-item label="下架时间：">
              <el-date-picker
                v-model="mainObj.searchForm.takeOffAt"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择"
              />
            </el-form-item>
            <!-- <el-form-item label="角色编码">
            <el-input
              v-model.trim="mainObj.searchForm.roleCode"
              placeholder="请输入"
              clearable
            /> -->
            <!-- </el-form-item> -->
            <el-form-item>
              <el-button
                type="primary"
                @click="onSearch"
              >查询</el-button>
              <el-button @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="table">
          <el-table
            ref="tableRef"
            :data="mainObj.list"
            fit
            border
            highlight-current-row
            :header-cell-style="{'background': '#F9F9F9'}"
          >
            <el-table-column
              prop="boothName"
              label="展台名称"
            />
            <el-table-column
              prop="boothUrl"
              label="展台链接"
            />
            <el-table-column
              prop="putOnAt"
              label="上架时间"
            />
            <el-table-column
              prop="takeOffAt"
              label="下架时间"
            />
            <el-table-column
              prop="lastOperator"
              label="最后操作人"
            />
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination
            :current-page="mainObj.currentPage"
            :page-size="mainObj.pageSize"
            :page-sizes="mainObj.pagesizes"
            :total="mainObj.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <!-- 更换展台 -->
    <el-dialog
      title="更换展台"
      center
      :visible.sync="visible"
      destroy-on-close
      width="800px"
      @close="visible = false"
    >
      <el-form ref="updateData" :rules="rules" :model="updateData">
        <el-form-item label="展台名称" label-width="100px" prop="boothName">
          <el-input
            v-model="updateData.boothName"
            maxlength="30"
            show-word-limit
            placeholder="最多可输入30个字"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item label="展台链接" label-width="100px" prop="boothUrl">
          <el-input v-model="updateData.boothUrl" placeholder="请输入" maxlength="256" autocomplete="off" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取 消</el-button>
        <el-button type="primary" @click="handleCommit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import sysManageApi from '@/api/sysManageApi'
import checkPermission from '@/utils/permission'
import RouteTitle from '@/components/RouteTitle'

const defaultSearchForm = {
  'boothName': '',
  'putOnAt': '',
  'takeOffAt': '',
  'currentPage': 1,
  'pageSize': 10
}
export default {
  components: {
    RouteTitle
  },
  data() {
    return {
      isloading: false,
      boothData: {
        'id': '',
        'boothName': '',
        'boothUrl': ''
      },
      updateData: {
        'oldBoothId': '',
        'boothName': '',
        'boothUrl': ''
      },
      mainObj: {
        list: [],
        pageSize: 10,
        pagesizes: [10, 30, 50],
        currentPage: 1,
        total: 0,
        searchForm: { ...defaultSearchForm }
      },
      visible: false,
      rules: {
        boothName: [
          { required: true, message: '展台名称不能为空', trigger: 'blur' }
        ],
        boothUrl: [
          { required: true, message: '展台链接不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.onSearch()
    this.getBooth()
  },
  methods: {
    checkPermission,
    // 获取当前展台
    getBooth() {
      sysManageApi.firstBooth().then(res => {
        this.boothData = res.data
      })
    },
    // 下架展台
    unBooth() {
      console.log(this.boothData.id)
      const id = this.boothData.id
      sysManageApi.takeOffBooth({ id: id }).then(res => {
        this.boothData = {

        }
        this.$message({
          type: 'success',
          message: '下架展台成功'
        })
      })
    },
    // 更换展台
    updateBooth() {
      this.visible = true
      const data = Object.assign({}, this.boothData)
      this.updateData = {
        'oldBoothId': data.id,
        'boothName': data.boothName,
        'boothUrl': data.boothUrl
      }
    },
    handleCommit() {
      this.$refs['updateData'].validate((valid) => {
        if (valid) {
          sysManageApi.changeBooth(this.updateData).then((res) => {
            this.onSearch()
            this.getBooth()
            this.visible = false
            this.$message({
              type: 'success',
              message: '更换展台成功'
            })
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    onSearch() {
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    onReset() {
      this.mainObj.searchForm = { ...defaultSearchForm }
      this.fetchData()
    },
    handleSizeChange(val) {
      this.mainObj.pageSize = val
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.mainObj.currentPage = val
      this.fetchData()
    },
    fetchData() {
      const params = {
        boothName: this.mainObj.searchForm.boothName,
        putOnAt: this.mainObj.searchForm.putOnAt,
        takeOffAt: this.mainObj.searchForm.takeOffAt,
        pageNum: this.mainObj.currentPage,
        pageSize: this.mainObj.pageSize
      }
      sysManageApi.searchBoothList(params).then((res) => {
        this.mainObj.list = res.data.list
        this.mainObj.total = res.data.totalCount
      })
    }

  }
}
</script>

<style lang="scss" scoped>
.main-body-bottom {
  box-sizing: border-box;
  padding: 20px;
}
.body-top-form {
  overflow: hidden;
}
.body-search-form {
  overflow: hidden;
  border: 1px solid #ddd;
  .left {
    padding: 0 22px;
  }
  .title {
    padding: 22px 0;
    text-align: center;
    color: #000;
    font-weight: 600;
    font-size: 26px;
    border-bottom: 1px solid #ddd;
    margin-bottom: 22px;
  }
}
.show-box {
  padding: 0 10px;
  min-height: 35px;
  border: 1px solid #ddd;
}
.right {
  float: right;
}

.left {
  float: left;
}

</style>
