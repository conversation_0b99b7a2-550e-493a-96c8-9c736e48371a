<template>
    <div :class="classObj" class="app-wrapper">
        <div class="Vheader">
            <div class="header-title">南京银行活动管理平台</div>

            <navbar />
        </div>

        <div
            v-if="device === 'mobile' && sidebar.opened"
            class="drawer-bg"
            @click="handleClickOutside"
        />
        <sidebar class="sidebar-container" />
        <div class="main-container">
            <div :class="{ 'fixed-header': true }" />
            <app-main />
        </div>
    </div>
</template>

<script>
// import RightPanel from '@/components/RightPanel'
import { AppMain, Navbar, Sidebar } from "./components";
// import ResizeMixin from './mixin/ResizeHandler'
import { mapState } from "vuex";

export default {
    name: "Layout",
    components: {
        AppMain,
        Navbar,
        // RightPanel,
        // Settings,
        Sidebar,
        // TagsView
    },
    // mixins: [ResizeMixin],
    computed: {
        ...mapState({
            sidebar: (state) => state.app.sidebar,
            device: (state) => state.app.device,
            showSettings: (state) => state.settings.showSettings,
            needTagsView: (state) => state.settings.tagsView,
            fixedHeader: (state) => state.settings.fixedHeader,
        }),
        classObj() {
            return {
                // hideSidebar: !this.sidebar.opened,
                openSidebar: this.sidebar.opened,
                withoutAnimation: this.sidebar.withoutAnimation,
                mobile: this.device === "mobile",
            };
        },
    },
    methods: {
        handleClickOutside() {
            this.$store.dispatch("app/closeSideBar", {
                withoutAnimation: false,
            });
        },
    },
};
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
@import "~@/styles/variables.scss";

.app-wrapper {
    @include clearfix;
    position: relative;
    height: 100%;
    width: 100%;
}
.Vheader {
    background: black;
    width: 100%;
    height: 50px;
    display: flex;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1002;
    justify-content: space-between;
}
.header-title {
    color: white;
    font-size: 25px;
    height: 50px;
    text-align: center;
    line-height: 50px;
    margin-left: 15px;
}

.drawer-bg {
    background: #000;
    opacity: 0.3;
    width: 100%;
    top: 0;
    height: 100%;
    position: absolute;
    z-index: 999;
}

.fixed-header {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 9;
    width: calc(100% - #{$sideBarWidth});
    transition: width 0.28s;
}
.header {
    margin-top: 50px;
    background: linear-gradient(180deg, #eaf5ff 0%, #ffffff 41.76%);
    box-shadow: 0px 2px 9px rgba(0, 0, 0, 0.08);
    height: 90px;
}
.hideSidebar .fixed-header {
    width: calc(100% - 54px);
}
</style>
