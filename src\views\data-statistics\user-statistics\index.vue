<template>
  <div>
    <RouteTitle />
    <div class="main-body">
      <div class="main-body-bottom">
        <el-form label-width="80px">
          <el-form-item label="日期：">
            <el-date-picker
              v-model="time"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              value-format="yyyy-MM-dd"
              end-placeholder="结束日期"
              :picker-options="setDateRange"
              @change="changeTime"
            />
          </el-form-item>
        </el-form>
        <div ref="chartColumn" style="width:100%;height:600px;margin-left:20px;" />
      </div>
    </div>
  </div>
</template>

<script>
import RouteTitle from '@/components/RouteTitle'
import { searchUserStatisticsList } from '@/api/community'
import echarts from 'echarts'
export default {
  name: 'UserStatistics',
  components: {
    RouteTitle
  },
  data() {
    return {
      setDateRange: {
        disabledDate: time => {
          // 禁用今天之后的日期【当前天可选】
          return time.getTime() > Date.now()
        }
      },
      time: [],
      option: {
        title: {
          text: '社区用户规模'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const total = params[0]
            const add = params[1]
            const totalNum = total.value + add.value
            return add.name + '<br/>' + total.seriesName + ' : ' + totalNum + '<br/>' + add.seriesName + ' : ' + add.value
          }
        },
        legend: {
          data: ['新增人数']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '总人数',
            type: 'bar',
            stack: 'Total',
            itemStyle: {
              borderColor: 'transparent',
              color: 'transparent'
            },
            emphasis: {
              itemStyle: {
                borderColor: 'transparent',
                color: 'transparent'
              }
            },
            data: []
          },
          {
            name: '新增人数',
            type: 'bar',
            stack: 'Total',
            label: {
              show: true,
              position: 'inside'
            },
            data: []
          },
          {
            name: '新增人数',
            type: 'bar',
            stack: 'Total',
            label: {
              show: true,
              position: 'top'
            },
            data: []
          }
        ]
      }

    }
  },

  mounted() {
    this.time = [this.ShowDate(30), this.ShowDate(0)]
    this.searchUserStatisticsList()
  },

  methods: {
    changeTime() {
      this.searchUserStatisticsList()
    },
    formatEveryDay(start, end, Spacer) {
      // const weekArr = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      const dateList = []
      const startTime = this.getDate(start)
      const endTime = this.getDate(end)

      while ((endTime.getTime() - startTime.getTime()) >= 0) {
        // const year = startTime.getFullYear()
        const month = startTime.getMonth() + 1 < 10 ? '0' + (startTime.getMonth() + 1) : startTime.getMonth() + 1
        const day = startTime.getDate().toString().length === 1 ? '0' + startTime.getDate() : startTime.getDate()
        // const timeDay = startTime.getDay(startTime)
        // dateList.push({
        //   time: year + '-' + month + '-' + day,
        //   week: weekArr[startTime.getDay(startTime)],
        //   isWeekend: timeDay === 0 || timeDay === 6
        // })
        if (Spacer) {
          // 如果不需要间隔符则直接拼接返回
          if (Spacer === 'null') {
            dateList.push(month + day) // 如20220422,20220423
          } else {
            // 需要自定义间隔符
            dateList.push(month + Spacer + day)
          }
        } else {
          // dateList.push(year + '-' + month + '-' + day) // 2022-04-22,2022-04-23
          dateList.push(month + '-' + day) // 2022-04-22,2022-04-23
        }
        startTime.setDate(startTime.getDate() + 1)
      }
      return dateList
    },
    getDate(datestr) {
      const temp = datestr.split('-')
      const date = new Date(temp[0], temp[1] - 1, temp[2])
      return date
    },
    searchUserStatisticsList() {
      searchUserStatisticsList({
        endDate: this.time[1],
        startDate: this.time[0]
      }).then(res => {
        const list = res.data || []
        const dateList = this.formatEveryDay(this.time[0], this.time[1], '-')
        const option = JSON.parse(JSON.stringify(this.option))
        option.xAxis.data = dateList
        const total = []
        dateList.forEach((dateItem, dateIndex) => {
          const flag = list.filter(item => item.registerDate.substring(5) === dateItem)
          if (flag.length > 0) {
            total.push(flag[0].totalRegisterPersons)
            option.series[1].data.push(flag[0].currdayRegisterPersons)
            option.series[2].data.push(0)
          } else {
            const num = total.length > 0 ? total[total.length - 1] : 0
            total.push(num)
            option.series[1].data.push(0)
            option.series[2].data.push(0)
          }
        })
        total.map((item, index) => {
          if (item !== option.series[1].data[index]) {
            option.series[0].data.push(item - option.series[1].data[index])
          } else {
            option.series[0].data.push(0)
          }
        })
        option.series[1].label.formatter = function(res) {
          if (res.data) {
            return '+' + res.data
          } else {
            return ''
          }
        }
        option.tooltip.formatter = function(params) {
          const total = params[0]
          const add = params[1]
          const totalNum = total.value + add.value
          return add.name + '<br/>' + total.seriesName + ' : ' + totalNum + '<br/>' + add.seriesName + ' : ' + add.value
        }
        option.series[2].label.formatter = function(res) {
          const total = option.series[0].data[res.dataIndex] + option.series[1].data[res.dataIndex]
          if (total && option.series[1].data[res.dataIndex]) {
            return total
          } else {
            return ''
          }
        }
        this.chartColumn = echarts.init(this.$refs.chartColumn)
        this.chartColumn.setOption(option)
      })
    },
    ShowDate(date) {
      var num = date
      const n = num
      const d = new Date()
      let year = d.getFullYear()
      let mon = d.getMonth() + 1
      let day = d.getDate()
      if (day <= n) {
        if (mon > 1) {
          mon = mon - 1
        } else {
          year = year - 1
          mon = 12
        }
      }
      d.setDate(d.getDate() - n)
      year = d.getFullYear()
      mon = d.getMonth() + 1
      day = d.getDate()
      const s = year + '-' + (mon < 10 ? ('0' + mon) : mon) + '-' + (day < 10 ? ('0' + day) : day)
      return s
    }
  }
}
</script>

<style lang="scss" scoped>
.main-body-bottom {
  box-sizing: border-box;
  padding: 20px;
}

</style>
