<template>
  <div>
    <!-- <RouteTitle /> -->
    <div class="json-editor">
      <textarea ref="textarea0" />
    </div>
    <div class="json-editor">
      <textarea ref="textarea" />
    </div>
  </div>

</template>

<script>
import CodeMirror from 'codemirror'
import 'codemirror/addon/lint/lint.css'
import 'codemirror/lib/codemirror.css'
import 'codemirror/theme/rubyblue.css' // 主题显示，可以百度搜索其他的主题
require('script-loader!jsonlint')
import 'codemirror/mode/javascript/javascript'
import 'codemirror/addon/lint/lint'
import 'codemirror/addon/lint/json-lint'
import 'codemirror/mode/xml/xml.js'
import sysManageApi from '../../../../api/sysManageApi'

export default {
  name: 'JsonEditor',
  /* eslint-disable vue/require-prop-types */
  props: {
    operationID: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      id: this.$route.query.id,
      value: '',
      params: '',
      jsonEditor: false
    }
  },
  // 监听是否数据改变，随时更新json数据
  // watch: {
  //   value(value) {
  //     console.log(value);
  //     const editorValue = this.jsonEditor.getValue();
  //     if (value !== editorValue) {
  //       this.jsonEditor.setValue(JSON.stringify(this.value, null, 2));
  //     }
  //   },
  //   immediate: true,
  //   deep: true
  // },
  mounted() {
    // CodeMirror的配置项，搜官网看这里的配置项配置
    this.jsonEditor0 = CodeMirror.fromTextArea(this.$refs.textarea0, {
      lineNumbers: true, // 是否显示行数
      mode: 'application/xml', // 接受的类型，json xml....
      gutters: ['CodeMirror-lint-markers'], // 样式的宽度
      theme: 'rubyblue', // 主题
      lint: true
    })
    this.jsonEditor = CodeMirror.fromTextArea(this.$refs.textarea, {
      lineNumbers: true, // 是否显示行数
      mode: 'application/xml', // 接受的类型，json xml....
      gutters: ['CodeMirror-lint-markers'], // 样式的宽度
      theme: 'rubyblue', // 主题
      lint: true
    })
    this.updatedInfo(this.operationID)
  },
  methods: {
    getValue() {
      return this.jsonEditor.getValue()
    },
    updatedInfo(data) {
      const params = {
        id: data
      }
      sysManageApi.getOperationLogDetail(params).then(res => {
        this.params = JSON.parse(res.data.params)
        if (res.data.requestStatus === 1) {
          this.value = JSON.parse(res.data.resultData)
          this.jsonEditor.setValue(JSON.stringify(this.value, null, 2))
          this.jsonEditor.on('change', cm => {
            //   this.$emit("changed", cm.getValue());
            // 编辑json框里面的内容可以时刻监听到值，通过cm.getValue()获取到
          })
        } else {
          this.jsonEditor.setValue(res.data.resultData)
        }

        this.jsonEditor0.setValue(JSON.stringify(this.params, null, 2))
        this.jsonEditor0.on('change', cm => {
          //   this.$emit("changed", cm.getValue());
          // 编辑json框里面的内容可以时刻监听到值，通过cm.getValue()获取到
        })
      })
    }
  }
}
</script>

<style scoped>
.json-editor0 {
  height: 80%;
  position: relative;
  margin: 20px;
}
.json-editor {
  height: 80%;
  position: relative;
  margin: 20px;
}
.json-editor >>> .CodeMirror {
  height: 400px;
  min-height: 300px;
}
.json-editor >>> .CodeMirror-scroll {
  min-height: 300px;
}
.json-editor >>> .cm-s-rubyblue span.cm-string {
  color: #f08047;
}
</style>

