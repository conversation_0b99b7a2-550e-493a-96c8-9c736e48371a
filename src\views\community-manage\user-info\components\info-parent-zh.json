[{"title": "联系方式", "children": [{"title": "姓名", "group": "basicInfo", "key": "name"}, {"title": "性别", "group": "basicInfo", "key": "userGender"}, {"title": "是孩子的", "group": "basicInfo", "key": "userParentRole", "options": ["", "父亲", "母亲", "亲戚"]}, {"title": "联系电话", "group": "basicInfo", "key": "phoneNo"}, {"title": "身份证号", "group": "basicInfo", "key": "idCard"}, {"title": "钱江分", "group": "identityInfo", "key": "score"}]}, {"title": "孩子基本信息", "children": [{"title": "孩子性别", "group": "basicInfo", "key": "gender"}, {"title": "出生年月", "group": "identityInfo", "keys": ["birthYear", "<PERSON><PERSON><PERSON><PERSON>"], "keyBind": "-"}, {"title": "年龄", "group": "basicInfo", "key": "age"}, {"title": "身高", "group": "basicInfo", "key": "height"}, {"title": "体重", "group": "basicInfo", "key": "weight"}, {"title": "籍贯", "group": "basicInfo", "key": "nativePlace"}, {"title": "现居地", "group": "basicInfo", "key": "<PERSON><PERSON><PERSON><PERSON>"}, {"title": "性格特点", "group": "basicInfo", "key": "character"}]}, {"title": "孩子身份信息", "children": [{"title": "学历", "group": "identityInfo", "key": "educationStatus", "options": ["专科以下", "专科", "本科", "硕士", "博士"]}, {"title": "住房情况", "group": "identityInfo", "key": "houseStatus", "options": ["有房", "无房", "计划购房"]}, {"title": "是否有车", "group": "identityInfo", "key": "car<PERSON>tatus", "options": ["无车", "有车", "计划购车"]}, {"title": "婚姻状况", "group": "identityInfo", "key": "marrier<PERSON><PERSON><PERSON>", "options": ["单身", "", "离异", "已婚"]}, {"title": "子女状况", "group": "identityInfo", "key": "childStatus", "options": ["无孩", "有孩"]}, {"title": "职业行业", "group": "identityInfo", "key": "field"}, {"title": "工作单位", "group": "identityInfo", "key": "workUnit"}, {"title": "年收入", "group": "identityInfo", "key": "annualIncome", "options": ["5W以下", "5~10W", "10~20W", "20~30W", "30~50W", "50~100W", "100W以上"]}]}, {"title": "择偶要求", "children": [{"title": "年龄要求", "group": "mateInfo", "key": "ageOpt"}, {"title": "学历要求", "group": "mateInfo", "key": "educationOpt"}, {"title": "籍贯", "group": "mateInfo", "key": "nativeOpt"}, {"title": "现居地", "group": "mateInfo", "key": "presentAddressOpt"}, {"title": "婚姻状况", "group": "mateInfo", "key": "marrierStatusOpt", "options": ["不限", "", "未婚", "离异"]}, {"title": "子女状况", "group": "mateInfo", "key": "childStatusOpt"}, {"title": "住房情况", "group": "mateInfo", "key": "houseStatusOpt", "options": ["有房", "无房", "计划购房", "不限"]}, {"title": "是否有车", "group": "mateInfo", "key": "carStatusOpt"}, {"title": "年收入", "group": "mateInfo", "key": "annualIncomeOpt", "options": ["不限", "5W以下", "5~10W", "10~20W", "20~30W", "30~50W", "50~100W", "100W以上"]}, {"title": "更多说明", "group": "mateInfo", "key": "moreInfomation"}]}]