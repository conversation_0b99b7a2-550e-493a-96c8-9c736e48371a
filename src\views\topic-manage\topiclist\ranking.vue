<template>
  <div>
    <el-dialog
      title="排行榜"
      :visible.sync="dialogVisible"
      width="40%"
      top="4vh"
      :before-close="() => dialogVisible = false"
    >
      <div>
        <el-table
          ref="tableRef"
          v-loading="listLoading"
          :data="list"
          fit
          border
          highlight-current-row
          size="small"
          :header-cell-style="{ background: '#F9F9F9' }"
        >
          <el-table-column label="序号" prop="rankNo" width="50" />
          <el-table-column label="用户名" prop="nickName" />
          <el-table-column label="点赞数" prop="likeNum" />
          <el-table-column v-if="buttonTitle !== '已发奖'" label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="removeRanking(scope.row)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <Pagination
          :total="Number(total)"
          :page.sync="queryFrom.pageNum"
          :limit.sync="queryFrom.pageSize"
          @pagination="getList"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="isloading" :disabled="buttonTitle === '已发奖'" type="primary" @click="sendPrizes">{{ buttonTitle }}</el-button>
        <el-button @click="() => dialogVisible = false">关闭</el-button>
      </div>

    </el-dialog>
  </div>
</template>

<script>
import community from '@/api/community'
import { communityPrizeAward } from '@/api/prizeManager'
import Pagination from '@/components/Pagination'
export default {
  components: {
    Pagination
  },
  data() {
    return {
      dialogVisible: false,
      list: [],
      buttonTitle: '发奖',
      listLoading: false,
      queryFrom: {
        topicId: '',
        pageNum: 1,
        pageSize: 10
      },
      isloading: false,
      total: 0
    }
  },
  methods: {
    showDialo(row) {
      this.list = []
      this.queryFrom.topicId = row.id
      if (row.awardStatus !== 'INIT') {
        this.buttonTitle = '已发奖'
      } else {
        this.buttonTitle = '发奖'
      }
      this.dialogVisible = true
      this.queryFrom.pageNum = 1
      this.queryFrom.pageSize = 10
      this.getList()
    },
    getList() {
      this.listLoading = true
      community.rankList(this.queryFrom).then(res => {
        this.total = res.data.totalCount
        this.list = res.data.list
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    removeRanking(row = {}) {
      this.$confirm(
        '确认移除?',
        {
          confirmButtonText: '是',
          cancelButtonText: '否',
          type: 'warning'
        }
      ).then(() => {
        community.topicBlock({ id: row.id }).then(res => {
          this.getList()
        })
      })
    },
    sendPrizes() {
      this.$confirm(
        '确认发奖?',
        {
          confirmButtonText: '是',
          cancelButtonText: '否',
          type: 'warning'
        }
      ).then(() => {
        this.isloading = true
        communityPrizeAward({ topicId: this.queryFrom.topicId }).then(res => {
          this.isloading = false
          this.dialogVisible = false
          this.$emit('success')
        }).catch(() => {
          this.isloading = false
        })
      })
    }
  }

}
</script>

<style lang="scss" scoped>
.dialog-footer {
    text-align: center;
}

::v-deep .pagination-container {
    padding: 10px;
    margin-top: 0px;
    text-align: right;
}
</style>
