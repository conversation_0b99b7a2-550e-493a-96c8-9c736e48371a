<template>
  <div class="bg">
    <Breadcrumb />
    <div class="line" />
    <div class="title">{{ activeId.meta?activeId.meta.title:'' }}
      <slot />
    </div>
  </div>
</template>

<script>
import Breadcrumb from '@/components/Breadcrumb'
export default {
  name: 'RouteTitle',
  components: {
    Breadcrumb
  },
  data() {
    return {
      activeId: {}
    }
  },
  watch: {
    $route(route) {
      this.activeId = this.$route
    }
  },
  mounted() {
    this.activeId = this.$route
  }
}
</script>

<style scoped>
.bg {
  height: 91px;
  background: linear-gradient(180deg, #EAF5FF 0%, #FFFFFF 41.76%);
  box-shadow: 0px 2px 9px rgba(0, 0, 0, 0.08);
}
  .line {
    margin-top: 7px;
    border: 0.5px solid #E2E8F0;
  }
  .title {
    font-style: normal;
    font-weight: 700;
    font-size: 20px;
    line-height: 28px;
    /* identical to box height, or 140% */

    margin-top: 12px;
    margin-left: 20px;

    color: rgba(0, 0, 0, 0.9);

  }
</style>
