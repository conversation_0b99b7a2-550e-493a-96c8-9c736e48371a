<template>
  <div>
    <el-dialog
      :title="type === 'examine' ? '评论待审核' : '评论详情'"
      :visible.sync="dialogVisible"
      width="780px"
      top="4vh"
      :before-close="handleClose"
    >
      <div v-loading="loading" class="detail" style="min-height:320px;">
        <div v-if="row">
          <div class="detail-msg">
            <div>
              <span>评论内容：</span>
              <span>{{ row.content }}</span>
            </div>
            <div>
              <span>状态：</span>
              <span>{{ row.status.text }}</span>
            </div>
            <div>
              <span>评论人：{{ row.createName }}</span>
              <span style="display:inline-block;padding-left:20px;">手机号：{{ row.mobile }}</span>
              <!-- <span style="display:inline-block;padding-left:20px;">身份证号：{{ row.idNo }}</span> -->
            </div>
            <div>
              <span>评论时间：</span>
              <span>{{ row.commentTime }}</span>
            </div>
            <div>
              <span>所属帖子标题：</span>
              <span>{{ row.title }}</span>
            </div>
            <div>
              <span>评论级别：</span>
              <span>{{ row.level }}</span>
            </div>
            <div>
              <span>父级评论人信息：</span>
              <span>{{ row.pCommentUserName }}</span>
            </div>
            <div>
              <span>父级评论内容：</span>
              <span>{{ row.pContent }}</span>
            </div>
          </div>
          <el-form
            ref="ruleForm"
            :model="ruleForm"
            :rules="rules"
            label-width="120px"
            class="demo-ruleForm"
            label-position="top"
            size="small"
            :disabled="type === 'view'"
          >
            <el-form-item label="拒绝原因（审核拒绝必填）" prop="applRemark">
              <el-input
                v-model="ruleForm.applRemark"
                type="textarea"
                resize="none"
                rows="5"
                maxlength="300"
                show-word-limit
                placeholder="请输入"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <span v-if="type !== 'view'" slot="footer" class="dialog-footer">
        <el-button @click="refuse">审核拒绝</el-button>
        <el-button
          type="primary"
          :loading="isloading"
          @click="submitForm"
        >审核通过</el-button>
      </span>
      <span v-else slot="footer" class="dialog-footer">
        <!-- <el-button @click="dialogVisible = false">返 回</el-button> -->
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { verifyCommentary, getCommentaryDetail } from '@/api/community'
export default {
  name: 'PostsDetail',
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isloading: false,
      loading: false,
      row: null,
      dialogVisible: false,
      ruleForm: {
        applRemark: ''
      },
      rules: {
        applRemark: [{ required: false, message: '不能为空', trigger: 'change' }]
      }
    }
  },
  computed: {
    // transType() {
    //   return type => {
    //     return {
    //       10: '待审核',
    //       11: '审核不通过',
    //       12: '审核通过'
    //     }[type]
    //   }
    // }
  },

  mounted() {
  },

  methods: {
    getCommentaryDetail(id) {
      this.loading = true
      getCommentaryDetail({
        commentaryId: id
      }).then(res => {
        this.row = res.data
        this.ruleForm.applRemark = res.data.applRemark
        this.loading = false
      })
    },
    refuse() {
      this.rules.applRemark[0].required = true
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          this.isloading = true
          const obj = {
            applRemark: this.ruleForm.applRemark,
            applId: this.row.commentaryId,
            status: 11
          }
          verifyCommentary(obj).then(res => {
            this.handleClose()
            this.isloading = false
            this.$message({
              type: 'success',
              message: '操作成功！'
            })
            this.$refs['ruleForm'].resetFields()
            this.$emit('changelist')
          })
        }
      })
    },
    handleClose() {
      if (this.$refs['ruleForm']) {
        this.$refs['ruleForm'].resetFields()
      }
      this.dialogVisible = false
    },
    submitForm(formName) {
      this.isloading = true
      const obj = {
        applRemark: this.ruleForm.applRemark,
        applId: this.row.commentaryId,
        status: 12
      }
      verifyCommentary(obj).then(res => {
        this.isloading = false
        this.handleClose()
        this.$message({
          type: 'success',
          message: '操作成功！'
        })
        this.$refs['ruleForm'].resetFields()
        this.$emit('changelist')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__footer {
  text-align: right;
}

.detail {
  width: 100%;
  box-sizing: border-box;
  padding: 0 40px 0 50px;
}

.line {
  text-align: center;
}

::v-deep .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
 ::v-deep .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
 ::v-deep .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 110px;
    line-height: 110px;
    text-align: center;
  }
 ::v-deep .avatar {
    width: 120px;
    height: 110px;
    display: block;
  }

  ::v-deep .el-upload__tip{
      color:#999;
      margin-top: -9px;
  }

  .detail-msg{
    box-sizing: border-box;
    width:100%;
    line-height: 2;
    margin-bottom:20px;
  }
</style>
