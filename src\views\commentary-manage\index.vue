<template>
  <div>
    <RouteTitle />
    <div class="main-body">
      <div class="main-body-bottom">
        <div class="body-top-form">
          <el-form
            ref="searchForm"
            class="left"
            :model="mainObj.searchForm"
            label-position="left"
            size="small"
            inline
          >
            <el-form-item label="状态：">
              <el-select
                v-model="mainObj.searchForm.status"
                placeholder="请选择"
                clearable
              >
                <!-- <el-option label="全部" value="" />
                <el-option label="审核通过" value="12" />
                <el-option label="待审核" value="10" />
                <el-option label="审核拒绝" value="11" /> -->
                <el-option
                  v-for="item in statusEnumObj"
                  :key="item.code"
                  :label="item.text"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="评论内容：">
              <el-input
                v-model="mainObj.searchForm.content"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
            <el-form-item label="评论人：">
              <el-input
                v-model="mainObj.searchForm.name"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
            <el-form-item label="所属帖子：">
              <el-input
                v-model="mainObj.searchForm.postTitle"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
            <el-form-item label="帖子来源：">
              <el-select
                v-model="mainObj.searchForm.postChannel"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in postChannelList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="评论时间：">
              <el-date-picker
                v-model="mainObj.searchForm.createTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">查询</el-button>
              <el-button @click="onReset">重置</el-button>
              <el-button type="primary" :icon="exportIcon" @click="exportCommentary()">导出</el-button>
            </el-form-item>
          </el-form>
          <!-- <el-button
            class="right"
            type="primary"
            @click="operation('add')"
          >新增</el-button> -->
        </div>
        <div class="table">
          <el-table
            ref="tableRef"
            :data="mainObj.list"
            fit
            border
            highlight-current-row
            size="small"
            :header-cell-style="{ background: '#F9F9F9' }"
          >
            <el-table-column prop="ipAddress" label="序号" width="50">
              <template slot-scope="scope">
                <span>{{ transTime(scope.$index) }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column prop="status" label="状态">
              <template slot-scope="scope">
                <span>{{ transType(scope.row.status) }}</span>
              </template>
            </el-table-column> -->
            <el-table-column prop="status" label="状态" />
            <el-table-column prop="content" label="评论内容" width="200" show-overflow-tooltip />
            <el-table-column prop="createName" label="评论人" />
            <el-table-column prop="title" label="所属帖子标题" width="150" show-overflow-tooltip />
            <el-table-column prop="postChannel" label="帖子来源" width="200" show-overflow-tooltip>
              <template slot-scope="scope">
                <span v-if="scope.row.postChannel === 'HELLO'">hello好店</span>
                <span v-else-if="scope.row.postChannel === 'SK'">市民卡平台</span>
                <span v-else>&nbsp;</span>
              </template>
            </el-table-column>
            <el-table-column prop="level" label="评论级别" />
            <el-table-column prop="pCommentUserName" label="父级评论人" />
            <el-table-column prop="pContent" label="父级评论内容" />
            <el-table-column prop="likeCount" label="评论点赞数" />
            <el-table-column prop="commentTime" label="评论时间" width="150" />
            <el-table-column prop="verifyUserName" label="审核人" />
            <el-table-column prop="verifyTime" label="审核时间" width="150" />
            <el-table-column label="操作" fixed="right" width="200">
              <template slot-scope="scope">
                <el-button v-if="scope.row.status.code === '11' || scope.row.status.code === '10' || scope.row.status.code === '9'" type="text" @click="operation('examine', scope.row)">审核</el-button>
                <el-button v-if="scope.row.status.code !== '10'" type="text" @click="operation('view', scope.row)">查看</el-button>
                <el-button v-if="scope.row.status.code === '11' || scope.row.status.code === '12'" type="text" @click="operation('del', scope.row)">删除</el-button>
                <el-button type="text" @click="operation('getCommentaryDetailApplys', scope.row)">审核记录</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination
            :current-page="mainObj.currentPage"
            :page-size="mainObj.pageSize"
            :page-sizes="mainObj.pagesizes"
            :total="mainObj.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <detail ref="detail" :type="type" @changelist="onSearch" />
    <applyRecord ref="applyRecord" :row="row" />
  </div>
</template>

<script>
import sysManageApi from '@/api/sysManageApi'
import community from '@/api/community'
import checkPermission from '@/utils/permission'
import RouteTitle from '@/components/RouteTitle'
import detail from './detail'
import applyRecord from './applyRecord'
import { downOrViewFile, formatDates } from '@/utils/index'
import { Message } from 'element-ui'
const defaultSearchForm = {
  name: '',
  status: '',
  postTitle: '',
  content: '',
  createTime: '',
  postChannel: ''
}

const defaultUserForm = {
  userName: '',
  mobile: '',
  roleIdList: []
}
export default {
  components: {
    RouteTitle,
    detail,
    applyRecord
  },
  data() {
    return {
      // token: getToken(),
      details: checkPermission(['requestLog_details']),
      row: {},
      type: '',
      infoId: '',
      dialogTableVisible: false,
      statusEnumObj: [],
      mainObj: {
        list: [],
        pageSize: 10,
        pagesizes: [10, 30, 50],
        currentPage: 1,
        total: 0,
        searchForm: { ...defaultSearchForm }
      },
      userDialogObj: {
        visible: false,
        title: '',
        type: '',
        form: Object.assign({}, defaultUserForm)
      },
      postChannelList: [{ name: '市民卡平台', value: 'SK' }, { name: 'hello好店', value: 'HELLO' }],
      value: 1,
      exportIcon: ''
    }
  },
  computed: {
    // transType() {
    //   return type => {
    //     return {
    //       10: '待审核',
    //       11: '审核不通过',
    //       12: '审核通过'
    //     }[type]
    //   }
    // },
    transTime() {
      return (index) => {
        return (this.mainObj.currentPage - 1) * this.mainObj.list.length + index + 1
      }
    }
  },
  created() {
    this.getCommentaryStatusEnum()
    this.onSearch()
  },
  methods: {
    exportCommentary() {
      this.exportIcon = 'el-icon-loading'
      const params = {
        postChannel: this.mainObj.searchForm.postChannel,
        name: this.mainObj.searchForm.name,
        status: this.mainObj.searchForm.status,
        postTitle: this.mainObj.searchForm.postTitle,
        content: this.mainObj.searchForm.content,
        createTime: this.mainObj.searchForm.createTime,
        pageNum: this.mainObj.currentPage,
        pageSize: this.mainObj.pageSize
      }
      community.exportCommentaryList(params).then(res => {
        downOrViewFile(res.data, '评论列表' + formatDates(new Date()) + '.xlsx')
        this.exportIcon = ''
      }).catch(err => {
        console.log(err)
        Message({
          message: '无数据导出',
          type: 'error',
          duration: 5 * 1000
        })
        this.exportIcon = ''
      })
    },
    checkPermission,
    getCommentaryStatusEnum() {
      sysManageApi.getTypeEnum({ enumName: 'CommentaryStatusEnum' }).then(res => {
        this.statusEnumObj = res.data
      })
    },
    onSearch() {
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    onReset() {
      this.mainObj.searchForm = { ...defaultSearchForm }
      this.fetchData()
    },
    handleSizeChange(val) {
      this.mainObj.pageSize = val
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.mainObj.currentPage = val
      this.fetchData()
    },
    getDetailInfo(row) {
      this.infoId = row.id.toString()
      this.dialogTableVisible = true
      if (this.$refs.child) {
        this.$refs.child.updatedInfo(row.id)
      }
    },
    fetchData() {
      const params = {
        postChannel: this.mainObj.searchForm.postChannel,
        name: this.mainObj.searchForm.name,
        status: this.mainObj.searchForm.status,
        postTitle: this.mainObj.searchForm.postTitle,
        content: this.mainObj.searchForm.content,
        createTime: this.mainObj.searchForm.createTime,
        pageNum: this.mainObj.currentPage,
        pageSize: this.mainObj.pageSize
      }
      community.getCommentaryList(params).then(res => {
        this.mainObj.list = res.data.list
        this.mainObj.total = Number(res.data.total)
      })
    },
    operation(type, row = {}) {
      this.type = type
      this.row = row
      if (type === 'view' || type === 'examine') {
        this.$nextTick(() => {
          this.$refs.detail.dialogVisible = true
          this.$refs.detail.getCommentaryDetail(this.row.commentaryId)
        })
      }

      if (type === 'del') {
        this.$confirm(`确定要删除此评论吗`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            community.deleteCommentary([row.commentaryId]).then(res => {
              this.handleSizeChange()
              this.$message({
                type: 'success',
                message: '删除成功！'
              })
            })
          })
          .catch(() => {})
      }

      if (type === 'getCommentaryDetailApplys') {
        this.$nextTick(() => {
          this.$refs.applyRecord.dialogVisible = true
          this.$refs.applyRecord.getCommentaryDetailApplys(this.row.commentaryId)
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.main-body-bottom {
  box-sizing: border-box;
  padding: 20px;
}

.right {
  float: right;
}

.left {
  float: left;
}

.body-top-form {
  overflow: hidden;
}
</style>
