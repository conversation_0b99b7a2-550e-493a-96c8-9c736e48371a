// import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'

export function getToken() {
  // return Cookies.get(TokenKey)
  return window.sessionStorage.getItem(TokenKey)
}

export function setToken(token) {
  // return Cookies.set(Token<PERSON><PERSON>, token)
  return window.sessionStorage.setItem(Token<PERSON><PERSON>, token)
}

export function removeToken() {
  // return Cookies.remove(TokenKey)
  return window.sessionStorage.removeItem(TokenKey)
}
