<template>
  <div>
    <RouteTitle />
    <div class="main-body">
      <div class="main-body-bottom">
        <div class="body-top-form">
          <el-form
            ref="searchForm"
            :model="mainObj.searchForm"
            label-width="75px"
            class="left"
            label-position="left"
            inline
          >
            <el-form-item label="图片类型">
              <el-select v-model="mainObj.searchForm.imageType" placeholder="请选择">
                <el-option label="全部" value="" />
                <el-option v-for="item in imageTypeObj" :key="item.code" :label="item.text" :value="item.code" />
              </el-select>
            </el-form-item>
            <el-form-item label="图片备注">
              <el-input v-model.trim="mainObj.searchForm.imageRemark" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">查询</el-button>
              <el-button @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
          <div v-if="addPermission" class="right">
            <div class="main-body-bottom-btn-left">
              <el-button v-if="addPermission" type="primary" @click="onOperate('add')">新增</el-button>
            </div>
          </div>
        </div>
        <div class="table">
          <el-table
            ref="tableRef"
            :data="mainObj.list"
            fit
            border
            highlight-current-row
            :header-cell-style="{ 'background': '#F9F9F9' }"
          >
            <el-table-column prop="id" label="图片编号" />
            <el-table-column prop="imageType" label="图片类型" />
            <!-- <el-table-column
              prop="imageLink"
              label="图片链接"
            /> -->
            <el-table-column prop="imageRemark" label="备注信息" />
            <!-- <el-table-column
              prop="topicId"
              label="关联话题ID"
            /> -->
            <el-table-column prop="topicName" label="关联话题">
              <template slot-scope="scope">
                {{ scope.row.topicName?scope.row.topicName:'-' }}
              </template>
            </el-table-column>
            <el-table-column prop="sort" label="排序">
              <template slot-scope="scope">
                {{ scope.row.sort?scope.row.sort:'-' }}
              </template>
            </el-table-column>
            <el-table-column prop="updateTime" label="更新时间" />
            <el-table-column label="操作" width="180">
              <template slot-scope="scope">
                <el-button
                  v-if="updatePermission"
                  v-preventReClick
                  type="text"
                  @click="onOperate('update', scope.row)"
                >编辑</el-button>
                <el-button type="text" @click="onOperate('view', scope.row)">查看</el-button>
                <el-button
                  v-if="deletePermission && scope.row.allowDelFlag.code === '1'"
                  type="text"
                  @click="onOperate('delete', scope.row)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination
            :current-page="mainObj.currentPage"
            :page-size="mainObj.pageSize"
            :page-sizes="mainObj.pagesizes"
            :total="mainObj.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <el-dialog
      :title="imageDialogObj.title"
      :visible.sync="imageDialogObj.visible"
      destroy-on-close
      width="800px"
      @close="imageDialogObj.visible = false"
    >
      <el-form
        ref="imageFormRef"
        :model="imageDialogObj.form"
        :rules="imageDialogObj.type === 'view' ? null : imageDialogObj.rules"
        :disabled="imageDialogObj.type === 'view'"
        label-width="80px"
        label-position="right"
        class="full-width"
      >
        <el-form-item label="图片类型" prop="imageType">
          <el-select v-model="imageDialogObj.form.imageType" placeholder="请选择" :disabled="imageDialogObj.type !== 'add'">
            <el-option v-for="item in imageTypeAddObj" :key="item.code" :label="item.text" :value="item.code" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="链接地址" prop="imageLink">
          <el-input
            v-model="imageDialogObj.form.imageLink"
            placeholder="请输入"
            clearable
          />
        </el-form-item> -->
        <el-form-item v-if="imageDialogObj.form.imageType === '2'" :key="'图片备注1'" label="图片备注" prop="imageRemark">
          <el-input v-model="imageDialogObj.form.imageRemark" maxlength="50" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item v-else :key="'图片备注2'" label="图片备注">
          <el-input v-model="imageDialogObj.form.imageRemark" maxlength="50" placeholder="请输入" clearable />
        </el-form-item>
        <template v-if="imageDialogObj.form.imageType !== '2'">
          <el-form-item label="话题" prop="topicId">
            <el-select v-model="imageDialogObj.form.topicId" placeholder="请选择">
              <el-option v-for="item in topicObj" :key="item.id" :label="item.topicName" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input v-model="imageDialogObj.form.sort" maxlength="5" placeholder="请输入" clearable />
          </el-form-item>
        </template>

        <el-form-item prop="url">
          <fileUpload
            ref="fileUpload"
            :type="imageDialogObj.type"
            :file-vos="imageDialogObj.form"
            @previewFile="handlePictureCardPreview"
          />
        </el-form-item>

      </el-form>
      <div v-if="imageDialogObj.type !== 'view'" slot="footer">
        <el-button @click="imageDialogObj.visible = false">取消</el-button>
        <el-button v-preventReClick type="primary" :loading="isloading" @click="onOperate('submit')">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="图片预览" :visible.sync="dialogVisible" :modal-append-to-body="false">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </div>
</template>

<script>
import sysManageApi from '@/api/sysManageApi'
import checkPermission from '@/utils/permission'
import fileUpload from './fileUpload'
import RouteTitle from '@/components/RouteTitle'

const defaultSearchForm = {
  imageType: '',
  imageRemark: ''
}

const defaultImageForm = {
  allowDelFlag: '',
  imageFileId: '',
  imageLink: '',
  imageName: '',
  sort: '',
  imageRemark: '',
  imageType: '',
  id: '',
  url: '',
  file: {},
  topicId: '',
  topicName: ''
}
export default {
  components: {
    fileUpload,
    RouteTitle
  },
  data() {
    const validateConfirmSort = (rule, value, callback) => {
      if (!value || (value && (/(^[1-9]\d*$)/.test(value)))) {
        callback()
      } else {
        callback(new Error('请输入正整数!'))
      }
    }
    return {
      isloading: false,
      addPermission: checkPermission(['glImage_add']),
      deletePermission: checkPermission(['glImage_delete']),
      updatePermission: checkPermission(['glImage_modify']),
      imageTypeObj: [],
      imageTypeAddObj: [],
      topicObj: [],
      imageNum: 0,
      mainObj: {
        list: [],
        pageSize: 10,
        pageSizes: [10, 30, 50],
        currentPage: 1,
        total: 0,
        searchForm: { ...defaultSearchForm }
      },
      imageDialogObj: {
        visible: false,
        tile: '',
        type: '',
        form: Object.assign({}, defaultImageForm),
        rules: {
          imageType: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          sort: [
            { trigger: 'blur', validator: validateConfirmSort }
          ],
          imageFileId: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          imageRemark: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          url: [{ required: true, message: '图片不能为空', trigger: 'blur' }],
          topicId: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ]
        }
      },
      dialogImageUrl: '',
      dialogVisible: false
    }
  },
  created() {
    this.getImageTypeEnum()
    this.onSearch()
    this.getImageTypeAddEnum()
    this.getImageTopics()
    // this.getImageNum()
  },
  methods: {
    checkPermission,
    getImageNum() {
      sysManageApi.getImageNum().then(res => {
        this.imageNum = res.data
      })
    },
    getImageTypeEnum() {
      sysManageApi.getTypeEnum({ enumName: 'ImageTypeEnum' }).then(res => {
        this.imageTypeObj = res.data
      })
    },
    getImageTypeAddEnum() {
      sysManageApi.getTypeEnum({ enumName: 'ImageTypeAddEnum' }).then(res => {
        this.imageTypeAddObj = res.data
      })
    },
    getImageTopics() {
      sysManageApi.getImageTopics().then(res => {
        this.topicObj = res.data
      })
    },
    onSearch() {
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    onReset() {
      this.mainObj.searchForm = { ...defaultSearchForm }
      this.fetchData()
    },
    handleSizeChange(val) {
      this.mainObj.pageSize = val
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.mainObj.currentPage = val
      this.fetchData()
    },
    fetchData() {
      const params = {
        imageRemark: this.mainObj.searchForm.imageRemark,
        imageType: this.mainObj.searchForm.imageType,
        pageNum: this.mainObj.currentPage,
        pageSize: this.mainObj.pageSize
      }
      sysManageApi.getImageList(params).then((res) => {
        this.mainObj.list = res.data.list
        this.mainObj.total = res.data.total
      })
      this.getImageNum()
    },
    recursionTreeForCheckedKeys(tree) {
      const arr = []
      const rev = data => {
        data.forEach(e => {
          if (Array.isArray(e.subPermDetailDtoList) && e.subPermDetailDtoList.length > 0) {
            rev(e.subPermDetailDtoList)
          } else {
            arr.push(e.id)
          }
        })
      }
      rev(tree)
      return arr
    },
    onOperate(type, row) {
      if (type === 'add') {
        this.imageDialogObj.visible = true
        this.imageDialogObj.type = type
        this.imageDialogObj.title = '新增'
        this.imageDialogObj.form = Object.assign({}, defaultImageForm)
        if (this.imageTypeAddObj) {
          this.imageDialogObj.form.imageType = this.imageTypeAddObj[0].code
        }

        this.$nextTick(() => {
          this.$refs.imageFormRef.clearValidate()
        })
      } else if (type === 'update') {
        this.imageDialogObj.visible = true
        this.imageDialogObj.type = type
        this.imageDialogObj.title = '修改'
        this.imageDialogObj.form = {
          id: row.id,
          imageType: row.imageType.code,
          imageFileId: row.imageFileId,
          imageLink: row.imageLink,
          sort: row.sort,
          imageRemark: row.imageRemark,
          file: row.file,
          topicId: row.topicId,
          url: row.file.url
        }
      } else if (type === 'view') {
        this.imageDialogObj.visible = true
        this.imageDialogObj.type = type
        this.imageDialogObj.title = '查看'
        this.imageDialogObj.form = {
          allowDelFlag: row.allowDelFlag,
          imageFileId: row.imageFileId,
          imageLink: row.imageLink,
          imageName: row.imageName,
          id: row.id,
          sort: row.sort,
          imageRemark: row.imageRemark,
          imageType: row.imageType.code,
          file: row.file,
          topicId: row.topicId,
          topicName: row.topicName,
          url: row.file.url
        }
      } else if (type === 'submit') {
        if (this.imageDialogObj.form.imageType !== '2' && !this.imageDialogObj.form.id && this.imageNum >= 5) {
          this.$alert('首页轮播图不能多于5张')
          return
        }
        this.$refs.imageFormRef.validate((valid) => {
          if (valid) {
            this.isloading = true
            if (this.checkImageUpload()) {
              const params = {
                imageFileId: this.imageDialogObj.form.imageFileId,
                imageLink: this.imageDialogObj.form.url,
                sort: this.imageDialogObj.form.sort,
                imageRemark: this.imageDialogObj.form.imageRemark,
                imageType: this.imageDialogObj.form.imageType,
                id: this.imageDialogObj.form.id,
                topicId: this.imageDialogObj.form.topicId
              }
              if (params.imageType === '2') {
                params.sort = ''
                params.topicId = ''
              }

              sysManageApi.saveImage(params, this.imageDialogObj.type).then(res => {
                this.$message({
                  type: 'success',
                  message: res.msg
                })
                this.imageDialogObj.visible = false
                this.isloading = false
                this.fetchData()
              }).catch(() => {
                this.isloading = false
              })
            }
          }
        })
      } else if (type === 'delete') {
        this.$confirm('确定删除该图片吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const params = {
            id: row.id
          }
          sysManageApi.deleteImage(params).then((res) => {
            this.$message({
              type: 'success',
              message: res.msg
            })
            this.fetchData()
          })
        }).catch(error => {
          this.$message({
            type: 'info',
            message: error
          })
        })
      }
    },
    checkImageUpload() {
      const imageUrl = this.imageDialogObj.form.file.url
      if (imageUrl) {
        return true
      } else {
        this.isloading = false
        this.$refs.fileUpload.check()
        return false
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.main-body-bottom {
  box-sizing: border-box;
  padding: 20px;
}

.right {
  float: right;
}

.left {
  float: left;
}
</style>

