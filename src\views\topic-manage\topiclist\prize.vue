<template>
  <div>
    <el-dialog
      :title="disabled ? '奖品配置（已发奖）' : '奖品配置'"
      :visible.sync="dialogVisible"
      width="60%"
      top="4vh"
      :before-close="() => dialogVisible = false"
    >
      <div id="table-box">
        <el-table
          ref="tableRef"
          v-loading="listLoading"
          :data="prizeConfigDTOS"
          fit
          border
          highlight-current-row
          size="small"
          :header-cell-style="{ background: '#F9F9F9' }"
        >
          <el-table-column width="400" label="发放排名">
            <template slot-scope="scope">
              <div v-if="scope.row.input === 1" class="numberInput">
                <!-- e=e.replace(/[^0-9.]/g,' ') -->
                <el-input
                  v-model="scope.row.areaStart"
                  type="number"
                  style="width: 50%;text-align: center;"
                  :max="999"
                  :min="0"
                  :disabled="disabled"
                />
              </div>
              <div v-else class="numberInput">
                <el-input
                  v-model="scope.row.areaStart"
                  type="number"
                  style="width: 50%;text-align: center;"
                  :disabled="disabled"
                  :min="0"
                  :max="999"
                />
                <span style="padding:10px;display: inline-block;">-</span>
                <el-input
                  v-model="scope.row.areaEnd"
                  type="number"
                  style="width: 50%;text-align: center;"
                  :disabled="disabled"
                  :min="0"
                  :max="999"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column label="奖品">
            <template slot-scope="scope">
              <el-select
                :disabled="disabled"
                clearable
                :class="'tips' + scope.row.prizeId"
                :value="scope.row.prizeName"
                placeholder="选择奖品"
                filterable
                @change="changePrize($event, scope.row)"
              >
                <el-option
                  v-for="item in prizeList"
                  :key="item.id"
                  :disabled="havePrize.includes(item.id)"
                  :label="item.awardName"
                  :value="item.id"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column v-if="!disabled" label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="removeprize(scope.$index)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div v-if="showErr" class="tips__error"> 配置区间排名数额不可大于奖品数量 </div>
        <div v-if="!disabled" style="text-align: center;padding-top:10px">
          <el-button icon="el-icon-plus" type="primary" @click="hangSend(1)">添加单个发放</el-button>
          <el-button icon="el-icon-plus" type="primary" @click="hangSend(2)">添加区间发放</el-button>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="() => dialogVisible = false">关闭</el-button>
        <el-button v-if="!disabled" :loading="isloading" type="primary" @click="sendPrizes">确定</el-button>
        <el-button v-else type="primary" @click="exportExcel">导出获奖名单</el-button>
      </div>

    </el-dialog>
  </div>
</template>

<script>
import { searchPrize, topicPrizeConfig, searchTopicConfig, exportWinPrize } from '@/api/prizeManager'
import { downOrViewFile, formatDates } from '@/utils/index'
export default {
  data() {
    return {
      dialogVisible: false,
      prizeList: [],
      prizeConfigDTOS: [],
      topicId: '',
      exportName: '',
      disabled: false,
      showErr: false,
      isloading: false,
      listLoading: false
    }
  },
  computed: {
    havePrize() {
      const arr = []
      this.prizeConfigDTOS.forEach((item) => {
        if (item.prizeId) {
          arr.push(item.prizeId)
        }
      })
      return arr
    }
  },
  methods: {
    showDialo(row) {
      this.exportName = row.topicName
      this.topicId = row.id
      this.prizeList = []
      this.prizeConfigDTOS = []
      this.showErr = false
      this.dialogVisible = true
      if (row.awardStatus !== 'INIT') {
        this.disabled = true
      } else {
        this.disabled = false
      }
      searchPrize({ bindAwardLabel: false, pageNo: 1, pageSize: 999 }).then(res => {
        this.prizeList = res.data.data
      })
      this.listLoading = true
      searchTopicConfig({ topicId: this.topicId }).then(res => {
        this.listLoading = false
        const arr = res.data
        arr.forEach(item => {
          item.ruleType = 1
          if (item.areaStart === item.areaEnd) {
            item.input = 1
          } else {
            item.input = 2
          }
        })
        this.prizeConfigDTOS = arr
      }).catch(() => {
        this.listLoading = false
      })
    },
    hangSend(type) {
      let data
      if (type === 1) {
        data = { ruleType: 1, areaStart: '', areaEnd: '', prizeId: '', prizeName: '', input: 1 }
      } else {
        data = { ruleType: 1, areaStart: '', areaEnd: '', prizeId: '', prizeName: '', input: 2 }
      }
      this.prizeConfigDTOS.push(data)
    },
    removeprize(index) {
      this.prizeConfigDTOS.splice(index, 1)
    },

    changePrize(e, row) {
      row.prizeId = e
      if (e) {
        const item = this.prizeList.find((item) => { return item.id === e })
        row.prizeName = item.awardName
      } else {
        row.prizeName = ''
      }
    },

    sendPrizes() {
      const dom = document.querySelectorAll('#table-box .el-select .el-input input')
      dom.forEach(item => {
        item.style.borderColor = '#DCDFE6'
      })
      this.prizeConfigDTOS.forEach(item => {
        if (item.input === 1) {
          item.areaEnd = item.areaStart
        }
      })
      const list = []
      this.prizeConfigDTOS.map((item) => {
        const num = (Number(item.areaEnd) + 1) - Number(item.areaStart)
        const obj = this.prizeList.find(prizeItem => { return item.prizeId === prizeItem.id })
        if (obj && (obj.awardStock < num)) {
          list.push(item.prizeId)
        }
        return item
      })
      list.forEach(item => {
        document.querySelector(`#table-box .tips${item} .el-input input`).style.borderColor = 'red'
      })

      if (list.length > 0) {
        this.showErr = true
        return false
      } else {
        this.showErr = false
      }

      const resulet = this.prizeConfigDTOS.some((item) => { return item.prizeId === '' })
      if (resulet) {
        return this.$message.error('请选择奖品')
      }
      this.isloading = true
      topicPrizeConfig({ topicId: this.topicId, prizeConfigDTOS: this.prizeConfigDTOS }).then(res => {
        this.dialogVisible = false
        this.isloading = false
      }).catch(() => {
        this.isloading = false
      })
    },

    exportExcel() {
      exportWinPrize({ topicId: this.topicId }).then(res => {
        const that = this
        const data = res
        if (data.data.type === 'application/json') {
          const reader = new FileReader()
          reader.readAsText(data)
          reader.onload = function() {
            const { msg } = JSON.parse(reader.msg)
            // 处理错误
            that.$message.error(msg)
          }
        } else {
          downOrViewFile(res.data, this.exportName + '获奖名单-' + formatDates(new Date()) + '.xlsx')
        }
      }).catch(() => {
        this.$message({
          message: '暂无数据导出',
          type: 'error',
          duration: 5 * 1000
        })
      })
    }
  }

}
</script>

<style lang="scss" scoped>
.tips__error {
  color: #ff4949;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
}

.dialog-footer {
  text-align: center;
}

.numberInput {
  display: flex;
  align-items: center;
  justify-content: center;

  .el-input {
    ::v-deep input {
      text-align: center;
    }
  }
}
</style>
