<template>
  <el-dialog
    :title="config.title"
    :visible.sync="localDialogVisible"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="form"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      size="small"
    >
      <!-- 活动类型ID - 修改时显示 -->
      <el-form-item
        v-if="config.type === 'edit' || config.type === 'detail'"
        label="活动类型ID:"
      >
        <el-input
          v-model="formData.typeId"
          disabled
          placeholder="系统自动生成"
        />
      </el-form-item>

      <!-- 活动类型名称 -->
      <el-form-item label="活动类型名称:" prop="typeTitle">
        <el-input
          v-model="formData.typeTitle"
          :disabled="config.type === 'detail'"
          maxlength="10"
          show-word-limit
          placeholder="请输入活动类型名称"
        />
      </el-form-item>

      <!-- 用户端排序 -->
      <el-form-item label="用户端排序:" prop="sort">
        <el-input-number
          v-model="formData.sort"
          :disabled="config.type === 'detail'"
          :min="0"
          :max="999"
          controls-position="right"
          placeholder="请输入排序"
        />
        <div class="form-tips">
          用户端活动类型排序按此字段展示。从0开始，默认按列表最大排序自增。
        </div>
      </el-form-item>

      <!-- 状态 -->
      <el-form-item label="状态:" prop="actTypeStatus">
        <el-radio-group
          v-model="formData.actTypeStatus"
          :disabled="config.type === 'detail'"
        >
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
        <div class="form-tips">
          开启状态表示该活动类型以及活动均在用户端展示
        </div>
      </el-form-item>

      <!-- icon -->
      <el-form-item label="icon:" prop="typeCoverImg">
        <ImgUpload
          v-model="formData.typeCoverImg"
          :disabled="config.type === 'detail'"
          :proportion="1"
          file-type="kb"
          img-txt="上传图标"
          :file-size="500"
          accept="image/jpeg,image/jpg,image/png,image/gif,image/svg+xml"
        >
          <template #tips>
            <div class="form-tips">
              支持上传1张1:1尺寸的图，支持常规的图片格式（jpg、JPEG、png、gif、svg），大小不超过500k
            </div>
          </template>
        </ImgUpload>
      </el-form-item>

      <!-- 备注 -->
      <el-form-item label="备注:">
        <el-input
          v-model="formData.remark"
          :disabled="config.type === 'detail'"
          type="textarea"
          :rows="4"
          maxlength="200"
          show-word-limit
          placeholder="请输入备注（仅后管可见）"
        />
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button
        v-if="config.type !== 'detail'"
        type="primary"
        :loading="submitLoading"
        @click="handleSubmit"
      >保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import ImgUpload from '@/components/ImgUpload'
import { addActivityType, modifyActivityType, getActivityTypeDetail, getActivityType } from '@/api/actManage'

export default {
  name: 'ActivityTypeDialog',
  components: {
    ImgUpload
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    config: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      submitLoading: false,
      localDialogVisible: false,
      formData: {
        typeId: '',
        typeTitle: '',
        sort: 0,
        actTypeStatus: 1,
        typeCoverImg: '',
        remark: ''
      },
      formRules: {
        typeTitle: [
          { required: true, message: '请输入活动类型名称', trigger: 'blur' },
          { min: 1, max: 10, message: '长度在 1 到 10 个字符', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入用户端排序', trigger: 'blur' },
          { type: 'number', min: 0, message: '排序必须大于等于0', trigger: 'blur' }
        ],
        actTypeStatus: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ],
        typeCoverImg: [
          { required: true, message: '请上传icon', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    dialogVisible: {
      handler(val) {
        this.localDialogVisible = val
        if (val) {
          this.initForm()
        }
      },
      immediate: true
    },
    localDialogVisible(val) {
      if (!val) {
        this.$emit('update:dialogVisible', false)
      }
    }
  },
  methods: {
    initForm() {
      if (this.config.type === 'add') {
        // 新增时重置表单
        this.formData = {
          typeId: '',
          typeTitle: '',
          sort: 0,
          actTypeStatus: 1,
          typeCoverImg: '',
          remark: ''
        }
        // 获取最大排序值
        this.getMaxSort()
      } else if (this.config.formData) {
        // 编辑或详情时都获取完整数据
        if (this.config.type === 'detail' || this.config.type === 'edit') {
          // 获取完整数据，确保图片等信息完整
          this.getTypeDetail()
        } else {
          // 其他情况使用传入的数据
          this.formData = { ...this.config.formData }
        }
      }
    },
    // 获取活动类型详情
    getTypeDetail() {
      getActivityTypeDetail({ typeId: this.config.formData.typeId }).then(res => {
        this.formData = { ...res.data }
      }).catch(error => {
        console.error('获取详情失败:', error)
        this.$message.error('获取详情失败，请重试')
        this.handleClose()
      })
    },
    // 获取最大排序值
    getMaxSort() {
      // 获取当前最大排序值，默认从0开始
      getActivityType({ delFlag: '0', pageNum: 1, pageSize: 999 }).then(res => {
        if (res.data && res.data.list && res.data.list.length > 0) {
          const maxSort = Math.max(...res.data.list.map(item => item.sort || 0))
          this.formData.sort = maxSort + 1
        } else {
          this.formData.sort = 0
        }
      }).catch(() => {
        this.formData.sort = 0
      })
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.submitLoading = true
          const apiMethod = this.config.type === 'add' ? addActivityType : modifyActivityType
          const params = { ...this.formData }

          // 删除不需要的字段
          if (this.config.type === 'add') {
            delete params.typeId
            delete params.createTime
            delete params.updateTime
            delete params.createId
            delete params.updateId
            delete params.versionCt
          }

          apiMethod(params).then(res => {
            this.$message.success(res.msg || '操作成功')
            this.$emit('saveSuccess')
          }).catch(error => {
            console.error('保存失败:', error)
            this.$message.error(error.msg || '操作失败，请重试')
          }).finally(() => {
            this.submitLoading = false
          })
        }
      })
    },
    handleClose() {
      this.localDialogVisible = false
      this.$emit('handleClose')
    }
  }
}
</script>

<style lang="scss" scoped>
.form-tips {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
  line-height: 1.4;
}

::v-deep .el-input-number {
  width: 200px;
}
</style>
