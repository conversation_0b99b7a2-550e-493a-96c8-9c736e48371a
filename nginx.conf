#user  nobody;
user  root;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;

    client_body_buffer_size 25m;
    client_max_body_size 25m;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  1000;

    #gzip  on;

    server {
        listen       80;

        location /{
            root /usr/local/smk;
            index index.html index.htm;
			try_files $uri $uri/ @router;  #需要指向下面的@router否则会出现vue的路由在nginx中刷新出现404
        }

		location @router {
            rewrite ^.*$ /index.html last;
       }
	   
	    location ^~/community-manager-api/{
                proxy_pass http://************:7902/community-manager-api/;
        }



        #error_page  404              /404.html;

        # redirect server error pages to the static page /50x.html
        #
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
}
