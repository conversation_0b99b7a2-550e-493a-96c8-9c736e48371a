<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <div class="filter-container">
      <el-form :inline="true" :model="listQuery" class="demo-form-inline">
        <el-form-item label="活动ID">
          <el-input
            v-model="listQuery.id"
            placeholder="请输入活动编码"
            style="width: 200px;"
            clearable
          />
        </el-form-item>
        <el-form-item label="活动名称">
          <el-input
            v-model="listQuery.actTitle"
            placeholder="请输入活动名称"
            style="width: 200px;"
            clearable
          />
        </el-form-item>
        <el-form-item label="活动类型">
          <el-select v-model="listQuery.typeTitle" placeholder="请选择活动类型" clearable style="width: 150px">
            <el-option label="全部" value="" />
            <el-option
              v-for="item in activityTypeOptions"
              :key="item.id"
              :label="item.typeTitle"
              :value="item.typeTitle"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="活动状态">
          <el-select v-model="listQuery.activityStatus" placeholder="请选择活动状态" clearable style="width: 150px">
            <el-option label="全部" value="" />
            <el-option label="活动未开始" value="活动未开始" />
            <el-option label="活动进行中" value="活动进行中" />
            <el-option label="活动已结束" value="活动已结束" />
          </el-select>
        </el-form-item>
        <el-form-item label="上架状态">
          <el-select v-model="listQuery.actStatus" placeholder="请选择上架状态" clearable style="width: 150px">
            <el-option label="全部" value="" />
            <el-option label="草稿" value="2" />
            <el-option label="已上架" value="1" />
            <el-option label="已下架" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="报名状态">
          <el-select v-model="listQuery.registerStatus" placeholder="请选择报名状态" clearable style="width: 150px">
            <el-option label="全部" value="" />
            <el-option label="报名未开始" value="未开始" />
            <el-option label="报名进行中" value="进行中" />
            <el-option label="报名已结束" value="已结束" />
          </el-select>
        </el-form-item>
        <el-form-item label="活动门槛">
          <el-select v-model="listQuery.actThreshold" placeholder="请选择活动门槛" clearable style="width: 150px">
            <el-option label="全部" value="" />
            <el-option label="无门槛" value="无门槛" />
            <el-option label="私人银行客户" value="私人银行客户" />
            <el-option label="金卡客户" value="金卡客户" />
          </el-select>
        </el-form-item>
        <el-form-item label="活动时间">
          <el-date-picker
            v-model="activityTimeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 350px"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-ddTHH:mm:ss"
            @change="handleTimeRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 功能按钮 -->
    <div class="filter-container" style="margin-bottom: 10px;">
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="success"
        icon="el-icon-download"
        @click="handleExport"
      >
        导出
      </el-button>
    </div>

    <!-- 数据表格 -->
    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="活动ID" prop="id" width="100" align="center" />
      <el-table-column label="活动名称" prop="actTitle" min-width="150" />
      <el-table-column label="活动类型" prop="typeTitle" width="120" align="center" />
      <el-table-column label="活动门槛" prop="actThreshold" width="120" align="center" />
      <el-table-column label="活动名额" prop="numRage" width="100" align="center">
        <template slot-scope="{row}">
          <span>{{ row.numRage === 0 ? '不限制' : row.numRage }}</span>
        </template>
      </el-table-column>
      <el-table-column label="已报名数" prop="registerNum" width="100" align="center" />
      <el-table-column label="名额情况" prop="rageStatus" width="100" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.rageStatus === '已满员' ? 'danger' : 'success'">
            {{ row.rageStatus }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="签到人数" prop="signNum" width="100" align="center" />
      <el-table-column label="上架状态" prop="actStatus" width="120" align="center" />
      <el-table-column label="活动状态" prop="activityStatus" width="120" align="center" />
      <el-table-column label="报名状态" prop="registerStatus" width="120" align="center" />
      <el-table-column label="活动时间" prop="actTime" min-width="180" align="center" />
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getActivityReport, exportActivityReport } from '@/api/orderManage'
import { getActivityType } from '@/api/actManage'
import Pagination from '@/components/Pagination'
import { downOrViewFile, formatDates } from '@/utils'

export default {
  name: 'ActivityStatistics',
  components: {
    Pagination
  },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        id: '',
        actTitle: '',
        typeTitle: '',
        actStatus: '',
        createTimeStart: '',
        createTimeEnd: '',
        actThreshold: '',
        activityStatus: '',
        registerStatus: '',
        delFlag: 0
      },
      activityTimeRange: [],
      activityTypeOptions: [] // 活动类型选项
    }
  },
  created() {
    this.getList()
    this.getActivityTypeList()
  },
  methods: {
    getList() {
      this.listLoading = true
      const params = { ...this.listQuery }
      // 清空空值参数
      Object.keys(params).forEach(key => {
        if (params[key] === '') {
          delete params[key]
        }
      })

      getActivityReport(params).then(response => {
        console.log('活动统计API响应:', response)
        if (response.data) {
          if (Array.isArray(response.data)) {
            // 如果data直接是数组
            this.list = response.data
            this.total = response.data.length
          } else if (response.data.list && Array.isArray(response.data.list)) {
            // 如果data包含list数组
            this.list = response.data.list
            this.total = response.data.total || response.data.list.length
          } else if (response.data.records && Array.isArray(response.data.records)) {
            // 如果data包含records数组
            this.list = response.data.records
            this.total = response.data.total || response.data.records.length
          } else {
            this.list = []
            this.total = 0
          }
        } else {
          this.list = []
          this.total = 0
        }
        this.listLoading = false
      }).catch(error => {
        console.error('获取活动统计失败:', error)
        // 使用测试数据
        this.listLoading = false
        this.$message.warning('API接口暂不可用')
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    resetQuery() {
      this.listQuery = {
        page: 1,
        limit: 20,
        id: '',
        actTitle: '',
        typeTitle: '',
        actStatus: '',
        createTimeStart: '',
        createTimeEnd: '',
        actThreshold: '',
        activityStatus: '',
        registerStatus: '',
        delFlag: 0
      }
      this.activityTimeRange = []
      this.getList()
    },
    handleTimeRangeChange(value) {
      if (value && value.length === 2) {
        this.listQuery.createTimeStart = value[0]
        this.listQuery.createTimeEnd = value[1]
      } else {
        this.listQuery.createTimeStart = ''
        this.listQuery.createTimeEnd = ''
      }
    },
    handleExport() {
      const params = { ...this.listQuery }
      Object.keys(params).forEach(key => {
        if (params[key] === '') {
          delete params[key]
        }
      })

      exportActivityReport(params).then(response => {
        downOrViewFile(response, '活动统计报表_' + formatDates(new Date()) + '.xlsx')
        this.$message.success('导出成功')
      }).catch(error => {
        console.error('导出失败:', error)
        this.$message.error('导出失败，请重试')
      })
    },
    // 获取活动类型列表
    getActivityTypeList() {
      getActivityType({
        pageNum: 1,
        pageSize: 999,
        delFlag: 0 // 只获取未删除的活动类型
      }).then(response => {
        console.log('活动类型API响应:', response)
        if (response.data && response.data.list) {
          // 注意：状态字段是 actTypeStatus，1表示启用
          this.activityTypeOptions = response.data.list.filter(item => item.actTypeStatus === 1)
          console.log('过滤后的活动类型:', this.activityTypeOptions)
        }
      }).catch(error => {
        console.error('获取活动类型失败:', error)
      })
    }
  }
}
</script>

<style scoped>
.filter-container {
  padding-bottom: 10px;
}
.filter-container .filter-item {
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 10px;
}
</style>
