<template>
  <div>
    <RouteTitle />
    <div class="main-body">
      <div class="main-body-bottom">
        <div class="body-top-form">
          <el-form ref="searchForm" class="left" :model="mainObj.searchForm" label-position="left" inline>
            <el-form-item label="状态：">
              <el-select v-model="mainObj.searchForm.status" placeholder="请选择" clearable>
                <el-option v-for="item in statusEnumObj" :key="item.code" :label="item.text" :value="item.code" />
              </el-select>
            </el-form-item>
            <el-form-item label="用户昵称：">
              <el-input v-model="mainObj.searchForm.nickName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="用户姓名：">
              <el-input v-model="mainObj.searchForm.name" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="手机号：">
              <el-input v-model="mainObj.searchForm.mobile" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="标识名称：">
              <el-select v-model="mainObj.searchForm.markId" placeholder="请选择" clearable>
                <el-option v-for="item in marks" :key="item.markId" :label="item.markName" :value="item.markId" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">查询</el-button>
              <el-button @click="onReset">重置</el-button>
              <el-button type="primary" :icon="exportIcon" @click="exportUsers()">导出</el-button>
            </el-form-item>
          </el-form>
          <!-- <el-button
            class="right"
            type="primary"
            @click="operation('add')"
          >新增</el-button> -->
        </div>
        <div class="table">
          <el-table
            ref="tableRef"
            :data="mainObj.list"
            fit
            border
            highlight-current-row
            size="small"
            :header-cell-style="{ background: '#F9F9F9' }"
          >
            <el-table-column prop="ipAddress" label="序号" width="50">
              <template slot-scope="scope">
                <span>{{ transTime(scope.$index) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="id" label="用户ID" show-overflow-tooltip />
            <el-table-column prop="statusName" label="状态" />
            <el-table-column prop="name" label="用户姓名" width="150" />
            <el-table-column prop="nickName" label="用户昵称" width="150" />
            <el-table-column prop="mobile" label="手机号" width="150" />
            <el-table-column prop="markName" label="标识" />
            <el-table-column prop="postNum" label="发布数" />
            <el-table-column prop="createTime" label="首次使用时间" />
            <el-table-column prop="banTime" label="拉黑时间" />
            <el-table-column prop="reason" label="拉黑理由" width="260" show-overflow-tooltip />
            <el-table-column label="操作" fixed="right" width="180">
              <template slot-scope="scope">
                <el-button
                  v-if="scope.row.status === '1'"
                  type="text"
                  @click="operation('enable', scope.row)"
                >解除封禁</el-button>
                <el-button
                  v-if="scope.row.status === '0'"
                  type="text"
                  style="color:#e6a23c;"
                  @click="operation('disable', scope.row)"
                >拉黑</el-button>
                <el-button type="text" @click="operation('view', scope.row)">查看</el-button>
                <el-button type="text" @click="setchannel(scope.row)">标识</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination
            :current-page="mainObj.currentPage"
            :page-size="mainObj.pageSize"
            :page-sizes="mainObj.pageSizes"
            :total="mainObj.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <detail ref="detail" :type="type" :row="row" />
    <el-dialog title="拉黑用户" :visible.sync="dialogVisible" width="500px" top="4vh" :before-close="handleClose">
      <div>
        <div class="header">是否确定拉黑该用户?</div>
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          label-width="90px"
          class="demo-ruleForm"
          label-position="top"
          size="small"
        >
          <el-form-item label="拉黑理由" prop="reason">
            <el-input
              v-model="ruleForm.reason"
              type="textarea"
              resize="none"
              rows="6"
              maxlength="100"
              show-word-limit
              placeholder="请输入"
            />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="isloading" @click="submitForm">
          确定
        </el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="标识"
      :visible.sync="channelVisible"
      width="500px"
      top="4vh"
      :before-close="() => channelVisible = false"
    >
      <el-form ref="ruleFormchannel" :rules="ruleFormMarkid" :model="channelForm" label-width="90px">
        <el-form-item label="用户标识" prop="markId">
          <!-- <el-radio-group v-model="channelForm.markId">
            <el-radio v-for="item in marks" :key="item.markId" :label="item.markId">{{ item.markName }}</el-radio>
          </el-radio-group> -->
          <el-select v-model="channelForm.markId" filterable>
            <el-option v-for="item in marks" :key="item.markId" :label="item.markName" :value="item.markId" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="channelVisible = false">取消</el-button>
        <el-button type="primary" :loading="chnnelloading" @click="savechnnel"> 确定 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import sysManageApi from '@/api/sysManageApi'
import communityUser from '@/api/communityUser'
import checkPermission from '@/utils/permission'
import RouteTitle from '@/components/RouteTitle'
import detail from './detail'
import { downOrViewFile, formatDates } from '@/utils/index'
import { Message } from 'element-ui'
const defaultSearchForm = {
  status: '',
  name: '',
  nickName: '',
  mobile: '',
  markId: ''
}

const defaultUserForm = {
  name: '',
  mobile: ''
}
export default {
  components: {
    RouteTitle,
    detail
  },
  data() {
    return {
      isloading: false,
      statusEnumObj: [],
      ruleForm: {
        reason: ''
      },
      rules: {
        reason: [{ required: true, message: '不能为空', trigger: 'change' }]
      },
      ruleFormMarkid: {
        markId: [{ required: true, message: '请选择用户标识', trigger: 'change' }]
      },
      dialogVisible: false,
      row: {},
      type: '',
      // token: getToken(),
      detail: checkPermission(['communityUser']),

      infoId: '',
      dialogTableVisible: false,
      mainObj: {
        list: [],
        pageSize: 10,
        pageSizes: [10, 30, 50],
        currentPage: 1,
        total: 0,
        searchForm: { ...defaultSearchForm }
      },
      userDialogObj: {
        visible: false,
        title: '',
        type: '',
        form: Object.assign({}, defaultUserForm)
      },
      value: 1,
      exportIcon: '',
      channelVisible: false,
      chnnelloading: false,
      channelForm: {
        markId: '',
        id: ''
      },
      marks: []
    }
  },
  computed: {
    transTime() {
      return (index) => {
        return (this.mainObj.currentPage - 1) * this.mainObj.list.length + index + 1
      }
    }
  },
  created() {
    this.getUserStatusEnum()
    this.onSearch()
    communityUser.getUserMarks().then(res => {
      this.marks = res.data
    })
  },
  methods: {
    exportUsers() {
      this.exportIcon = 'el-icon-loading'
      const params = {
        status: this.mainObj.searchForm.status,
        mobile: this.mainObj.searchForm.mobile,
        nickName: this.mainObj.searchForm.nickName,
        markId: this.mainObj.searchForm.markId,
        name: this.mainObj.searchForm.name,
        pageNum: this.mainObj.currentPage,
        pageSize: this.mainObj.pageSize
      }
      communityUser.exportUser(params).then(res => {
        downOrViewFile(res.data, '社区用户' + formatDates(new Date()) + '.xlsx')
        this.exportIcon = ''
      }).catch(err => {
        console.log(err)
        Message({
          message: '无数据导出',
          type: 'error',
          duration: 5 * 1000
        })
        this.exportIcon = ''
      })
    },
    checkPermission,
    getUserStatusEnum() {
      sysManageApi.getTypeEnum({ enumName: 'ClientUserStatusEnum' }).then(res => {
        this.statusEnumObj = res.data
      })
    },
    onSearch() {
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    onReset() {
      this.mainObj.searchForm = { ...defaultSearchForm }
      this.fetchData()
    },
    handleSizeChange(val) {
      this.mainObj.pageSize = val
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.mainObj.currentPage = val
      this.fetchData()
    },
    getDetailInfo(row) {
      this.infoId = row.id.toString()
      this.dialogTableVisible = true
      if (this.$refs.child) {
        this.$refs.child.updatedInfo(row.id)
      }
    },
    fetchData() {
      const params = {
        status: this.mainObj.searchForm.status,
        mobile: this.mainObj.searchForm.mobile,
        nickName: this.mainObj.searchForm.nickName,
        markId: this.mainObj.searchForm.markId,
        name: this.mainObj.searchForm.name,
        pageNum: this.mainObj.currentPage,
        pageSize: this.mainObj.pageSize
      }
      communityUser.getUserList(params).then(res => {
        this.mainObj.list = res.data.list
        this.mainObj.total = Number(res.data.total)
      })
    },
    submitForm() {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          this.isloading = true
          communityUser.banUser({ id: this.row.id, reason: this.ruleForm.reason }).then(res => {
            this.handleClose()
            this.$message({
              type: 'success',
              message: '操作成功！'
            })
            this.isloading = false
            this.onSearch()
          })
        }
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
    operation(type, row = {}) {
      this.type = type
      this.row = row
      if (type === 'disable') {
        this.ruleForm.reason = ''
        this.dialogVisible = true
      }

      if (type === 'view') {
        this.$nextTick(() => {
          this.$refs.detail.dialogVisible = true
          this.$refs.detail.getUserList(row)
          // this.$refs.detail.onSearch()
          this.$refs.detail.activeName = 'first'
        })
      }

      if (type === 'enable') {
        this.$confirm(`确定要解除该用户封禁吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            communityUser.banUser({ id: this.row.id }).then(res => {
              this.onSearch()
              this.$message({
                type: 'success',
                message: '操作成功！'
              })
            })
          })
          .catch(() => { })
      }
    },
    setchannel(row) {
      this.channelForm = {
        markId: '',
        id: ''
      }
      this.channelVisible = true
      this.$nextTick(() => {
        this.$refs['ruleFormchannel'].clearValidate()
      })
      this.channelForm.id = row.id

      if (row.markId) {
        this.channelForm.markId = row.markId
      }
    },
    savechnnel() {
      this.$refs['ruleFormchannel'].validate(valid => {
        if (valid) {
          communityUser.editUserMark(this.channelForm).then(res => {
            this.onSearch()
            this.channelVisible = false
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.main-body-bottom {
  box-sizing: border-box;
  padding: 20px;
}

.right {
  float: right;
}

.left {
  float: left;
}

.body-top-form {
  overflow: hidden;
}

.header {
  height: 40px;
  line-height: 40px;
  width: 100%;
  box-sizing: border-box;
  padding: 0 0px;
}

::v-deep .el-dialog__body {
  padding: 0 20px;
}

::v-deep .el-dialog__footer {
  text-align: right;
}
</style>
