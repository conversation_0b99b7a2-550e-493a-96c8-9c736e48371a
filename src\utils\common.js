export function download(res) {
  const downloadElement = document.createElement('a')
  const downloadUrl = window.URL.createObjectURL(res.data)
  downloadElement.href = downloadUrl
  const name = res.headers['content-disposition']
  downloadElement.download = decodeURIComponent(name.substring(20, name.length))
  document.body.appendChild(downloadElement)
  downloadElement.click()
  document.body.removeChild(downloadElement)
  window.URL.revokeObjectURL(downloadUrl)
}
// 数据服务下载
export function servicedownload(res) {
  const downloadElement = document.createElement('a')
  const downloadUrl = window.URL.createObjectURL(res.data)
  downloadElement.href = downloadUrl
  // const name = res.headers['content-disposition']
  const name = res.headers.filename
  // const name = window.decodeURIComponent(filename)
  downloadElement.download = decodeURIComponent(name.substring(0, name.length))
  document.body.appendChild(downloadElement)
  downloadElement.click()
  document.body.removeChild(downloadElement)
  window.URL.revokeObjectURL(downloadUrl)
}

// 数据脱敏处理
export function maskData(data, type) {
  if (!data) return data

  switch (type) {
    case 'mobile':
      // 手机号脱敏：显示前3位和后4位，中间用*代替
      return data.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    case 'name':
      // 姓名脱敏：只显示第一个字符，其余用*代替
      return data.charAt(0) + '*'.repeat(data.length - 1)
    case 'idCard':
      // 身份证脱敏：显示前6位和后4位，中间用*代替
      return data.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
    default:
      return data
  }
}

// 获取文件完整访问URL
export function getFileUrl(url) {
  if (!url) return ''

  // 如果已经是完整URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }

  // 拼接前端域名地址
  const baseUrl = 'http://192.20.97.35:18188'
  return baseUrl + url
}

// 文件上传处理
export function handleFileUpload(file) {
  const formData = new FormData()
  formData.append('file', file)

  // 这里需要引入API，但为了避免循环依赖，建议在组件中直接调用API
  // 返回formData供组件使用
  return formData
}

