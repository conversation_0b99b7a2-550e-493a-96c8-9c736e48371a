<template>
  <div>
    <el-dialog
      :title="type === 'examine' ? '帖子待审核' : (type === 'view' ? '帖子详情' : '审核不通过')"
      :visible.sync="dialogVisible"
      width="780px"
      top="4vh"
      :before-close="handleClose"
    >
      <div v-loading="loading" class="detail" style="min-height:360px;">
        <div v-if="postObj">
          <div class="detail-head">
            <div style="font-size:16px;">
              <span v-show="postObj.topicName" style="font-weight: bold;">#{{ postObj.topicName }}#</span>
              <span>&nbsp;&nbsp;{{ postObj.title }}</span>
            </div>
            <div>
              状态：<span :style="postObj.status === '12' ? 'color:#1989fa' : ''">{{ transStatus(postObj.status) }}</span>
              <span>&nbsp;&nbsp;&nbsp;&nbsp;帖子ID：{{ row.id }}</span>
              <span v-if="type === 'view'">&nbsp;&nbsp;&nbsp;审核人：{{ postObj.verifyName }}</span>
              <span v-if="type === 'view'">&nbsp;&nbsp;&nbsp;审核时间：{{ postObj.applTime }}</span>
            </div>
            <div>
              <span>发帖人：{{ postObj.createName }}</span>
              <span>&nbsp;&nbsp;&nbsp;&nbsp;发帖人手机号：{{ postObj.mobile }}</span>
              <span v-if="type === 'view'">&nbsp;&nbsp;&nbsp;&nbsp;关联位置：{{ postObj.locationAddressDetail?postObj.locationAddressDetail:'-/空' }}</span>
            </div>
            <div v-if="type === 'view'">
              <span>点赞数：{{ postObj.likeNum }}</span>
              <span>&nbsp;&nbsp;&nbsp;&nbsp;评论数：{{ postObj.commentNum }}</span>
              <span>&nbsp;&nbsp;&nbsp;&nbsp;浏览量：{{ postObj.viewNum }}</span>
              <span>&nbsp;&nbsp;&nbsp;&nbsp;发帖时间：{{ postObj.createTime }}</span>
            </div>
          </div>
          <div class="content">
            <div>
              <Editor v-model="postObj.content" :default-config="{readOnly:true}" />
              <!-- {{ postObj.content }} -->
            </div>
            <div v-if="postObj.resourcesType !== '1' && postObj.photoUrl && postObj.photoUrl.length > 0" class="imgList">
              <ul>
                <el-row :gutter="10">
                  <el-col v-for="(item, index) in postObj.photoUrl" :key="index" :span="8">
                    <el-image class="imgLi" fit="cover" :src="item.url" @click="chnageImg(index)" />
                    <el-image-viewer
                      v-if="showViewer"
                      class="pre_img"
                      :on-close="closeViewer"
                      :initial-index="imgIndex"
                      :url-list="transImg(postObj.photoUrl)"
                    />
                  </el-col>
                </el-row>
              </ul>
            </div>
            <div v-if="postObj.resourcesType === '1'" class="video">
              <video controls style="border-radius:4px;width:100%;height:100%;" preload="meta">
                <source :src="postObj.photoUrl[0].url" type="video/mp4">
              </video>
            </div>
          </div>

          <div v-if="type === 'view'&&(postObj.goodsList&&postObj.goodsList.length>0)" style="display: flex;">
            <div class="title">关联商品</div>
            <div class="list-box">
              <div v-for="item in postObj.goodsList" :key="item.goodsId" class="item-box">
                <img class="goods-img" :src="item.goodsCover">
                <div style="padding:5px" class="item-title">{{ item.goodsName }}</div>
                <div style="padding-left:5px" class="item-title">￥{{ item.goodsCostPrice }}</div>
              </div>
            </div>
          </div>

          <div v-if="type === 'view'&&(postObj.appList&&postObj.appList.length>0)" style="display: flex;">
            <div class="title">关联应用</div>
            <div class="list-box">
              <div v-for="item in postObj.appList" :key="item.applyId" class="item-box">
                <img class="app-img" :src="item.iconUrl">
                <div style="padding-top:5px" class="app-title">{{ item.applyName }}</div>
              </div>
            </div>
          </div>

          <el-form
            v-if="postObj.status!=='12'"
            ref="ruleForm"
            :model="ruleForm"
            :rules="rules"
            label-width="120px"
            class="demo-ruleForm"
            label-position="top"
            size="small"
            :disabled="type === 'view'"
          >
            <el-form-item label="拒绝原因（审核拒绝必填）" prop="applRemark">
              <el-input
                v-model="ruleForm.applRemark"
                type="textarea"
                resize="none"
                rows="5"
                maxlength="300"
                show-word-limit
                placeholder="请输入"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <span v-if="type !== 'view'" slot="footer" class="dialog-footer">
        <el-button @click="submitForm(false)">审核拒绝</el-button>
        <el-button type="primary" :loading="isloading" @click="submitForm(true)">审核通过</el-button>
      </span>
      <span v-else slot="footer" class="dialog-footer">
        <!-- <el-button @click="dialogVisible = false">返 回</el-button> -->
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import { Editor } from '@wangeditor/editor-for-vue'
import '@wangeditor/editor/dist/css/style.css' // 引入 css
import { getPostDetail, approvePost } from '@/api/community'
export default {
  name: 'PostsDetail',
  components: {
    ElImageViewer,
    Editor
  },
  props: {
    type: {
      type: String,
      default: ''
    },
    row: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      isloading: false,
      imgIndex: 0,
      showViewer: false,
      loading: true,
      postObj: null,
      tableData: [],
      imageUrl: '',
      dialogVisible: false,
      ruleForm: {
        applRemark: ''
      },
      rules: {
        applRemark: [{ required: false, message: '不能为空', trigger: 'change' }]
      }
    }
  },
  computed: {
    transImg() {
      return imgList => {
        return imgList.map(item => item.url)
      }
    },
    transStatus() {
      return status => {
        return {
          12: '已上架',
          5: '已下架',
          10: '待审核',
          11: '审核拒绝',
          99: '已删除'
        }[status]
      }
    }
  },

  mounted() { },

  methods: {
    chnageImg(index) {
      this.imgIndex = index
      this.showViewer = true
      document.body.style.overflow = ''
    },
    onPreview() {
      this.showViewer = true
    },
    // 关闭查看器
    closeViewer() {
      this.showViewer = false
    },
    async getDetail() {
      this.postObj = null
      this.loading = true
      const data = await getPostDetail({ postId: this.row.id })
      this.postObj = data.data
      this.ruleForm.applRemark = this.postObj.applRemark
      this.loading = false
    },
    handleClose() {
      if (this.$refs['ruleForm']) {
        this.$refs['ruleForm'].resetFields()
      }
      this.dialogVisible = false
    },
    submitForm(isprove) {
      if (isprove) {
        this.isloading = true
        approvePost({
          applId: this.row.id,
          status: '12'
        }).then(res => {
          this.isloading = false
          this.$message({
            type: 'success',
            message: '审核成功！'
          })
          this.handleClose()
          this.$emit('onsearch')
        })
      } else {
        this.rules.applRemark[0].required = true
        this.$refs['ruleForm'].validate(valid => {
          if (valid) {
            this.isloading = true
            approvePost({
              applId: this.row.id,
              status: '11',
              applRemark: this.ruleForm.applRemark
            }).then(res => {
              this.$message({
                type: 'success',
                message: '操作成功！'
              })
              this.handleClose()
              this.isloading = false
              this.$emit('onsearch')
            })
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__footer {
  text-align: right;
}

.detail {
  width: 100%;
  box-sizing: border-box;
  padding: 10px 20px;
}

.line {
  text-align: center;
}

::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 110px;
  line-height: 110px;
  text-align: center;
}

::v-deep .avatar {
  width: 120px;
  height: 110px;
  display: block;
}

::v-deep .el-upload__tip {
  color: #999;
  margin-top: -9px;
}

.detail-head {
  line-height: 2.4;
  border-bottom: 1px solid #ddd;
}

::v-deep .el-dialog__body {
  padding: 0;
}

.content {
  padding: 20px 20px 0;
  line-height: 1.2;
  font-size: 14px;
}

.imgList {
  ul {
    padding: 0;

    li {
      list-style: none;
    }
  }
}

.video {
  width: 440px;
  height: 240px;
  margin: 0 auto;
}

.imgLi {
  height: 140px;
  width: 100%;
  object-fit: cover;
  margin-bottom: 8px;
  border-radius: 4px;
}

.demo-ruleForm {
  margin-top: 20px;
}

.title {
  font-weight: 700;
  padding: 20px 20px 20px 0px;
}
.list-box{
  display: flex;
  flex: 1;
  padding: 20px 0px 0px 0px;
}

.item-box {
  width: fit-content;
  margin-right: 20px;
}

.goods-img {
  width: 110px;
  height: 110px;

}

.item-title {
  font-size: 13px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 100px;
}

.app-img{
  width: 80px;
  height: 80px;
}
.app-title{
  width: 80px;
  font-size: 13px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: center;
}
</style>
