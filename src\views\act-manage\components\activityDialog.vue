<template>
  <el-dialog
    :title="config.title"
    :visible.sync="visible"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="120px"
      :disabled="config.type === 'detail'"
    >
      <el-row :gutter="20">
        <!-- 活动编号 - 仅修改时显示 -->
        <el-col v-if="config.type === 'edit' || config.type === 'detail'" :span="12">
          <el-form-item label="活动编号:">
            <el-input v-model="formData.id" disabled />
          </el-form-item>
        </el-col>

        <!-- 活动名称 -->
        <el-col :span="12">
          <el-form-item label="活动名称:" prop="actTitle">
            <el-input
              v-model="formData.actTitle"
              placeholder="请输入活动名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>

        <!-- 活动标题 -->
        <el-col :span="12">
          <el-form-item label="活动标题:" prop="actCaption">
            <el-input
              v-model="formData.actCaption"
              placeholder="请输入活动标题"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>

        <!-- 活动类型 -->
        <el-col :span="12">
          <el-form-item label="活动类型:" prop="typeTitle">
            <el-select
              v-model="formData.typeTitle"
              placeholder="请选择活动类型"
              style="width: 100%"
            >
              <el-option
                v-for="item in activityTypeOptions"
                :key="item.typeId"
                :label="item.typeTitle"
                :value="item.typeTitle"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <!-- 头图 -->
        <el-col :span="24">
          <el-form-item label="头图:" prop="headerImg">
            <ImgUpload
              v-model="formData.headerImg"
              :disabled="config.type === 'detail'"
              file-type="kb"
              img-txt="上传头图"
              :file-size="500"
              accept="image/jpeg,image/jpg,image/png,image/gif,image/svg+xml"
              @changeId="handleImageIdChange"
            />
          </el-form-item>
        </el-col>

        <!-- 排序 -->
        <el-col :span="6">
          <el-form-item label="排序:">
            <el-input-number
              v-model="formData.sort"
              :min="0"
              :max="9999"
              placeholder="默认为0"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <!-- 活动门槛 -->
        <el-col :span="24">
          <el-form-item label="活动门槛:" prop="actThreshold">
            <el-checkbox-group v-model="thresholdList">
              <el-checkbox label="ALL">所有用户</el-checkbox>
              <el-checkbox label="NOT_RATED">00未评级客户</el-checkbox>
              <el-checkbox label="NORMAL">01普通客户</el-checkbox>
              <el-checkbox label="POTENTIAL">02潜力客户</el-checkbox>
              <el-checkbox label="GOLD">03金卡客户</el-checkbox>
              <el-checkbox label="PLATINUM">04白金卡客户</el-checkbox>
              <el-checkbox label="DIAMOND">05钻石客户</el-checkbox>
              <el-checkbox label="PRIVATE_BANKING">06私人银行客户</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>

        <!-- 报名时间 -->
        <el-col :span="24">
          <el-form-item label="报名时间:" prop="registerTime">
            <el-date-picker
              v-model="registerTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="报名开始时间"
              end-placeholder="报名结束时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <!-- 活动时间 -->
        <el-col :span="24">
          <el-form-item label="活动时间:" prop="activityTime">
            <el-date-picker
              v-model="activityTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="活动开始时间"
              end-placeholder="活动结束时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <!-- 活动地点 -->
        <el-col :span="24">
          <el-form-item label="活动地点:" prop="location">
            <el-input
              v-model="formData.location"
              placeholder="请输入活动地点"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>

        <!-- 详细地址 -->
        <el-col :span="24">
          <el-form-item label="详细地址:">
            <el-input
              v-model="formData.localDetail"
              placeholder="请输入详细地址"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>

        <!-- 活动名额 -->
        <el-col :span="6">
          <el-form-item label="活动名额:" prop="numRage">
            <el-input-number
              v-model="formData.numRage"
              :min="0"
              :max="99999"
              placeholder="0表示不限制"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <!-- 活动须知 -->
        <el-col :span="24">
          <el-form-item label="活动须知:" prop="actNotice">
            <el-input
              v-model="formData.actNotice"
              type="textarea"
              :rows="4"
              placeholder="请输入活动须知"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-col>

        <!-- 活动介绍 -->
        <el-col :span="24">
          <el-form-item label="活动介绍:" prop="actDesc">
            <tinymce v-model="formData.actDesc" :height="300" placeholder="请输入活动介绍" />
          </el-form-item>
        </el-col>

        <!-- 报名填写项 -->
        <el-col :span="24">
          <el-form-item label="报名填写项:">
            <RegistrationForm v-model="registrationFormData" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">{{ config.type === 'detail' ? '返回' : '取消' }}</el-button>
      <el-button
        v-if="config.type !== 'detail'"
        type="primary"
        :loading="saving"
        @click="handleSave"
      >保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addActivity, modifyActivity, getActivityType, getActivityDetail } from '@/api/actManage'
import { getToken } from '@/utils/auth'
import ImgUpload from '@/components/ImgUpload'
import RegistrationForm from './registrationForm.vue'
import Tinymce from '@/components/Tinymce/index.vue'

export default {
  name: 'ActivityDialog',
  components: {
    RegistrationForm,
    ImgUpload,
    Tinymce
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    config: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      visible: this.dialogVisible,
      saving: false,
      activityTypeOptions: [],
      uploadUrl: process.env.VUE_APP_BASE_API + '/file/upload/file',
      uploadHeaders: {
        Authorization: 'Bearer ' + getToken()
      },
      fileList: [],
      thresholdList: [],
      registerTime: [],
      activityTime: [],
      registrationFormData: {},
      formData: {
        id: null,
        actTitle: '',
        actCaption: '',
        typeTitle: '',
        headerImg: '',
        headerImgId: null,
        sort: 0,
        actThreshold: '',
        numRage: 0,
        location: '',
        localDetail: '',
        actNotice: '',
        actDesc: '',
        registerStartTime: '',
        registerEndTime: '',
        actStartTime: '',
        actEndTime: ''
      },
      rules: {
        actTitle: [
          { required: true, message: '请输入活动名称', trigger: 'blur' }
        ],
        actCaption: [
          { required: true, message: '请输入活动标题', trigger: 'blur' }
        ],
        typeTitle: [
          { required: true, message: '请选择活动类型', trigger: 'change' }
        ],
        headerImg: [
          { required: true, message: '请上传头图', trigger: 'change' }
        ],
        actThreshold: [
          {
            required: true, 
            validator: (_, __, callback) => {
              if (!this.thresholdList || this.thresholdList.length === 0) {
                callback(new Error('请选择活动门槛'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        registerTime: [
          {
            required: true, 
            validator: (_, __, callback) => {
              if (!this.registerTime || this.registerTime.length !== 2) {
                callback(new Error('请选择报名时间'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        activityTime: [
          {
            required: true, 
            validator: (_, __, callback) => {
              if (!this.activityTime || this.activityTime.length !== 2) {
                callback(new Error('请选择活动时间'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        location: [
          { required: true, message: '请输入活动地点', trigger: 'blur' }
        ],
        numRage: [
          { required: true, message: '请输入活动名额', trigger: 'blur' }
        ],
        actNotice: [
          { required: true, message: '请输入活动须知', trigger: 'blur' }
        ],
        actDesc: [
          { required: true, message: '请输入活动介绍', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    dialogVisible: {
      handler(val) {
        this.visible = val
        if (val) {
          this.initData()
        }
      },
      immediate: true
    },
    thresholdList: {
      handler(val) {
        this.formData.actThreshold = val.join(',')
      },
      deep: true
    },
    registerTime: {
      handler(val) {
        if (val && val.length === 2) {
          this.formData.registerStartTime = val[0]
          this.formData.registerEndTime = val[1]
        }
      },
      deep: true
    },
    activityTime: {
      handler(val) {
        if (val && val.length === 2) {
          this.formData.actStartTime = val[0]
          this.formData.actEndTime = val[1]
        }
      },
      deep: true
    }
  },
  created() {
    this.loadActivityTypes()
  },
  mounted() {
    if (this.dialogVisible) {
      this.initData()
    }
  },
  methods: {
    // 初始化数据
    initData() {
      if (this.config.formData) {
        // 详情、编辑、复制都需要获取完整的活动详情
        if (this.config.type === 'detail' || this.config.type === 'edit' || this.config.isCopy) {
          this.getActivityDetail()
        } else {
          // 新增时使用传入的数据或重置表单
          this.setFormData(this.config.formData)
        }
      } else {
        this.resetForm()
      }
    },

    // 获取活动详情
    async getActivityDetail() {
      try {
        const res = await getActivityDetail(this.config.formData.id)
        if (res.code === 200 && res.data) {
          const data = { ...res.data }

          // 如果是复制操作，清空ID相关字段
          if (this.config.isCopy) {
            data.id = null
            data.createId = null
            data.createTime = null
            data.updateId = null
            data.updateTime = null
            data.versionCt = null
          }

          this.setFormData(data)
        } else {
          this.$message.error('获取活动详情失败')
          this.handleClose()
        }
      } catch (error) {
        console.error('获取活动详情失败:', error)
        this.$message.error('获取活动详情失败，请重试')
        this.handleClose()
      }
    },

    // 设置表单数据
    setFormData(data) {
      this.formData = { ...data }

      // 处理活动门槛
      if (this.formData.actThreshold) {
        this.thresholdList = this.formData.actThreshold.split(',')
      }

      // 处理时间 - 转换ISO格式为Element UI期望的格式
      if (this.formData.registerStartTime && this.formData.registerEndTime) {
        const registerStart = this.formatDateTime(this.formData.registerStartTime)
        const registerEnd = this.formatDateTime(this.formData.registerEndTime)
        this.registerTime = [registerStart, registerEnd]
      }
      if (this.formData.actStartTime && this.formData.actEndTime) {
        const actStart = this.formatDateTime(this.formData.actStartTime)
        const actEnd = this.formatDateTime(this.formData.actEndTime)
        this.activityTime = [actStart, actEnd]
      }

      // 处理图片
      if (this.formData.headerImg) {
        this.fileList = [{
          name: 'image',
          url: this.formData.headerImg,
          uid: Date.now()
        }]
      }

      // 处理报名填写项
      if (this.formData.registerInfo) {
        try {
          const parsedData = JSON.parse(this.formData.registerInfo)

          // 如果解析后是数组，需要转换为对象格式
          if (Array.isArray(parsedData)) {
            this.registrationFormData = this.convertArrayToFormData(parsedData)
          } else if (parsedData && typeof parsedData === 'object') {
            this.registrationFormData = parsedData
          } else {
            this.registrationFormData = {}
          }
        } catch (error) {
          console.error('解析报名信息失败:', error)
          this.registrationFormData = {}
        }
      } else {
        this.registrationFormData = {}
      }
    },

    // 重置表单
    resetForm() {
      this.formData = {
        id: null,
        actTitle: '',
        actCaption: '',
        typeTitle: '',
        headerImg: '',
        headerImgId: null,
        sort: 0,
        actThreshold: '',
        numRage: 0,
        location: '',
        localDetail: '',
        actNotice: '',
        actDesc: '',
        registerStartTime: '',
        registerEndTime: '',
        actStartTime: '',
        actEndTime: ''
      }
      this.thresholdList = []
      this.registerTime = []
      this.activityTime = []
      this.fileList = []
      this.registrationFormData = {}
    },

    // 加载活动类型
    async loadActivityTypes() {
      try {
        const res = await getActivityType({
          delFlag: '0',
          actTypeStatus: 1
        })
        if (res.code === 200) {
          this.activityTypeOptions = res.data.list || []
        }
      } catch (error) {
        console.error('加载活动类型失败:', error)
      }
    },

    // 处理图片ID变化
    handleImageIdChange(imageId) {
      this.formData.headerImgId = imageId
    },

    // 上传前验证
    beforeUpload(file) {
      const isValidType = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/svg+xml'].includes(file.type)
      const isLt500K = file.size / 1024 < 500

      if (!isValidType) {
        this.$message.error('只能上传 JPG、PNG、JPEG、GIF、SVG 格式的图片!')
        return false
      }
      if (!isLt500K) {
        this.$message.error('图片大小不能超过 500KB!')
        return false
      }
      return true
    },

    // 上传成功
    handleUploadSuccess(response, file, fileList) {
      if (response.code === 200) {
        this.formData.headerImg = response.data.url
        this.formData.headerImgId = response.data.id
        this.$message.success('上传成功')
      } else {
        this.$message.error(response.msg || '上传失败')
        // 移除失败的文件
        this.fileList = fileList.filter(item => item.uid !== file.uid)
      }
    },

    // 移除图片
    handleRemove(_, fileList) {
      this.fileList = fileList
      if (fileList.length === 0) {
        this.formData.headerImg = ''
        this.formData.headerImgId = null
      }
    },

    // 验证时间
    validateTime() {
      if (this.registerTime.length === 2 && this.activityTime.length === 2) {
        const registerEnd = new Date(this.registerTime[1])
        const activityStart = new Date(this.activityTime[0])

        if (registerEnd > activityStart) {
          this.$message.error('活动开始时间不得早于报名结束时间，请重新设置')
          return false
        }
      }
      return true
    },

    // 保存
    handleSave() {
      this.$refs.form.validate(async(valid) => {
        if (!valid) return

        if (!this.validateTime()) return

        // 验证图片是否已上传
        if (!this.formData.headerImgId) {
          this.$message.error('请先上传头图')
          return
        }

        this.saving = true
        try {
          const isEdit = this.config.type === 'edit'
          const apiMethod = isEdit ? modifyActivity : addActivity

          const submitData = { ...this.formData }

          // 处理报名填写项数据
          if (this.registrationFormData && Object.keys(this.registrationFormData).length > 0) {
            submitData.activityUserDTO = this.convertToActivityUserDTO(this.registrationFormData)
          }

          // 移除不需要的字段
          delete submitData.registerInfo
          delete submitData.location  // 接口文档中没有这个字段

          // 新增时不传 id
          if (!isEdit) {
            delete submitData.id
          }

          const res = await apiMethod(submitData)
          if (res.code === 200) {
            this.$message.success(isEdit ? '修改成功' : '新增成功')
            this.$emit('saveSuccess')
            this.handleClose()
          } else {
            this.$message.error(res.msg || (isEdit ? '修改失败' : '新增失败'))
          }
        } catch (error) {
          console.error('保存失败:', error)
          this.$message.error('保存失败，请重试')
        } finally {
          this.saving = false
        }
      })
    },

    // 转换报名信息为接口格式
    convertToActivityUserDTO(registrationData) {
      const activityUserDTO = []

      // 处理系统预设项目
      if (registrationData.presetItems) {
        const presetItems = registrationData.presetItems
        const fieldMapping = {
          name: { title: '姓名', key: 'name', type: 0 },
          mobile: { title: '手机号', key: 'mobile', type: 0 },
          idNo: { title: '身份证', key: 'idNo', type: 0 },
          age: { title: '年龄', key: 'age', type: 0 },
          gender: { title: '性别', key: 'gender', type: 2 },
          cardType: { title: '证件类型', key: 'cardType', type: 2 },
          relative: { title: '携带亲属', key: 'relative', type: 0 },
          height: { title: '身高', key: 'height', type: 0 },
          weight: { title: '体重', key: 'weight', type: 0 },
          education: { title: '学历', key: 'education', type: 2 }
        }

        Object.keys(fieldMapping).forEach(key => {
          const item = presetItems[key]
          if (item && item.enabled) {
            const dto = {
              title: fieldMapping[key].title,
              key: fieldMapping[key].key,
              type: fieldMapping[key].type,
              required: item.required
            }

            // 处理选项类型的字段
            if (item.options && Array.isArray(item.options)) {
              dto.text = item.options.join(',')
            }

            // 处理携带亲属的特殊字段
            if (key === 'relative') {
              if (item.adultLimit !== null && item.adultLimit !== undefined) {
                dto.human = item.adultLimit
              }
              if (item.childLimit !== null && item.childLimit !== undefined) {
                dto.child = item.childLimit
              }
            }

            activityUserDTO.push(dto)
          }
        })
      }

      // 处理自增项目
      if (registrationData.customItems && Array.isArray(registrationData.customItems)) {
        registrationData.customItems.forEach((item, index) => {
          if (item.enabled) {
            const dto = {
              title: item.name,
              key: `custom_${index + 1}_${item.componentType}`, // 生成唯一key
              type: this.getTypeByComponentType(item.componentType),
              required: item.required
            }

            // 处理选项
            if (item.options && Array.isArray(item.options)) {
              dto.text = item.options.join(',')
            }

            activityUserDTO.push(dto)
          }
        })
      }

      return activityUserDTO
    },

    // 根据组件类型获取type值
    getTypeByComponentType(componentType) {
      const typeMapping = {
        text: 0,      // 单行文本
        textarea: 1,  // 多行文本
        select: 2     // 单选下拉框
      }
      return typeMapping[componentType] || 0
    },

    // 将数组格式的报名信息转换为对象格式
    convertArrayToFormData(arrayData) {
      const formData = {
        presetItems: {
          name: { enabled: true, required: true },
          mobile: { enabled: true, required: true },
          idNo: { enabled: false, required: false },
          age: { enabled: false, required: false },
          gender: { enabled: false, required: false, options: ['男', '女', '其他'] },
          cardType: { enabled: false, required: false, options: ['身份证', '护照', '其他'] },
          relative: { enabled: false, required: false, adultLimit: null, childLimit: null },
          height: { enabled: false, required: false },
          weight: { enabled: false, required: false },
          education: { enabled: false, required: false, options: ['初中', '高中', '大专', '本科', '硕士', '博士', '其他'] }
        },
        customItems: []
      }

      // 遍历数组数据，映射到对应的字段
      arrayData.forEach(item => {
        const { key, title, type, required, text, human, child } = item

        // 处理系统预设项目
        const presetMapping = {
          name: 'name',
          mobile: 'mobile',
          idNo: 'idNo',
          age: 'age',
          gender: 'gender',
          cardType: 'cardType',
          relative: 'relative',
          height: 'height',
          weight: 'weight',
          education: 'education'
        }

        const presetKey = presetMapping[key]
        if (presetKey && formData.presetItems[presetKey]) {
          formData.presetItems[presetKey].enabled = true
          formData.presetItems[presetKey].required = required || false

          // 处理选项类型的字段
          if (text && ['gender', 'cardType', 'education'].includes(presetKey)) {
            formData.presetItems[presetKey].options = text.split(',')
          }

          // 处理携带亲属的特殊字段
          if (presetKey === 'relative') {
            if (human !== null && human !== undefined) {
              formData.presetItems[presetKey].adultLimit = human
            }
            if (child !== null && child !== undefined) {
              formData.presetItems[presetKey].childLimit = child
            }
          }
        } else if (key && key.startsWith('custom_')) {
          // 处理自定义项目
          const componentType = type === 0 ? 'text' : type === 1 ? 'textarea' : 'select'
          formData.customItems.push({
            name: title,
            componentType: componentType,
            required: required || false,
            enabled: true,
            options: text ? text.split(',') : []
          })
        }
      })

      return formData
    },

    // 格式化日期时间 - 将ISO格式转换为Element UI期望的格式
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return ''
      
      try {
        const date = new Date(dateTimeStr)
        if (isNaN(date.getTime())) {
          // 如果不是有效的日期，尝试直接返回原字符串
          return dateTimeStr
        }
        
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        const seconds = String(date.getSeconds()).padStart(2, '0')
        
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      } catch (error) {
        console.error('时间格式转换失败:', error)
        return dateTimeStr
      }
    },

    // 关闭弹窗
    handleClose() {
      this.visible = false
      this.$emit('handleClose')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .el-dialog__body {
    max-height: 70vh;
    overflow-y: auto;
  }
}

::v-deep .el-upload--picture-card {
  width: 100px;
  height: 100px;
  line-height: 100px;
}

::v-deep .el-upload-list--picture-card .el-upload-list__item {
  width: 100px;
  height: 100px;
}

.el-checkbox-group {
  .el-checkbox {
    margin-right: 15px;
    margin-bottom: 10px;
  }
}
</style>
