<template>
  <div>
    <el-dialog
      :title="type === 'edit' ? '编辑马甲' : '新增马甲'"
      :visible.sync="dialogVisible"
      width="580px"
      top="4vh"
      :before-close="handleClose"
      destroy-on-close
    >
      <div class="detail">
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          label-width="120px"
          class="demo-ruleForm"
          label-position="right"
          size="small"
          :disabled="type === 'view'"
        >
          <el-form-item label="马甲昵称：" prop="nickName">
            <el-input
              v-model="ruleForm.nickName"
              placeholder="请输入昵称"
              maxlength="10"
            />
          </el-form-item>
          <el-form-item label="关联手机号：" prop="relMobile">
            <el-input
              v-model="ruleForm.relMobile"
              :disabled="phoneDisabled"
              placeholder="请输入手机号"
              maxlength="20"
            />
          </el-form-item>
          <el-form-item label="马甲头像：">
            <div class="upload-box">
              <div v-if="imageUrl" class="upload">
                <img
                  :src="imageUrl"
                  class="el-upload-list__item-thumbnail"
                  style="object-fit: cover;"
                >
                <span class="el-upload-list__item-actions">
                  <span
                    class="el-upload-list__item-preview"
                    @click="handlePictureCardPreview"
                  >
                    <i class="el-icon-zoom-in" />
                  </span>
                  <span
                    class="el-upload-list__item-delete"
                    @click="handleRemove"
                  >
                    <i class="el-icon-delete" />
                  </span>
                </span>
              </div>
              <div v-if="imageUrl" class="el-upload__tip">头像尺寸102X102px，传输格式是jpg、jpeg、png、bmp，大小不超过1M</div>
              <el-upload
                v-else
                :class="['avatar-uploader', {'uplaodto': type === 'view'}]"
                action="#"
                :http-request="uploadImage"
                :before-upload="(file) => {return beforeUpload(file)}"
                accept=".jpg,.jpeg,.png,.bmp"
                :file-list="fileList"
                :show-file-list="false"
                :limit="1"
                :on-change="handleChange"
              >
                <img v-if="imageUrl" :src="imageUrl" class="avatar" style="object-fil:cover;">
                <i v-else class="el-icon-plus avatar-uploader-icon" />
                <div slot="tip" class="el-upload__tip">头像尺寸102X102px，传输格式是jpg、jpeg、png、bmp，大小不超过1M</div>
              </el-upload>
            </div>
          </el-form-item>
          <el-form-item label="状态：" prop="status">
            <el-radio-group v-model="ruleForm.status">
              <el-radio label="0">启用</el-radio>
              <el-radio label="1">禁用</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="用户标识：" prop="markId">
            <!-- <el-radio-group v-model="ruleForm.markId">
              <el-radio v-for="item in marks" :key="item.markId" :label="item.markId">{{ item.markName }}</el-radio>
            </el-radio-group> -->

            <el-select v-model="ruleForm.markId" filterable>
              <el-option v-for="item in marks" :key="item.markId" :label="item.markName" :value="item.markId" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <span v-if="type !== 'view'" slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button
          type="primary"
          @click="submitForm"
        >确 定</el-button>
      </span>
      <span v-else slot="footer" class="dialog-footer">
        <el-button @click="handleClose">返 回</el-button>
      </span>
    </el-dialog>
    <el-dialog title="图片预览" :visible.sync="imgView" :modal-append-to-body="false">
      <img width="100%" :src="imageUrl" alt="">
    </el-dialog>
  </div>
</template>

<script>
import community from '@/api/community'
import communityUser from '@/api/communityUser'
export default {
  name: 'VirtualUserDetail',
  props: {
    type: {
      type: String,
      default: ''
    },
    row: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {
      imgView: false,
      imageUrl: '',
      dialogVisible: false,
      ruleForm: {
        nickName: '',
        relMobile: '',
        markId: '',
        status: '0',
        avatar: '',
        id: ''
      },
      rules: {
        nickName: [{ required: true, message: '不能为空', trigger: 'blur' },
          {
            validator: function(rule, value, callback) {
              if (/^[\u4E00-\u9FA5A-Za-z0-9]+$/.test(value) === false) {
                callback(new Error('仅支持中英文数字'))
              } else {
              // 校验通过
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        relMobile: [{ required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3|5|7|8|9]\d{9}$/, message: '请输入正确的号码格式', trigger: 'blur' }
        ],
        status: [{ required: true, message: '不能为空', trigger: 'blur' }],
        markId: [{ required: true, message: '请选择用户标识', trigger: 'blur' }]
      },
      fileList: [],
      phoneDisabled: false,
      marks: []
    }
  },

  mounted() {

  },
  methods: {
    handlePictureCardPreview() {
      this.imgView = true
    },
    handleRemove() {
      this.imageUrl = ''
      this.ruleForm.coverImgId = ''
    },
    /**
     * @method: 限制上传文件格式和大小
     */
    beforeUpload(file) {
      const isJPEG = file.type === 'image/jpeg'
      const isJPG = file.type === 'image/jpg'
      const isPNG = file.type === 'image/png'
      // const isGIF = file.type === 'image/gif'
      const isBMP = file.type === 'image/bmp'
      const isLt1M = file.size / 1024 / 1024 < 2
      if (!isJPEG && !isJPG && !isPNG && !isBMP) {
        this.$message.error('上传文件只能是 JPG, JPEG, PNG, BMP 格式!')
        this.clearFile()
        return false
      }
      if (!isLt1M) {
        this.$message.error('上传文件大小不能超过 1MB!')
        this.clearFile()
        return false
      }
    },
    handleChange() {
      this.fileList = []
    },
    getUserList(row) {
      communityUser.getUserMarks().then(res => {
        this.marks = res.data
      })
      if (row) {
        this.ruleForm.nickName = row.nickName
        this.ruleForm.relMobile = row.relMobile
        this.ruleForm.status = row.status
        this.imageUrl = row.avatar
        this.ruleForm.markId = row.markId
        this.phoneDisabled = true
        this.ruleForm.id = row.id
      } else {
        this.ruleForm.nickName = ''
        this.ruleForm.relMobile = ''
        this.ruleForm.status = '0'
        this.imageUrl = ''
        this.phoneDisabled = false
        this.ruleForm.id = ''
        this.ruleForm.markId = ''
      }
    },
    uploadImage(file) {
      const formData = new FormData()
      formData.append('file', file.file)
      // formData.append('access', 1)
      community.uploadFile(formData).then(res => {
        this.imageUrl = res.data.url
        this.ruleForm.coverImgId = res.data.id
      })
    },

    handleClose() {
      this.imageUrl = ''
      this.$nextTick(() => {
        this.$refs['ruleForm'].resetFields()
      })
      this.dialogVisible = false
    },
    submitForm() {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          const obj = JSON.parse(JSON.stringify(this.ruleForm))
          const params = {
            nickName: obj.nickName,
            relMobile: obj.relMobile,
            markId: obj.markId,
            status: obj.status,
            avatar: this.imageUrl,
            id: obj.id
          }
          communityUser.saveVirtualUser(params).then(res => {
            this.$message({
              type: 'success',
              message: `${this.type === 'add' ? '新增' : '编辑'}成功！`
            })
            this.handleClose()
            this.$emit('fetchdata')
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__footer {
  text-align: right;
}

.detail {
  width: 100%;
  box-sizing: border-box;
  padding: 0 60px 0 0;
}

.line {
  text-align: center;
}

::v-deep .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
 ::v-deep .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
 ::v-deep .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 110px;
    line-height: 110px;
    text-align: center;
  }
 ::v-deep .avatar {
    width: 120px;
    height: 110px;
    display: block;
    object-fit: cover;
  }

  ::v-deep .el-upload__tip{
      color:#999;
      margin-top: -9px;
  }

::v-deep .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
 ::v-deep .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
 ::v-deep .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    line-height: 110px;
    text-align: center;
  }
 ::v-deep .avatar {
    width: 120px;
    height: 120px;
    display: block;
    object-fit: cover;
  }

  ::v-deep .el-upload__tip{
      color:#999;
      margin-top: -9px;
  }
  .upload-box{
    width: 100%;
  }
  .upload {
    overflow: hidden;
    background-color: #fff;
    border: 1px solid #D6DCDF;
    border-radius: 6px;
    box-sizing: border-box;
    width: 120px;
    height: 120px;
    margin-bottom: 8px;
    display: inline-block;
    color: #606266;
    line-height: 110px;
    position: relative;
    .el-upload-list__item-thumbnail {
      width: 100%;
      height: 100%;
    }
    .el-upload-list__item-actions {
      position: absolute;
      width: 100%;
      height: 100%;
      line-height: 120px;
      left: 0;
      top: 0;
      cursor: default;
      text-align: center;
      color: #fff;
      opacity: 0;
      font-size: 20px;
      background-color: rgba(0,0,0,.5);
      transition: opacity .3s;
      span {
        display: none;
        cursor: pointer;
      }
      span+span {
        margin-left: 15px;
      }
      .el-upload-list__item-delete {
        right: 10px;
        top: 0;
        display: none;
        position: static;
        font-size: inherit;
        color: inherit;
      }
      ::after {
        display: inline-block;
        content: "";
        height: 100%;
        vertical-align: middle;
      }
      &:hover {
        opacity: 1;
        span {
          display: inline-block;
        }
      }
    }
  }
</style>
