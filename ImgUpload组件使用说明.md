# ImgUpload 组件使用说明

## 组件概述

`ImgUpload` 是一个自定义的图片上传组件，位于 `src/components/ImgUpload/index.vue`，提供图片上传、预览、删除等功能。

## 组件属性 (Props)

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| value | String | '' | 图片URL，支持v-model双向绑定 |
| src | String | '' | 图片源地址 |
| accept | String | 'image/jpeg,image/jpg,image/png,image/gif' | 接受的文件类型 |
| disabled | Boolean | false | 是否禁用上传 |
| imgTxt | String | '' | 上传按钮显示文字 |
| tips | String | '只能上传jpeg、jpg、png、gif 格式的图片，图片大小在2M以内。' | 提示文字 |
| fileSize | Number | 2 | 文件大小限制 |
| fileType | String | 'MB' | 文件大小单位 (MB/kb) |
| proportion | Number | 0 | 图片宽高比例限制 (如1表示1:1) |
| imgWidth | Number | 0 | 图片宽度限制 |
| imgHeight | Number | 0 | 图片高度限制 |
| imgSize | Object | {} | 图片尺寸限制对象 |

## 在 activityTypeDialog.vue 中的使用

```vue
<ImgUpload
  v-model="formData.typeCoverImg"
  :disabled="config.type === 'detail'"
  :proportion="1"
  file-type="kb"
  img-txt="上传图标"
  :file-size="500"
  accept="image/jpeg,image/jpg,image/png,image/gif,image/svg+xml"
>
  <template #tips>
    <div class="form-tips">
      支持上传1张1:1尺寸的图，支持常规的图片格式（jpg、JPEG、png、gif、svg），大小不超过500k
    </div>
  </template>
</ImgUpload>
```

## 可能导致加载失败的原因及解决方案

### 1. 文件上传接口问题
**问题**: 上传接口返回错误或网络问题
**解决方案**:
- 检查 `src/api/community.js` 中的 `uploadFile` 接口是否正常
- 确认接口地址是否正确：`/file/upload/file`
- 检查网络连接和服务器状态

### 2. 文件格式不支持
**问题**: 上传的文件格式不在 accept 列表中
**解决方案**:
```vue
<!-- 确保 accept 属性包含所需的文件类型 -->
<ImgUpload
  accept="image/jpeg,image/jpg,image/png,image/gif,image/svg+xml"
/>
```

### 3. 文件大小超限
**问题**: 文件大小超过限制
**解决方案**:
```vue
<!-- 调整文件大小限制 -->
<ImgUpload
  :file-size="500"
  file-type="kb"
/>
```

### 4. 图片比例不符合要求
**问题**: 图片宽高比不是1:1
**解决方案**:
- 使用1:1比例的图片
- 或者移除 `:proportion="1"` 属性

### 5. 组件引入问题
**问题**: 组件未正确引入
**解决方案**:
```javascript
// 确保正确引入组件
import ImgUpload from '@/components/ImgUpload'

export default {
  components: {
    ImgUpload
  }
}
```

## 调试方法

### 1. 检查控制台错误
打开浏览器开发者工具，查看 Console 面板是否有错误信息。

### 2. 检查网络请求
在 Network 面板中查看上传请求是否成功：
- 请求URL是否正确
- 请求状态码是否为200
- 响应数据格式是否正确

### 3. 检查组件状态
在组件中添加调试代码：
```javascript
uploadFile(file) {
  console.log('开始上传文件:', file)
  this.loading = true
  const formData = new FormData()
  formData.append('file', file)
  
  uploadFile(formData)
    .then(res => {
      console.log('上传成功:', res)
      if (res.code === 200) {
        const fullUrl = getFileUrl(res.data.url)
        console.log('完整URL:', fullUrl)
        this.$emit('input', fullUrl)
      }
    })
    .catch(error => {
      console.error('上传失败:', error)
      this.$message.error('上传失败，请重试')
    })
    .finally(() => {
      this.loading = false
    })
}
```

## 常见问题解决

### 1. 图片显示不出来
**原因**: URL路径问题
**解决**: 检查 `getFileUrl` 函数是否正确拼接了完整的访问地址

### 2. 上传按钮无响应
**原因**: 组件被禁用或事件未绑定
**解决**: 检查 `disabled` 属性和事件绑定

### 3. 文件验证失败
**原因**: 文件不符合验证条件
**解决**: 根据错误提示调整文件或组件配置

## 正确的使用示例

```vue
<template>
  <el-form-item label="图标:" prop="icon">
    <ImgUpload
      v-model="formData.icon"
      :disabled="isReadonly"
      :proportion="1"
      file-type="kb"
      img-txt="上传图标"
      :file-size="500"
      accept="image/jpeg,image/jpg,image/png,image/gif"
      @changeId="handleImageIdChange"
    >
      <template #tips>
        <div class="tips">
          支持上传1:1尺寸的图片，格式：jpg、png、gif，大小不超过500KB
        </div>
      </template>
    </ImgUpload>
  </el-form-item>
</template>

<script>
import ImgUpload from '@/components/ImgUpload'

export default {
  components: {
    ImgUpload
  },
  data() {
    return {
      formData: {
        icon: ''
      },
      isReadonly: false
    }
  },
  methods: {
    handleImageIdChange(id) {
      console.log('图片ID:', id)
      // 处理图片ID变化
    }
  }
}
</script>
```

## 注意事项

1. **文件大小单位**: `file-type="kb"` 表示以KB为单位，`file-type="MB"` 表示以MB为单位
2. **比例限制**: `proportion="1"` 表示要求1:1的正方形图片
3. **双向绑定**: 使用 `v-model` 绑定图片URL
4. **事件监听**: 可以监听 `@changeId` 事件获取上传后的文件ID
5. **自定义提示**: 使用 `<template #tips>` 插槽自定义提示信息
