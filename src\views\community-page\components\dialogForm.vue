<template>
  <div>
    <el-dialog
      :title="config.title"
      :visible.sync="dialogVisible"
      width="820px"
      top="4vh"
      :before-close="handleClose"
    >
      <el-form
        ref="ruleForm"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="demo-ruleForm"
        label-position="right"
        size="small"
      >
        <el-form-item label="内容类型：" prop="contentType">
          <el-select
            v-model="formData.contentType"
            placeholder="请选择内容类型"
            style="width:320px"
            @change="changeType"
          >
            <el-option
              v-for="k in Object.keys(contentTypeMap)"
              :key="k"
              :label="contentTypeMap[k]"
              :value="k"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          v-if="formData.contentType == 4"
          label="内容名称"
          prop="contentTitle"
        >
          <el-input
            v-model="formData.contentTitle"
            placeholder="请输入外部广告内容名称"
          />
        </el-form-item>
        <el-form-item v-else-if="formData.contentType == 3" label="内容名称：" prop="idNo">
          <el-select
            v-model="formData.idNo"
            style="width:320px"
            filterable
            clearable
            remote
            reserve-keyword
            :loading="searchLoading"
            placeholder="请输入内容名称进行搜索确认"
            :remote-method="querySearch"
            @change="handleSelect"
          >
            <el-option
              v-for="item in contentList"
              :key="item.idNo"
              :label="item.contentTitle"
              :value="item.idNo"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-else label="内容名称：" prop="contentId">
          <el-select
            ref="contentTitle"
            v-model="formData.contentId"
            style="width:320px"
            filterable
            clearable
            remote
            reserve-keyword
            :loading="searchLoading"
            placeholder="请输入内容名称进行搜索确认"
            :remote-method="querySearch"
            @change="handleSelect"
          >
            <el-option
              v-for="item in contentList"
              :key="item.contentId"
              :label="item.contentTitle"
              :value="item.contentId"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="formData.contentType != 4"
          label="内容ID："
        >
          <el-input
            v-model="formData.contentId"
            disabled
            placeholder="根据搜索选定内容自动显示"
          />
        </el-form-item>
        <el-form-item v-if="formData.contentType == 3" label="用户手机号：">
          <el-input
            v-model="formData.mobile"
            disabled
            placeholder="根据搜索选定内容自动显示"
          />
        </el-form-item>
        <template v-if="formData.contentType == 4">
          <el-form-item label="内容图片：" prop="contentImg">
            <el-input v-show="false" v-model="formData.contentImg" />
            <ImgUpload
              v-model="formData.contentImg"
              img-txt="添加大封面"
              :img-size="{
                contrast: '==',
                imgWidth: imgWidth,
                imgHeight: imgHeight
              }"
              :file-size="10"
              :tips="`图片比例大小: ${imgWidth}*${imgHeight}`"
            />
          </el-form-item>
          <el-form-item label="内容链接：" prop="contentUrl">
            <el-input
              v-model="formData.contentUrl"
              placeholder="请输入外部广告内容链接"
            />
          </el-form-item>
        </template>
        <el-form-item label="排序：" prop="sort">
          <el-input
            v-model="formData.sort"
            placeholder="请输入数字，排序1在前面，排序5在后面"
          />
        </el-form-item>
        <el-form-item label="上架开始时间：" prop="upShelfStartTime">
          <el-date-picker
            v-model="formData.upShelfStartTime"
            style="width:320px"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            placeholder="请输入上架开始时间"
          />
        </el-form-item>
        <el-form-item label="上架结束时间：" prop="upShelfEndTime">
          <el-date-picker
            v-model="formData.upShelfEndTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width:320px"
            type="datetime"
            placeholder="请输入上架结束时间"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button
          type="primary"
          :loading="commitBtnLoading"
          @click="handleSave"
        >确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ImgUpload from '@/components/ImgUpload'
import validators from '@/utils/validate'
import { actSearch, homePageAddOrEdit } from '@/api/homePage'
export default {
  name: 'DialogForm',
  components: {
    ImgUpload
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    config: {
      type: Object,
      default: () => {}
    },
    homePageType: {
      type: Number,
      default: 1
    },
    contentTypeMap: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      formData: {
        contentType: '',
        contentImg: '',
        contentId: ''
      },
      imgWidth: null,
      imgHeight: 225,
      searchLoading: false,
      commitBtnLoading: false,
      contentList: [],
      rules: {
        contentType: [validators.required('内容类型', 'change')],
        contentId: [validators.required('内容名称', 'change')],
        idNo: [validators.required('内容名称', 'change')],
        contentTitle: [validators.required('内容名称')],
        sort: [validators.required('排序')],
        upShelfStartTime: [validators.required('上架开始时间', 'change')],
        upShelfEndTime: [validators.required('上架结束时间', 'change')],
        contentImg: [validators.required('内容图片', 'change')],
        contentUrl: [validators.required('内容链接')]
      }
    }
  },
  mounted() {
    if (this.config.url === 'edit') {
      this.init()
    }
    this.imgWidth = this.homePageType === 1 ? 375 : 225
  },
  methods: {
    init() {
      this.formData = {
        ...this.config.formData
      }
      this.formData.mobile = this.formData.mobile || ''
      this.querySearch(this.config.formData, true)
    },
    querySearch(val, isEdit) {
      const _this = this
      if (val) {
        _this.searchLoading = true
        const params = isEdit ? {
          contentTitle: val.contentTitle,
          contentType: val.contentType,
          contentId: val.contentId,
          nameType: val.nameType
        } : {
          contentType: this.formData.contentType,
          contentTitle: val
        }
        actSearch(params).then(res => {
          _this.searchLoading = false
          _this.contentList = res.data
          if (isEdit) {
            _this.handleSelect(val.contentId)
            if (_this.contentList.length === 0) {
              _this.changeType()
            }
          }
        })
      }
    },
    // 内容类型变化
    changeType() {
      this.formData.contentId = ''
      this.contentList = []
    },
    handleSelect(val) {
      if (val) {
        // 多数据填写，使用循环处理
        this.contentList.forEach(item => {
          if (item.contentId === val || item.idNo === val) {
            this.formData.contentTitle = item.contentTitle
            this.formData.contentId = item.contentId
            this.formData.mobile = item.mobile || ''
            this.formData.nameType = item.nameType || ''
          }
        })
        // this.$nextTick(() => {
        //   this.formData.contentTitle = this.$refs.contentTitle.selectedLabel;
        // });
      } else {
        this.formData.contentTitle = ''
        this.formData.mobile = ''
      }
    },
    handleSave() {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          this.commitBtnLoading = true
          const params = {
            ...this.formData,
            type: this.homePageType
          }
          delete params['mobile']
          homePageAddOrEdit(params, this.config.url)
            .then(res => {
              this.$message.success(res.msg)
              this.$emit('saveSuccess')
            })
            .finally(() => {
              this.commitBtnLoading = false
            })
        } else {
          return false
        }
      })
    },
    handleCancel() {
      this.$emit('cancel')
    },
    handleClose() {
      this.$emit('handleClose')
      this.$refs['ruleForm'].resetFields()
    }
  }
}
</script>

<style></style>
