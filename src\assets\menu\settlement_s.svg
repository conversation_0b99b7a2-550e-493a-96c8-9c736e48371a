<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="2" y="3" width="16" height="13" rx="2" fill="url(#paint0_linear_57_1138)"/>
<path d="M7.5 9.5C7.5 7.29086 9.29086 5.5 11.5 5.5H17.5V13.5H11.5C9.29086 13.5 7.5 11.7091 7.5 9.5Z" fill="#2474FF" stroke="white"/>
<g filter="url(#filter0_d_57_1138)">
<circle cx="11.5" cy="9.5" r="1.5" fill="url(#paint1_linear_57_1138)"/>
</g>
<defs>
<filter id="filter0_d_57_1138" x="10" y="7.5" width="3.5" height="3.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.5" dy="-0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_57_1138"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_57_1138" result="shape"/>
</filter>
<linearGradient id="paint0_linear_57_1138" x1="12.2003" y1="4.75527" x2="12.2003" y2="16" gradientUnits="userSpaceOnUse">
<stop stop-color="#9ECBFF"/>
<stop offset="1" stop-color="#4D94FF"/>
</linearGradient>
<linearGradient id="paint1_linear_57_1138" x1="11.5" y1="8" x2="11.5" y2="11" gradientUnits="userSpaceOnUse">
<stop stop-color="#9ECBFF"/>
<stop offset="1" stop-color="#DCECFF"/>
</linearGradient>
</defs>
</svg>
