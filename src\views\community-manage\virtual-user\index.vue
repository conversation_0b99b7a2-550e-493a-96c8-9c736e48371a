<template>
  <div>
    <RouteTitle />
    <div class="main-body">
      <div class="main-body-bottom">
        <div class="body-top-form">
          <el-form
            ref="searchForm"
            class="left"
            :model="mainObj.searchForm"
            label-position="left"
            inline
          >
            <el-form-item label="状态：">
              <el-select
                v-model="mainObj.searchForm.status"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in statusEnumObj"
                  :key="item.code"
                  :label="item.text"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="马甲昵称：">
              <el-input
                v-model="mainObj.searchForm.nickName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
            <el-form-item label="关联手机号：">
              <el-input
                v-model="mainObj.searchForm.relMobile"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
            <el-form-item label="标识名称：">
              <el-select v-model="mainObj.searchForm.markId" placeholder="请选择" clearable>
                <el-option v-for="item in marks" :key="item.markId" :label="item.markName" :value="item.markId" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">查询</el-button>
              <el-button @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
          <el-button
            class="right"
            type="primary"
            @click="operation('add')"
          >新增</el-button>
        </div>
        <div class="table">
          <el-table
            ref="tableRef"
            :data="mainObj.list"
            fit
            border
            highlight-current-row
            size="small"
            :header-cell-style="{ background: '#F9F9F9' }"
          >
            <el-table-column prop="ipAddress" label="序号" width="50">
              <template slot-scope="scope">
                <span>{{
                  mainObj.pageSize *
                    (mainObj.currentPage === 1 ? 0 : mainObj.currentPage) +
                    scope.$index +
                    1
                }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="id" label="马甲ID" show-overflow-tooltip />
            <el-table-column prop="statusName" label="状态" />
            <el-table-column prop="markName" label="标识" width="150" />
            <el-table-column prop="nickName" label="马甲昵称" width="150" />
            <el-table-column prop="relMobile" label="关联手机号" width="150" />
            <el-table-column prop="updateName" label="更新人" />
            <el-table-column prop="updateTime" label="更新时间" />
            <el-table-column label="操作" fixed="right" width="180">
              <template slot-scope="scope">
                <el-button v-if="scope.row.status === '1'" type="text" @click="operation('enable', scope.row)">启用</el-button>
                <el-button v-if="scope.row.status === '0'" type="text" style="color:#e6a23c;" @click="operation('disable', scope.row)">禁用</el-button>
                <el-button v-if="scope.row.status === '0' || scope.row.status === '1' " type="text" @click="operation('edit', scope.row)">编辑</el-button>
                <el-button v-if="scope.row.status === '1' " type="text" @click="operation('delete', scope.row)">删除</el-button>
                <el-button type="text" @click="HandArticleEditing(scope.row)">发长文</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination
            :current-page="mainObj.currentPage"
            :page-size="mainObj.pageSize"
            :page-sizes="mainObj.pageSizes"
            :total="mainObj.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <detail ref="detail" :type="type" :row="row" @fetchdata="fetchData" />

    <!-- 长文编辑 -->
    <articleEditing ref="articleEditing" />
    <!--<el-dialog
      title="拉黑用户"
      :visible.sync="dialogVisible"
      width="500px"
      top="4vh"
      :before-close="handleClose"
    >
      <div>
        <div class="header">是否确定拉黑该用户?</div>
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          label-width="90px"
          class="demo-ruleForm"
          label-position="top"
          size="small"
        >
          <el-form-item label="拉黑理由" prop="reason">
            <el-input
              v-model="ruleForm.reason"
              type="textarea"
              resize="none"
              rows="6"
              maxlength="100"
              show-word-limit
              placeholder="请输入"
            />
          </el-form-item>
        </el-form>
      </div>-->
    <!-- <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button
        type="primary"
        @click="submitForm"
      >确定</el-button>
    </span>
    </el-dialog> -->
  </div>
</template>

<script>
import sysManageApi from '@/api/sysManageApi'
import communityUser from '@/api/communityUser'
import checkPermission from '@/utils/permission'
import RouteTitle from '@/components/RouteTitle'
import detail from './detail'
import articleEditing from './articleEditing'
const defaultSearchForm = {
  status: '',
  nickName: '',
  relMobile: '',
  markId: ''
}

const defaultUserForm = {
  nickName: '',
  relMobile: '',
  avatar: '',
  status: { code: '', text: '', name: '' }

}
export default {
  components: {
    RouteTitle,
    detail,
    articleEditing
  },
  data() {
    return {
      statusEnumObj: [],
      ruleForm: {
        reason: ''
      },
      rules: {
        reason: [{ required: true, message: '不能为空', trigger: 'change' }]
      },
      dialogVisible: false,
      row: {},
      type: '',
      // token: getToken(),
      detail: checkPermission(['virtualUser']),

      infoId: '',
      dialogTableVisible: false,
      mainObj: {
        list: [],
        pageSize: 10,
        pageSizes: [10, 30, 50],
        currentPage: 1,
        total: 0,
        searchForm: { ...defaultSearchForm }
      },
      userDialogObj: {
        visible: false,
        title: '',
        type: '',
        form: Object.assign({}, defaultUserForm)
      },
      value: 1,
      marks: []
    }
  },
  computed: {

  },
  created() {
    this.getUserStatusEnum()
    this.onSearch()
    communityUser.getUserMarks().then(res => {
      this.marks = res.data
    })
  },
  methods: {
    checkPermission,
    getUserStatusEnum() {
      sysManageApi.getTypeEnum({ enumName: 'VirtualUserStatusEnum' }).then(res => {
        this.statusEnumObj = res.data
      })
    },
    onSearch() {
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    onReset() {
      this.mainObj.searchForm = { ...defaultSearchForm }
      this.fetchData()
    },

    handleSizeChange(val) {
      this.mainObj.pageSize = val
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.mainObj.currentPage = val
      this.fetchData()
    },
    getDetailInfo(row) {
      this.infoId = row.id.toString()
      this.dialogTableVisible = true
      if (this.$refs.child) {
        this.$refs.child.updatedInfo(row.id)
      }
    },
    fetchData() {
      const params = {
        status: this.mainObj.searchForm.status,
        relMobile: this.mainObj.searchForm.relMobile,
        nickName: this.mainObj.searchForm.nickName,
        markId: this.mainObj.searchForm.markId,
        pageNum: this.mainObj.currentPage,
        pageSize: this.mainObj.pageSize
      }
      communityUser.searchVirtualUserList(params).then(res => {
        this.mainObj.list = res.data.list
        this.mainObj.total = Number(res.data.total)
      })
    },
    submitForm() {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          communityUser.banUser({ id: this.row.id, reason: this.ruleForm.reason }).then(res => {
            this.handleClose()
            this.$message({
              type: 'success',
              message: '操作成功！'
            })
            this.onSearch()
          })
        }
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
    operation(type, row = {}) {
      this.type = type
      this.row = row

      if (type === 'edit') {
        this.$nextTick(() => {
          this.$refs.detail.dialogVisible = true
          this.$refs.detail.getUserList(row)
          // this.$refs.detail.onSearch()
          // this.$refs.detail.activeName = 'first'
        })
      }

      if (type === 'enable') {
        this.$confirm(`启用后，用户将可重新使用该马甲，是否启用？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            communityUser.enableDisableVirtualUser({ id: this.row.id }).then(res => {
              this.onSearch()
              this.$message({
                type: 'success',
                message: '操作成功！'
              })
            })
          })
          .catch(() => {})
      }

      if (type === 'disable') {
        this.$confirm(`禁用后，用户将无法切换及使用该马甲，是否禁用？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            communityUser.enableDisableVirtualUser({ id: this.row.id }).then(res => {
              this.onSearch()
              this.$message({
                type: 'success',
                message: '操作成功！'
              })
            })
          })
          .catch(() => {})
      }

      if (type === 'delete') {
        this.$confirm(`删除后，用户将无法切换及使用该马甲且不可重新启用，是否删除？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            communityUser.deleteVirtualUser({ id: this.row.id }).then(res => {
              this.onSearch()
              this.$message({
                type: 'success',
                message: '操作成功！'
              })
            })
          })
          .catch(() => {})
      }
      if (type === 'add') {
        this.$nextTick(() => {
          this.row = {}
          this.$refs.detail.dialogVisible = true
          this.$refs.detail.getUserList()
        })
      }
    },
    HandArticleEditing(row) {
      this.$refs.articleEditing.showDialo(row)
    }
  }
}
</script>

<style lang="scss" scoped>
.main-body-bottom {
  box-sizing: border-box;
  padding: 20px;
}

.right {
  float: right;
  margin-bottom: 10px;
}

.left {
  float: left;
}

.body-top-form {
  overflow: hidden;
}

.header{
  height:40px;
  line-height:40px;
  width:100%;
  box-sizing: border-box;
  padding:0 0px;
}

::v-deep .el-dialog__body{
  padding:0 20px;
}

::v-deep .el-dialog__footer{
  text-align:right;
}
</style>
