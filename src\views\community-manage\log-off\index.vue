<template>
  <div>
    <RouteTitle />
    <!-- 条件查询 -->
    <div class="main-body">
      <div class="main-body-top">
        <!-- 页面条件搜索 -->
        <dynamic-query
          :data="queryConfig"
          @queryChanged="handleQueryDataChanged"
          @search="handleSearch"
        />
        <!-- 页面列表数据 + 分页条 -->
        <dynamic-table
          :config="tableConfig"
          :data="tableData"
          :is-loading.sync="tableLoading"
          @pagination="handlePageChange"
        >
          <!-- S 处理表格自定义插槽 -->
          <template #cancelTypeText="{ rowData, index}">
            <span>{{ cancelTypeMap[rowData.cancelType] }}</span>
          </template>

          <template #cancelText="{ rowData, index }">
            <span
              :style="{ color: rowData.cancelStatus == 3 ? '#C03639' : '' }"
            >{{ cancelStatusMap[rowData.cancelStatus] }}
            </span>
          </template>
          <template #typeText="{ rowData, index }">
            <span>{{ checkTypeMap[rowData.checkType] }}
            </span>
          </template>
          <template v-slot:operate="{ rowData, index }">
            <el-button
              type="text"
              @click.stop="handleView(rowData)"
            >查看原因</el-button>
            <el-button
              v-if="rowData.checkType == '1' && rowData.cancelStatus == 2"
              type="text"
              class="green-btn"
              @click.stop="handleReview(rowData.cancelId, 1)"
            >审核通过</el-button>
            <el-button
              v-if="rowData.checkType == '1' && rowData.cancelStatus == 2"
              type="text"
              class="red-btn"
              @click.stop="handleReview(rowData.cancelId, 0)"
            >审核不通过</el-button>
          </template>
        </dynamic-table>
      </div>
    </div>
    <!-- 查看原因 -->
    <el-dialog
      :visible.sync="dialogVisible"
      top="4vh"
      width="820px"
      title="查看原因"
    >
      <div class="dialog-box">
        <div class="header">申请注销原因</div>
        <div>{{ dialogData.cancelReason }}</div>
      </div>
      <div class="dialog-box">
        <div class="header">审核不通过原因</div>
        <div>{{ dialogData.rejectReason }}</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import RouteTitle from '@/components/RouteTitle'
import DynamicQuery from '@/components/dynamic/Query.vue'
import DynamicTable from '@/components/dynamic/Table.vue'
import { doCancel } from '@/api/communityUser'
const cancelStatusMap = {
  1: '正常',
  2: '审核中',
  3: '注销失败',
  4: '已注销'
}
const checkTypeMap = {
  '0': '自动审核',
  '1': '人工审核'
}
const cancelTypeMap = {
  1: '普通注销',
  2: '切换为家长身份',
  3: '切换为单身身份'
}

import mixin from '../../mixin'
export default {
  components: {
    RouteTitle,
    DynamicQuery,
    DynamicTable
  },
  mixins: [mixin],
  data() {
    return {
      queryUrl: '/communityUser/cancel/getCancelList',
      dialogVisible: false,
      dialogData: {},
      queryConfig: {
        queryItem: [
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '用户ID:',
            model: 'userId',
            clearable: true, // 默认 true
            size: 'small' // 默认 small
          },
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '手机号:',
            model: 'phoneNo',
            clearable: true, // 默认 true
            size: 'small' // 默认 small
          },
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '用户姓名:',
            model: 'name',
            clearable: true, // 默认 true
            size: 'small' // 默认 small
          },
		  {
		    type: 'select',
		    label: '注销审核类型:',
		    model: 'checkType',
		    optionValue: 'id',
		    optionLabel: 'value',
		    optionList: checkTypeMap,
            defaultValue: '1'
		  },
          {
            type: 'select',
            label: '注销审核状态:',
            model: 'cancelStatus',
            optionValue: 'id',
            optionLabel: 'value',
            optionList: cancelStatusMap,
            defaultValue: '2'
          },
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '申请编号:',
            model: 'cancelId',
            clearable: true, // 默认 true
            size: 'small' // 默认 small
          },
          {
            type: 'date', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '注销申请时间:',
            model: 'logOffDateArr',
            config: {
              type: 'datetimerange', // 时间区间 - 返回数组 需业务重写字段
              separator: '-',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
              format: 'yyyy-MM-dd HH:mm:ss'
            }
          },
          {
            type: 'option'
          },
          {
            type: 'option',
            slotName: 'addBtn'
          }
        ]
      },
      // 列表字段
      tableConfig: {
        tableColumn: [
          {
            prop: '',
            label: '序号',
            isIndex: true
          },
          {
            prop: 'cancelId',
            label: '申请编号'
          },
          {
            prop: 'cancelType',
            label: '申请类型',
            slotName: 'cancelTypeText'
          },
          {
            prop: 'userId',
            label: '用户ID'
          },
          {
            prop: 'phoneNo',
            label: '手机号'
          },
          {
            prop: 'name',
            label: '用户姓名'
          },
          {
            prop: 'nickname',
            label: '用户昵称'
          },
          {
            prop: 'cancelTime',
            label: '注销申请时间'
          },
		  {
		    prop: 'checkType',
		    label: '注销审核类型',
		    slotName: 'typeText'
		  },
          {
            prop: 'cancelStatus',
            label: '注销审核状态',
            slotName: 'cancelText'
          },
          {
            prop: 'checkTime',
            label: '审核时间'
          },
          {
            prop: 'checkUser',
            label: '操作者'
          },
          {
            prop: '',
            label: '操作',
            slotName: 'operate', // 操作 - 自定义操作，和operateList二选一
            fixed: 'right',
            minWidth: 160
          }
        ]
      },

      // 列表数据
      tableData: {
        list: [],
        // 分页数据
        pageTotal: 0, // 列表总数
        pageSize: 10, // 每页条数
        pageNum: 1 // 当前页码
      },
      cancelStatusMap: cancelStatusMap,
      checkTypeMap: checkTypeMap,
      cancelTypeMap: cancelTypeMap
    }
  },
  methods: {
    handleView(rowData) {
      this.dialogVisible = true
      this.dialogData = rowData
    },
    // 提交审核
    handleReview(cancelId, operation) {
      const _this = this
      if (operation === 1) {
        const msg = '确定审核通过吗？'
        const btn = '审核通过'
        this.$confirm(msg, '提示', {
          confirmButtonText: btn,
          cancelButtonText: '取消',
          center: true
        })
          .then(() => {
            _this.submitDoCancel({
              cancelId: cancelId,
              operateType: operation
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消操作'
            })
          })
      } else if (operation === 0) {
        this.$prompt('', '提示', {
          confirmButtonText: '审核不通过',
          cancelButtonText: '取消',
          inputPlaceholder: '请输入审核不通过的原因...',
          inputType: 'textarea',
          inputPattern: /^.+$/,
          inputErrorMessage: '审核不通过原因必填',
          center: true
        }).then(({ value }) => {
          _this.submitDoCancel({
            cancelId: cancelId,
            operateType: operation,
            rejectReason: value
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          })
        })
      }
    },
    submitDoCancel(obj) {
      const _this = this
      doCancel(obj).then(res => {
        this.$message({
          type: 'success',
          message: res.msg
        })
        _this.getList()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.header-tab {
  display: inline-block;
  margin-left: 20px;
  font-weight: normal;
  font-size: 16px;
  .active {
    color: #1890ff;
    padding-bottom: 5px;
    border-bottom: 3px solid #1890ff;
  }
  .tab-line {
    margin: 0 10px;
  }
}
.dialog-box {
  min-height: 89px;
  border: 1px solid #ddd;
  margin-bottom: 30px;
  .header {
    background: #f5f5f5;
    border-bottom: 1px solid #ddd;
  }
  > div {
    min-height: 43px;
    line-height: 43px;
    padding-left: 20px;
  }
}
</style>
