<template>
  <div>
    <RouteTitle />
    <div class="app-container">
    <!-- 查询表单 -->
    <div class="filter-container">
      <el-form :inline="true" :model="listQuery" class="demo-form-inline">
        <el-form-item label="ID">
          <el-input
            v-model="listQuery.id"
            placeholder="请输入运营位编码"
            style="width: 200px;"
            clearable
          />
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="listQuery.contentType" placeholder="请选择类型" clearable style="width: 120px">
            <el-option label="全部" value="" />
            <el-option label="活动" :value="1" />
            <el-option label="广告" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="展位">
          <el-select v-model="listQuery.buoyLocation" placeholder="请选择展位" clearable style="width: 120px">
            <el-option label="全部" value="" />
            <el-option label="精选活动" value="0" />
            <el-option label="头图" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="名称">
          <el-input
            v-model="listQuery.contentTitle"
            placeholder="请输入Banner名称"
            style="width: 200px;"
            clearable
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="listQuery.status" placeholder="请选择状态" clearable style="width: 120px">
            <el-option label="全部" value="" />
            <el-option label="生效" :value="1" />
            <el-option label="失效" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="上架状态">
          <el-select v-model="listQuery.upShelfStatus" placeholder="请选择上架状态" clearable style="width: 120px">
            <el-option label="全部" value="" />
            <el-option label="已上架" :value="1" />
            <el-option label="已下架" :value="2" />
            <el-option label="草稿" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 功能按钮 -->
    <div class="filter-container" style="margin-bottom: 10px;">
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >
        新增
      </el-button>
    </div>

    <!-- 数据表格 -->
    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="序号" type="index" width="60" align="center" />
      <el-table-column label="类型" width="80" align="center">
        <template slot-scope="{row}">
          <span>{{ row.contentType }}</span>
        </template>
      </el-table-column>
      <el-table-column label="ID" prop="id" width="100" align="center" />
      <el-table-column label="名称" prop="contentTitle" min-width="150" />
      <el-table-column label="展位" width="100" align="center">
        <template slot-scope="{row}">
          <span>{{ row.buoyLocation === '0' ? '精选活动' : '头图' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" prop="sort" width="80" align="center" />
      <el-table-column label="上架状态" width="100" align="center">
        <template slot-scope="{row}">
          <el-tag :type="getUpShelfStatusType(row.upShelfStatus)">
            {{ getUpShelfStatusText(row.upShelfStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="上架开始时间" width="160" align="center">
        <template slot-scope="{row}">
          <span>{{ row.upShelfStartTime | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上架结束时间" width="160" align="center">
        <template slot-scope="{row}">
          <span>{{ row.upShelfEndTime | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="80" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.status === '生效' ? 'success' : 'danger'">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" width="160" align="center">
        <template slot-scope="{row}">
          <span>{{ row.updateTime | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作者" prop="updateId" width="100" align="center" />
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            修改
          </el-button>
          <el-button
            v-if="row.status === '生效'"
            size="mini"
            type="danger"
            @click="handleModifyStatus(row, 0)"
          >
            停用
          </el-button>
          <el-button
            v-else
            size="mini"
            type="success"
            @click="handleModifyStatus(row, 1)"
          >
            启用
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 新增/编辑弹窗 -->
    <banner-dialog
      :visible.sync="dialogVisible"
      :form-data="temp"
      :dialog-status="dialogStatus"
      @success="handleDialogSuccess"
    />
    </div>
  </div>
</template>

<script>
import { getHomePageList, modifyStatus } from '@/api/homePage'
import Pagination from '@/components/Pagination'
import BannerDialog from './components/BannerDialog'
import RouteTitle from '@/components/RouteTitle'
import { parseTime } from '@/utils'

export default {
  name: 'BannerManage',
  components: {
    Pagination,
    BannerDialog,
    RouteTitle
  },
  filters: {
    parseTime
  },
  data() {
    return {
      tableKey: 0,
      list: [], // 确保初始化为空数组
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        id: '',
        contentType: '',
        buoyLocation: '',
        contentTitle: '',
        status: '',
        upShelfStatus: '',
        delFlag: '0'
      },
      dialogVisible: false,
      dialogStatus: '',
      temp: {}
    }
  },
  created() {
    console.log('Banner管理页面初始化')
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      const params = { ...this.listQuery }
      // 清空空值参数
      Object.keys(params).forEach(key => {
        if (params[key] === '') {
          delete params[key]
        }
      })

      getHomePageList(params).then(response => {
        console.log('API响应数据:', response)
        // 处理不同的响应数据结构
        if (response.data) {
          if (Array.isArray(response.data)) {
            // 如果data直接是数组
            this.list = response.data
            this.total = response.data.length
          } else if (response.data.records && Array.isArray(response.data.records)) {
            // 如果data是对象，包含records数组
            this.list = response.data.records
            this.total = response.data.total || response.data.records.length
          } else if (response.data.list && Array.isArray(response.data.list)) {
            // 如果data是对象，包含list数组
            this.list = response.data.list
            this.total = response.data.total || response.data.list.length
          } else {
            // 其他情况，初始化为空数组
            this.list = []
            this.total = 0
          }
        } else {
          // 如果没有data字段，初始化为空数组
          this.list = []
          this.total = 0
        }
        this.listLoading = false
      }).catch(error => {
        console.error('获取数据失败:', error)
        this.$message.warning('API接口暂不可用')
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    resetQuery() {
      this.listQuery = {
        page: 1,
        limit: 20,
        id: '',
        contentType: '',
        buoyLocation: '',
        contentTitle: '',
        status: '',
        upShelfStatus: '',
        delFlag: '0'
      }
      this.getList()
    },
    handleCreate() {
      this.temp = {}
      this.dialogStatus = 'create'
      this.dialogVisible = true
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogVisible = true
    },
    handleModifyStatus(row, status) {
      const statusText = status === 1 ? '启用' : '停用'
      const message = `确定对【${row.id}】进行${statusText}操作吗？${statusText === '停用' ? '停用后将不再根据配置自动上下架' : '启用后将根据配置自动上下架'}`

      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        modifyStatus({ id: row.id }).then(() => {
          this.$message({
            type: 'success',
            message: `${statusText}成功!`
          })
          this.getList()
        })
      })
    },
    handleDialogSuccess() {
      this.dialogVisible = false
      this.getList()
    },
    getUpShelfStatusType(status) {
      const statusMap = {
        0: 'info', // 草稿
        已上架: 'success', // 已上架
        已下架: 'danger' // 已下架
      }
      return statusMap[status] || 'info'
    },
    getUpShelfStatusText(status) {
      const statusMap = {
        0: '草稿',
        已上架: '已上架',
        已下架: '已下架'
      }
      return statusMap[status] || '未知'
    },
    getTestData() {
      // 返回测试数据
      return [
        {
          id: 1,
          contentType: 1,
          contentTitle: '春节活动Banner',
          buoyLocation: '1',
          sort: 1,
          upShelfStatus: 1,
          upShelfStartTime: '2024-01-01 00:00:00',
          upShelfEndTime: '2024-12-31 23:59:59',
          status: 1,
          updateTime: '2024-01-15 10:30:00',
          updateId: 'admin',
          createTime: '2024-01-01 09:00:00'
        },
        {
          id: 2,
          contentType: 2,
          contentTitle: '新年广告Banner',
          buoyLocation: '0',
          sort: 2,
          upShelfStatus: 2,
          upShelfStartTime: '2024-01-01 00:00:00',
          upShelfEndTime: '2024-01-31 23:59:59',
          status: 0,
          updateTime: '2024-01-10 14:20:00',
          updateId: 'admin',
          createTime: '2024-01-01 08:00:00'
        }
      ]
    }
  }
}
</script>

<style scoped>
.filter-container {
  padding-bottom: 10px;
}
.filter-container .filter-item {
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 10px;
}
</style>
