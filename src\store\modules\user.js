import { login, logout, getInfo, getMenu } from '@/api/userApi'
import { getToken, setToken, removeToken } from '@/utils/auth'
import router, { resetRouter } from '@/router'
import { generateRoles } from '@/utils'

const state = {
  token: getToken(),
  name: '',
  expireFlag: '',
  introduction: '',
  roles: [],
  userInfo: {},
  menuList: [] // 添加菜单列表状态
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_EXPIREFLAG: (state, expireFlag) => {
    state.expireFlag = expireFlag
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_USERINFO: (state, userInfo) => {
    state.userInfo = userInfo
  },
  SET_MENU_LIST: (state, menuList) => {
    state.menuList = menuList // 添加设置菜单列表的mutation
  }
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    return new Promise((resolve, reject) => {
      login(userInfo).then(response => {
        const { token } = response
        commit('SET_TOKEN', token)
        commit('SET_EXPIREFLAG', response.data.pwdExpireFlag)
        setToken(token)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // get user info
  getInfo({ commit, dispatch }) {
    return new Promise((resolve, reject) => {
      getInfo().then(response => {
        const { data } = response

        if (!data) {
          reject('验证失败，请重新登录')
        }

        const { loginName } = data

        getMenu(data).then((res) => {
          const roles = generateRoles(res.data)
          data.roles = roles
          commit('SET_ROLES', roles)
          commit('SET_NAME', loginName)
          commit('SET_USERINFO', data)
          commit('SET_MENU_LIST', res.data) // 保存菜单数据

          // 生成路由时传递菜单数据
          dispatch('permission/generateRoutes', { roles, menuList: res.data }, { root: true })

          resolve(data)
        })
      }).catch(error => {
        reject(error)
      })
    })
  },

  // user logout
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      logout(state.token).then(() => {
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        commit('SET_NAME', '')
        commit('SET_USERINFO', {})
        commit('SET_MENU_LIST', []) // 清空菜单数据
        removeToken()
        resetRouter()

        router.push(`/login?redirect=${router.currentRoute.fullPath}`)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  updateFlag({ commit }) {
    return new Promise(resolve => {
      commit('SET_EXPIREFLAG', false)
      resolve()
    })
  },
  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      commit('SET_MENU_LIST', []) // 清空菜单数据
      removeToken()
      resolve()
    })
  },

  // dynamically modify permissions
  changeRoles({ commit, dispatch, state }, role) {
    return new Promise(async resolve => {
      const token = role + '-token'

      commit('SET_TOKEN', token)
      setToken(token)

      const { roles } = await dispatch('getInfo')

      resetRouter()

      // generate accessible routes map based on roles and menuList
      const accessRoutes = await dispatch('permission/generateRoutes', {
        roles,
        menuList: state.menuList // 使用保存的菜单数据
      }, { root: true })

      // dynamically add accessible routes
      router.addRoutes(accessRoutes)

      // reset visited views and cached views
      dispatch('tagsView/delAllViews', null, { root: true })

      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
