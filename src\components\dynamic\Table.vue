<!--
	<AUTHOR>
	@date 2022-09-18
	@desc 动态表格列表 - 数据反显 & 分页集成
	写法↓
	<dynamic-table :data="数据"></dynamic-table>
-->
<template>
  <div class="dynamic-table-mod">
    <!-- 列表数据展示 -->
    <el-table
      v-loading="isLoading"
      refs="dynamicTable"
      :data="data.list"
      :element-loading-text="config.loadingText ? config.loadingText : '加载中...'"
      border
      fit
      highlight-current-row
      @current-change="handleCurrentChange"
      @selection-change="handleSelectionChange"
    >
      <!-- 第一列复选框 -->
      <el-table-column
        v-if="config.selectionObj && config.selectionObj.show"
        type="selection"
        align="center"
        width="50"
      />

      <!-- 自定义列 -->
      <template v-for="(column, index) in config.tableColumn">
        <!-- 序号 - 行索引值 -->
        <el-table-column
          v-if="column.isIndex"
          type="index"
          align="center"
          :prop="column.prop"
          :label="column.label"
          :width="column.width ? column.width : null"
        />
        <!-- 其他 -->
        <el-table-column
          v-else
          align="center"
          :prop="column.prop"
          :label="column.label"
          :width="column.width ? column.width : null"
          :min-width="column.minWidth ? column.minWidth : null"
          :sort-by="column.sortBy ? column.sortBy : null"
          :sortable="column.sortBy ? true : null"
          :class-name="column.className ? column.className : null"
          :fixed="column.fixed ? column.fixed : null"
        >
          <!-- 重写表头 - 插槽名: 自定义名称+Header -->
          <template slot="header">
            <slot v-if="column.slotName" :name="column.slotName + 'Header'" />
          </template>

          <!-- 字段展示 -->
          <template
            v-if="!column.childrenList || column.childrenList.length <= 0"
            slot-scope="{ row, $index }"
          >
            <!-- 自定义内容插槽 - 插槽传参： data当前值 index行索引 rowData当前行对象 -->
            <slot
              v-if="column.slotName"
              :name="column.slotName"
              :data="column.prop ? row[column.prop] : null"
              :index="$index"
              :rowData="row"
            />

            <!-- 默认字段展示 -->
            <template v-else>
              <!-- 显示tooltip - 字数过长等情况 -->
              <el-tooltip
                v-if="column.showTooltip"
                effect="dark"
                :content="row[column.prop]"
                placement="top"
              >
                <span>{{ row[column.prop] }}</span>
              </el-tooltip>

              <!-- 默认字段展示 -->
              <div v-else>{{ row[column.prop] }}</div>
            </template>
          </template>

          <!-- 存在合并列 - 仅支持二级标题 - TODO 优化 column独立组件 递归嵌套 -->
          <el-table-column
            v-for="(item, index) in column.childrenList"
            v-if="column.childrenList && column.childrenList.length > 0"
            :key="index"
            align="center"
            :prop="item.prop"
            :label="item.label"
            :width="item.width ? item.width : null"
            :min-width="column.minWidth ? item.minWidth : null"
            :sort-by="item.sortBy ? item.sortBy : null"
            :sortable="item.sortBy ? true : null"
            :class-name="item.className ? item.className : null"
            :fixed="item.fixed ? item.fixed : null"
          >
            <!-- 重写表头 - 插槽名: 自定义名称+Header -->
            <template slot="header">
              <slot v-if="item.slotName" :name="item.slotName + 'Header'" />
            </template>

            <!-- 字段展示 -->
            <template slot-scope="{ row, $index }">
              <!-- 自定义内容插槽 - 插槽传参： data当前值 index行索引 rowData当前行对象 -->
              <slot
                v-if="item.slotName"
                :name="item.slotName"
                :data="item.prop ? row[item.prop] : null"
                :index="$index"
                :rowData="row"
              />

              <!-- 默认字段展示 -->
              <template v-else>
                <!-- 显示tooltip - 字数过长等情况 -->
                <el-tooltip
                  v-if="item.showTooltip"
                  effect="dark"
                  :content="row[item.prop]"
                  placement="top"
                >
                  <span>{{ row[item.prop] }}</span>
                </el-tooltip>

                <!-- 默认字段展示 -->
                <div v-else>{{ row[item.prop] }}</div>
              </template>
            </template>
          </el-table-column>
        </el-table-column>
      </template>
    </el-table>

    <!-- 分页条 -->
    <pagination
      :total="data.pageTotal"
      :page.sync="data.pageNum"
      :limit.sync="data.pageSize"
      @pagination="handlePageChange"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
export default {
  name: 'DynamicTable',
  components: {
    Pagination
  },
  props: {
    // 列表字段数据
    config: {
      type: Object,
      default: () => {}
    },
    // 列表数据
    data: {
      type: Object,
      default: () => {}
    },
    // 数据获取中
    isLoading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  mounted() {
  },
  methods: {
    /**
		 * 当表格的当前行发生变化的时候会触发该事件
		 * @param {Object} currentRow
		 */
    handleCurrentChange: function(currentRow) {
      this.$emit('table-current-change', currentRow)
    },

    /**
		 * 当选择项发生变化时会触发该事件
		 * @param {Object} selection
		 */
    handleSelectionChange: function(selection) {
      this.$emit('table-selection-change', selection)
    },

    /**
		 * 分页条页码/每页条数改变
		 * @param {Object} obj { page: 当前页码, limit: 每页条数 }
		 */
    handlePageChange: function(obj) {
      this.$emit('pagination', obj)
    }
  }
}
</script>
<style lang="scss" scoped>
.dynamic-table-mod {
	.el-table {
		// min-height: 320px;
	}
}

</style>
