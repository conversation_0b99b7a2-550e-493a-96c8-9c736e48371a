<template>
  <div>
    <el-dialog
      :title="type === 'goods' ? '选择商品' : '选择应用'"
      :visible.sync="dialogVisible"
      width="80%"
      top="4vh"
      :before-close="() => dialogVisible = false"
    >
      <div>
        <div class="table-title">{{ type === 'goods' ? '可选商品' : '可选应用' }}</div>
        <div style="padding:10px">
          <div class="flex-between" style="padding:10px">
            <div style="flex:1;display: flex;align-items: center;">
              <span class="title">{{ type === 'goods' ? '商品名称' : '应用名称' }}</span>
              <el-input v-model="queryFrom.key" style="width: 25%;" size="small" clearable />
            </div>

            <div>
              <el-button type="primary" size="small" @click="search">查询</el-button>
              <el-button @click="reset">重置</el-button>
            </div>

          </div>
          <el-table
            v-loading="listLoading"
            size="mini"
            :data="list"
            fit
            border
            highlight-current-row
            :header-cell-style="{ background: '#F9F9F9' }"
          >
            <el-table-column v-for="item in head" :key="item.key" :prop="item.value" :label="item.key" />
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button
                  :disabled="type === 'goods' ? (currentChang.length >= 5) : (currentChang.length >= 4)"
                  type="text"
                  @click="handChang(scope.row)"
                >选择</el-button>
              </template>
            </el-table-column>
          </el-table>
          <Pagination
            :total="Number(total)"
            :page.sync="queryFrom.pageNum"
            :limit.sync="queryFrom.pageSize"
            @pagination="getList"
          />
        </div>

        <div>
          <div class="table-title">{{ type === 'goods' ? '已选商品' : '已选应用' }}</div>
          <div style="padding:10px">
            <el-table
              size="mini"
              :data="currentChang"
              fit
              border
              highlight-current-row
              :header-cell-style="{ background: '#F9F9F9' }"
            >
              <el-table-column v-for="item in head" :key="item.key" :prop="item.value" :label="item.key" />
              <el-table-column label="操作">
                <template slot-scope="scope">
                  <el-button type="text" @click="handRemove(scope.$index)">移除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="() => dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmChang">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import community from '@/api/community'
import { getApply } from '@/api/communityUser'
const goodshead = [{ key: '商品名称', value: 'goodsName' }, { key: '商品价格（元）', value: 'goodsCostPrice' }]
const apphead = [{ key: '应用名称', value: 'showName' }, { key: '应用id', value: 'applyId' }]
export default {
  components: {
    Pagination
  },
  data() {
    return {
      dialogVisible: false,
      type: '',
      list: [],
      queryFrom: {
        key: '',
        pageNum: 1,
        pageSize: 10
      },
      listLoading: false,
      total: 0,
      currentChang: [],
      head: []

    }
  },

  mounted() {

  },
  methods: {
    search() {
      this.queryFrom.pageNum = 1
      this.queryFrom.pageSize = 10
      this.getList()
    },
    showDialo(type, data) {
      this.type = type
      if (type === 'goods') {
        this.head = goodshead
      } else {
        this.head = apphead
      }
      this.list = []
      this.total = 0
      this.reset()
      this.currentChang = []
      this.dialogVisible = true
      if (data && data.length > 0) {
        this.currentChang = this.currentChang.concat(data)
      }
    },
    getList() {
      this.listLoading = true
      if (this.type === 'goods') {
        const params = {
          goodsName: this.queryFrom.key,
          pageNum: this.queryFrom.pageNum,
          pageSize: this.queryFrom.pageSize
        }
        community.getGoodsList(params).then(res => {
          this.list = res.data.list
          this.total = Number(res.data.total)
          this.listLoading = false
        }).catch(() => {
          this.listLoading = false
        })
      } else {
        const params = {
          showName: this.queryFrom.key,
          pageNum: this.queryFrom.pageNum,
          channelId: '10000',
          pageSize: this.queryFrom.pageSize
        }
        getApply(params).then(res => {
          this.list = res.data.records
          this.total = Number(res.data.total)
          this.listLoading = false
        }).catch((res) => {
          this.listLoading = false
        })
      }
    },
    reset() {
      this.queryFrom = {
        key: '',
        pageNum: 1,
        pageSize: 10
      }
      this.getList()
    },
    handChang(row) {
      if ((this.type === 'goods' && this.currentChang.length >= 5) || (this.type === 'app' && this.currentChang.length >= 4)) {
        return false
      }
      let key
      if (this.type === 'goods') {
        key = 'goodsId'
      } else {
        key = 'applyId'
      }
      const index = this.currentChang.findIndex((item) => { return row[key] === item[key] })
      if (index === -1) {
        this.currentChang.push(row)
      } else {
        this.$message.warning(this.type === 'goods' ? '该商品已存在' : '该应用已存在')
      }
    },
    handRemove(index) {
      this.currentChang.splice(index, 1)
    },
    confirmChang() {
      this.$emit('currentChang', { list: this.currentChang, type: this.type })
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.table-title {
    font-size: 14px;
    color: #606266;
    font-weight: 700;
}

.flex-between {
    display: flex;
    justify-content: space-between;
}

.title {
    text-align: right;
    vertical-align: middle;
    font-size: 14px;
    color: #606266;
    font-weight: 700;
    display: inline-block;
    width: 80px;
    padding: 0 12px 0 0;
}

::v-deep .pagination-container {
    padding: 10px;
    margin-top: 0px;
    text-align: right;
}

.dialog-footer {
    text-align: center;
}
</style>
