<template>
  <div v-if="!item.hidden">
    <template v-if="hasOneShowingChild(item.children,item) && (!onlyOneChild.children||onlyOneChild.noShowingChildren)&&!item.alwaysShow">
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path)">
        <el-menu-item :index="resolvePath(onlyOneChild.path)" :class="{'submenu-title-noDropdown':!isNest}">
          <!-- 优先使用后端图标，如果没有则使用前端图标 -->
          <svg-icon
            v-if="onlyOneChild.meta.icon && !onlyOneChild.meta.icon_o"
            :icon-class="onlyOneChild.meta.icon"
            class="menu-icon"
          />
          <img
            v-else-if="onlyOneChild.meta.icon_o && onlyOneChild.meta.icon_c"
            class="img"
            :src="isPathActive(onlyOneChild.path) ? onlyOneChild.meta.icon_o : onlyOneChild.meta.icon_c"
            alt=""
          >
          <span>{{ onlyOneChild.meta.title }}</span>
        </el-menu-item>
      </app-link>
    </template>

    <el-submenu v-else ref="subMenu" :index="resolvePath(item.path)" popper-append-to-body>
      <template slot="title">
        <!-- 优先使用后端图标，如果没有则使用前端图标 -->
        <svg-icon
          v-if="item.meta.icon && !item.meta.icon_o"
          :icon-class="item.meta.icon"
          class="menu-icon"
        />
        <img
          v-else-if="item.meta.icon_o && item.meta.icon_c"
          class="img"
          :src="isPathActive(item.path) ? item.meta.icon_o : item.meta.icon_c"
          alt=""
        >
        <span :style="isPathActive(item.path) ? 'color: #409EFF;':'color: #000000;'">{{ item.meta.title }}</span>
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
import path from 'path'
import { isExternal } from '@/utils/validate'
// import Item from './Item'
import AppLink from './Link'
import FixiOSBug from './FixiOSBug'

export default {
  name: 'SidebarItem',
  components: { AppLink },
  mixins: [FixiOSBug],
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  data() {
    // To fix https://github.com/PanJiaChen/vue-admin-template/issues/237
    // TODO: refactor with render function
    this.onlyOneChild = null
    return {
      activeId: ''
    }
  },
  watch: {
    $route(route) {
      this.activeId = this.$route.path
      // console.log("watch path",this.activeId);
    }
  },
  mounted() {
    // console.log("item",this.item)
  },
  created() {
    this.activeId = this.$route.path
  },
  methods: {
    // 判断路径是否激活
    isPathActive(path) {
      // 精确匹配当前路由路径
      if (this.activeId === path) {
        return true
      }

      // 对于有子菜单的情况，检查当前路径是否以该路径开头
      // 但要避免父级菜单在访问子菜单时也高亮
      if (this.item && this.item.children && this.item.children.length > 0) {
        // 如果是父级菜单，只有当前路径完全匹配时才高亮
        return this.activeId === path
      }

      // 对于子菜单，检查是否匹配
      return this.activeId.startsWith(path) && this.activeId !== path
    },

    hasOneShowingChild(children = [], parent) {
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          return false
        } else {
          // Temp set(will be used if only has one showing child)
          this.onlyOneChild = item
          return true
        }
      })
      // When there is only one child router, the child router is displayed by default
      if (showingChildren.length === 1) {
        // 如果父级没有设置iconShow或iconShow为false，则子级继承父级图标
        if (parent.meta && (!parent.meta.hasOwnProperty('iconShow') || !parent.meta.iconShow)) {
          if (parent.meta.icon_o && parent.meta.icon_c) {
            this.onlyOneChild.meta.icon_o = parent.meta.icon_o
            this.onlyOneChild.meta.icon_c = parent.meta.icon_c
          }
        }
        return true
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ... parent, noShowingChildren: true }
        // console.log('onlyOneChild',this.onlyOneChild)
        return true
      }

      return false
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(this.basePath)) {
        return this.basePath
      }
      return path.resolve(this.basePath, routePath)
    }
  }
}
</script>

<style lang="scss" scoped>
.img {
  width: 20px;
  height: 20px;
}

.menu-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  font-size: 18px;
}

.nest-menu{
  height:50px;
}
</style>
