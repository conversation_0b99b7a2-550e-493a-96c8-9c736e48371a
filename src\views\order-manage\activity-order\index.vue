<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <div class="filter-container">
      <el-form :inline="true" :model="listQuery" class="demo-form-inline">
        <el-form-item label="活动ID">
          <el-input
            v-model="listQuery.actId"
            placeholder="请输入活动编码"
            style="width: 200px;"
            clearable
          />
        </el-form-item>
        <el-form-item label="活动名称">
          <el-input
            v-model="listQuery.actTitle"
            placeholder="请输入活动名称"
            style="width: 200px;"
            clearable
          />
        </el-form-item>
        <el-form-item label="活动类型">
          <el-select v-model="listQuery.typeTitle" placeholder="请选择活动类型" clearable style="width: 150px">
            <el-option label="全部" value="" />
            <el-option
              v-for="item in activityTypeOptions"
              :key="item.id"
              :label="item.typeTitle"
              :value="item.typeTitle"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select v-model="listQuery.orderStatus" placeholder="请选择订单状态" clearable style="width: 150px">
            <el-option label="全部" value="" />
            <el-option label="待签到" value="0" />
            <el-option label="已取消" value="1" />
            <el-option label="已签到" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="报名时间">
          <el-date-picker
            v-model="registerTimeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 350px"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-ddTHH:mm:ss"
            @change="handleTimeRangeChange"
          />
        </el-form-item>
        <el-form-item label="用户姓名">
          <el-input
            v-model="listQuery.registerName"
            placeholder="请输入用户姓名"
            style="width: 150px;"
            clearable
          />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input
            v-model="listQuery.registerTel"
            placeholder="请输入手机号"
            style="width: 150px;"
            clearable
          />
        </el-form-item>
        <el-form-item label="证件号码">
          <el-input
            v-model="listQuery.registerIdCard"
            placeholder="请输入证件号码"
            style="width: 180px;"
            clearable
          />
        </el-form-item>
        <el-form-item label="行内客户号">
          <el-input
            v-model="listQuery.inBankNo"
            placeholder="请输入客户号"
            style="width: 150px;"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleFilter">
            查询
          </el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 功能按钮 -->
    <div class="filter-container" style="margin-bottom: 10px;">
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="success"
        icon="el-icon-download"
        @click="handleExport"
      >
        导出
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-check"
        @click="handleBatchSign"
      >
        签到
      </el-button>
    </div>

    <!-- 数据表格 -->
    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单号" prop="id" width="120" align="center" />
      <el-table-column label="活动ID" prop="actId" width="100" align="center" />
      <el-table-column label="活动名称" prop="actTitle" min-width="150" />
      <el-table-column label="活动类型" prop="typeTitle" width="120" align="center" />
      <el-table-column label="活动名额" prop="numRage" width="100" align="center">
        <template slot-scope="{row}">
          <span>{{ row.numRage === 0 ? '不限制' : row.numRage }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户姓名" prop="registerName" width="120" align="center" />
      <el-table-column label="客户等级" prop="userGrade" width="100" align="center" />
      <el-table-column label="手机号" prop="registerTel" width="130" align="center" />
      <el-table-column label="行内客户号" prop="inBankNo" width="120" align="center" />
      <el-table-column label="证件号码" prop="registerIdCard" width="150" align="center" />
      <el-table-column label="报名人数" prop="registrantsNum" width="100" align="center" />
      <el-table-column label="活动时间" width="180" align="center">
        <template slot-scope="{row}">
          <span>{{ formatActivityTime(row.actStartTime, row.actEndTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报名时间" width="160" align="center">
        <template slot-scope="{row}">
          <span>{{ formatTime(row.registerTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="签到时间" width="160" align="center">
        <template slot-scope="{row}">
          <span>{{ row.signTime ? formatTime(row.signTime) : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单状态" width="100" align="center">
        <template slot-scope="{row}">
          <el-tag :type="getOrderStatusType(row.orderStatus)">
            {{ getOrderStatusText(row.orderStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作者" prop="operator" width="120" align="center" />
      <el-table-column label="操作" align="center" width="120">
        <template slot-scope="{row}">
          <div style="text-align: center;">
            <el-button type="primary" size="mini" @click="handleViewDetail(row)">
              查看详情
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 订单详情弹窗 -->
    <order-detail-dialog
      :visible.sync="detailDialogVisible"
      :order-id="currentOrderId"
    />
  </div>
</template>

<script>
import { getRegisterInfoList, signOrder, exportOrderList } from '@/api/orderManage'
import { getActivityType } from '@/api/actManage'
import Pagination from '@/components/Pagination'
import OrderDetailDialog from './components/OrderDetailDialog'
import { parseTime } from '@/utils'
import { downOrViewFile, formatDates } from '@/utils'

export default {
  name: 'ActivityOrderList',
  components: {
    Pagination,
    OrderDetailDialog
  },
  filters: {
    parseTime
  },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        actId: '',
        actTitle: '',
        typeTitle: '',
        orderStatus: '',
        registerTimeStart: '',
        registerTimeEnd: '',
        registerName: '',
        registerTel: '',
        registerIdCard: '',
        inBankNo: ''
      },
      registerTimeRange: [],
      selectedRows: [],
      detailDialogVisible: false,
      currentOrderId: null,
      activityTypeOptions: [] // 活动类型选项
    }
  },
  created() {
    this.getList()
    this.getActivityTypeList()
  },
  methods: {
    getList() {
      this.listLoading = true
      const params = { ...this.listQuery }
      // 清空空值参数
      Object.keys(params).forEach(key => {
        if (params[key] === '') {
          delete params[key]
        }
      })

      getRegisterInfoList(params).then(response => {
        console.log('订单列表API响应:', response)
        if (response.data) {
          if (Array.isArray(response.data)) {
            // 如果data直接是数组
            this.list = response.data
            this.total = response.data.length
          } else if (response.data.list && Array.isArray(response.data.list)) {
            // 如果data包含list数组（当前API格式）
            this.list = response.data.list
            this.total = response.data.total || response.data.list.length
          } else if (response.data.records && Array.isArray(response.data.records)) {
            // 如果data包含records数组
            this.list = response.data.records
            this.total = response.data.total || response.data.records.length
          } else {
            this.list = []
            this.total = 0
          }
        } else {
          this.list = []
          this.total = 0
        }
        this.listLoading = false
      }).catch(error => {
        console.error('获取订单列表失败:', error)
        this.list = []
        this.total = 0
        this.listLoading = false
        this.$message.error('获取数据失败，请稍后重试')
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    resetQuery() {
      this.listQuery = {
        page: 1,
        limit: 20,
        actId: '',
        actTitle: '',
        typeTitle: '',
        orderStatus: '',
        registerTimeStart: '',
        registerTimeEnd: '',
        registerName: '',
        registerTel: '',
        registerIdCard: '',
        inBankNo: ''
      }
      this.registerTimeRange = []
      this.getList()
    },
    handleTimeRangeChange(value) {
      if (value && value.length === 2) {
        this.listQuery.registerTimeStart = value[0]
        this.listQuery.registerTimeEnd = value[1]
      } else {
        this.listQuery.registerTimeStart = ''
        this.listQuery.registerTimeEnd = ''
      }
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    handleBatchSign() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请至少选择一笔订单')
        return
      }

      // 检查订单状态（查询条件用数字，但API返回中文状态）
      const invalidOrders = this.selectedRows.filter(row => row.orderStatus !== '待签到')
      if (invalidOrders.length > 0) {
        this.$message.error('订单状态非待签到状态，不可签到！')
        return
      }

      this.$confirm('确定对该订单进行签到操作吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.performBatchSign()
      })
    },
    async performBatchSign() {
      const signPromises = this.selectedRows.map(row => signOrder(row.id))

      try {
        await Promise.all(signPromises)
        this.$message.success('签到成功!')
        this.getList()
      } catch (error) {
        console.error('签到失败:', error)
        this.$message.error('签到失败，请重试')
      }
    },
    handleExport() {
      const params = { ...this.listQuery }
      Object.keys(params).forEach(key => {
        if (params[key] === '') {
          delete params[key]
        }
      })

      exportOrderList(params).then(response => {
        downOrViewFile(response, '活动订单列表_' + formatDates(new Date()) + '.xlsx')
        this.$message.success('导出成功')
      }).catch(error => {
        console.error('导出失败:', error)
        this.$message.error('导出失败，请重试')
      })
    },
    handleViewDetail(row) {
      this.currentOrderId = row.id
      this.detailDialogVisible = true
    },
    formatTime(time) {
      if (!time) return '-'
      return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
    },
    formatActivityTime(startTime, endTime) {
      if (!startTime || !endTime) return '-'
      const start = parseTime(startTime, '{y}-{m}-{d} {h}:{i}:{s}')
      const end = parseTime(endTime, '{y}-{m}-{d} {h}:{i}:{s}')
      return `${start} ~ ${end}`
    },
    getOrderStatusType(status) {
      const statusMap = {
        '待签到': 'warning',
        '已取消': 'danger',
        '已签到': 'success'
      }
      return statusMap[status] || 'info'
    },
    getOrderStatusText(status) {
      // API直接返回中文状态，直接使用
      return status || '未知'
    },
    // 获取活动类型列表
    getActivityTypeList() {
      getActivityType({
        pageNum: 1,
        pageSize: 999,
        delFlag: 0 // 只获取未删除的活动类型
      }).then(response => {
        console.log('活动类型API响应:', response)
        if (response.data && response.data.list) {
          // 注意：状态字段是 actTypeStatus，1表示启用
          this.activityTypeOptions = response.data.list.filter(item => item.actTypeStatus === 1)
          console.log('过滤后的活动类型:', this.activityTypeOptions)
        }
      }).catch(error => {
        console.error('获取活动类型失败:', error)
      })
    }
  }
}
</script>

<style scoped>
.filter-container {
  padding-bottom: 10px;
}
.filter-container .filter-item {
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 10px;
}
</style>
