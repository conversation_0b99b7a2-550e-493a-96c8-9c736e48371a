<template>
  <div :class="{'has-logo':showLogo}">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="false"
        :collapse-transition="false"
        mode="vertical"
        class="menu"
      >
        <sidebar-item v-for="route in permission_routes" :key="route.path" :item="route" :base-path="route.path" />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/styles/variables.scss'

export default {
  components: { SidebarItem, Logo },
  computed: {
    ...mapGetters([
      'permission_routes',
      'sidebar'
    ]),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  },
  methods: {
    // test() {
    //   console.log('全部',this.permission_routes);
    // }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .menu {
      width: 226px;
      // min-width: 226px;
      height: calc(100vh -50px) ;
      padding-top: 18px ;
      margin-right: 2px;
      border-right: unset;
      list-style-type: none;
      background: #FFFFFF;
      .menu-li {
        padding-top: 15px !important;
        padding-bottom:  15px !important;
        padding-left: 34px !important;
      }

      .menu-head {
        padding: 0 10px !important;
        display: flex;
        align-items: center;

        .menu-title {
          font-size: 14px;
          font-weight: 600;
          color: #000000;
        }

        .title-img {
          width: 16px;
          height: 16px;
          margin-right: 8px;
        }
      }
      .el-submenu{
        // margin-bottom: 10px;

        .el-submenu__title{
          // margin: 0px 8px;
          height: 50px;
          line-height: 50px;
        }
        .el-menu--inline {
          background: linear-gradient(180deg, #FAFAFA 0%, rgba(250, 250, 250, 0.78) 109.37%);
          box-shadow: inset 0px 1px 2px rgba(0, 0, 0, 0.04);
        }
      }
      .el-submenu.is-active .el-submenu__title {
          color: #409EFF;
        }
      .el-menu-item {
        // width: 170px;
        min-width: unset;
        height: 50px;
        line-height: 50px;
        // margin-bottom: 5px;
        // margin: 0px 8px 10px;

      }

      .el-menu-item.is-active {
        background: rgba(199, 227, 255, 0.4);
        // box-shadow: 0px 4px 6px 0px rgba(0, 109, 210, 0.2);
        border-radius: 2px;
        // color: #fff;

        .menu-title {
          color:  #2474FF;
        }
      }

    .el-menu-item.is-disabled {
      opacity: unset;
      cursor: default;
      background: unset;
    }
}
</style>
