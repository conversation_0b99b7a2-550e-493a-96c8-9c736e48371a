<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="path-1-inside-1_160_644" fill="white">
<path d="M8.92736 5.00142C9.72812 3.98227 11.2719 3.98228 12.0726 5.00142L15.7863 9.72792C15.9248 9.90411 16 10.1217 16 10.3457V13.9999C16 15.1045 15.1046 15.9999 14 15.9999H7C5.89543 15.9999 5 15.1045 5 13.9999V10.3457C5 10.1217 5.07525 9.90411 5.21368 9.72792L8.92736 5.00142Z"/>
</mask>
<path d="M8.92736 5.00142C9.72812 3.98227 11.2719 3.98228 12.0726 5.00142L15.7863 9.72792C15.9248 9.90411 16 10.1217 16 10.3457V13.9999C16 15.1045 15.1046 15.9999 14 15.9999H7C5.89543 15.9999 5 15.1045 5 13.9999V10.3457C5 10.1217 5.07525 9.90411 5.21368 9.72792L8.92736 5.00142Z" fill="url(#paint0_linear_160_644)" fill-opacity="0.8"/>
<path d="M15.7863 9.72792L15 10.3457L15.7863 9.72792ZM5.21368 9.72792L6 10.3457L5.21368 9.72792ZM14 14.9999H7V16.9999H14V14.9999ZM6 13.9999V10.3457H4V13.9999H6ZM15 10.3457V13.9999H17V10.3457H15ZM6 10.3457L9.71368 5.61924L8.14105 4.3836L4.42736 9.1101L6 10.3457ZM11.2863 5.61924L15 10.3457L16.5726 9.1101L12.859 4.3836L11.2863 5.61924ZM9.71368 5.61924C10.1141 5.10967 10.8859 5.10967 11.2863 5.61924L12.859 4.3836C11.6578 2.85488 9.34218 2.85488 8.14105 4.3836L9.71368 5.61924ZM17 10.3457C17 9.89762 16.8495 9.46247 16.5726 9.1101L15 10.3457H17ZM6 10.3457V10.3457L4.42736 9.1101C4.1505 9.46247 4 9.89762 4 10.3457H6ZM7 14.9999C6.44772 14.9999 6 14.5522 6 13.9999H4C4 15.6567 5.34315 16.9999 7 16.9999V14.9999ZM14 16.9999C15.6569 16.9999 17 15.6567 17 13.9999H15C15 14.5522 14.5523 14.9999 14 14.9999V16.9999Z" fill="#666666" mask="url(#path-1-inside-1_160_644)"/>
<path d="M4 11L9.76721 4.78916C10.1628 4.3631 10.8372 4.3631 11.2328 4.78916L17 11" stroke="#666666" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9 15L11.5 15V16H9V15Z" fill="white"/>
<g filter="url(#filter0_d_160_644)">
<rect x="8" y="12" width="3" height="4" fill="url(#paint1_linear_160_644)"/>
</g>
<defs>
<filter id="filter0_d_160_644" x="8" y="11.5" width="3.5" height="4.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.5" dy="-0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_160_644"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_160_644" result="shape"/>
</filter>
<linearGradient id="paint0_linear_160_644" x1="11.2934" y1="5.8572" x2="11.2934" y2="15.1597" gradientUnits="userSpaceOnUse">
<stop stop-color="#EEEEEE"/>
<stop offset="1" stop-color="#999999"/>
</linearGradient>
<linearGradient id="paint1_linear_160_644" x1="9.5" y1="12" x2="9.5" y2="16" gradientUnits="userSpaceOnUse">
<stop stop-color="#DDDDDD"/>
<stop offset="1" stop-color="#FAFAFA"/>
</linearGradient>
</defs>
</svg>
