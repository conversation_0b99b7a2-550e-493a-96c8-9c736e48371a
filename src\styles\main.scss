.main-body {
  margin: 20px;
  &-top,&-bottom,&-content {
    background-color: #ffffff;
    border: 1px solid #dfdfdf;
    // box-shadow: 0 3px 6px rgba(0, 0, 0, 0.06);
  }
  &-top {
    margin-bottom: 20px;
    padding: 20px 20px 0;
    box-shadow: 0px 2px 9px rgba(0, 0, 0, 0.17);
    border-radius: 8px;
    .el-form {
      &.el-form--inline {
        .el-input {
          width: 223px;
        }
      }
    }
  }
  &-bottom {
    box-shadow: 0px 2px 9px rgba(0, 0, 0, 0.17);
    border-radius: 8px;
    &-btn {
      margin: 15px;
      display: flex;
      justify-content: space-between;
    }
  }
}
// .el-dialog {
//   background: linear-gradient(180deg, #F0F8FF 0%, #FFFFFF 8.44%);
//   box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.25);
//   border-radius: 4px;
//   .el-dialog__header {
//     height: 40px;
//     padding-top: 10px;
//     .el-dialog__headerbtn{
//       top: 10px;
//     }
//   }

//   .el-dialog__title {
//     margin-top: 10px;
//     margin-right: 20px;
//     font-family: 'Source Han Sans CN';
//     font-style: normal;
//     font-weight: 500;
//     font-size: 16px;
//     line-height: 22px;
//     color: rgba(0, 0, 0, 0.9);
//   }
// }

.el-table th > .cell {
  color: black!important;
  text-align: center;
}
.el-table .cell  {
  text-align: center
}
.pagination {
  margin: 25px;
  display: flex;
  justify-content: flex-end;

}

.full-width {
  &.el-form {
    .el-form-item {
      .el-form-item__content {
        &>div {
          width: 100%;
        }
      }
    }
  }
}