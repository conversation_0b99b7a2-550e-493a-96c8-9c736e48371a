<template>
  <div>
    <RouteTitle />
    <div class="password">
      <div class="line" />
      <el-form
        ref="updateForm"
        :model="updateForm"
        :rules="updateRules"
        class="form-width"
        auto-complete="on"
      >
        <el-form-item prop="oldPassword" label="旧密码">
          <el-input
            v-model="updateForm.oldPassword"
            :type="oldPwdType"
            name="oldPassword"
            auto-complete="on"
            placeholder="请输入当前密码"
            show-password
          />

        </el-form-item>
        <el-form-item prop="password" label="新密码">
          <el-input
            v-model="updateForm.password"
            :type="pwdType"
            name="password"
            auto-complete="on"
            placeholder="新密码长度必须是8-20位, 包含字母，数字和特殊符号3种组合"
            show-password
          />
        </el-form-item>
        <el-form-item prop="confirmPassword" label="确认密码">
          <el-input
            v-model="updateForm.confirmPassword"
            :type="confirmPwdType"
            name="confirmPassword"
            auto-complete="on"
            placeholder="请确认新密码"
            show-password
          />
        </el-form-item>
        <el-form-item>
          <div class="next" align="center">
            <el-button
              type="default"
              @click.native.prevent="resetForm('updateForm')"
            >
              重置
            </el-button>
            <el-button
              :loading="loading"
              type="primary"
              @click.native.prevent="handleUpdate"
            >
              确认
            </el-button>
          </div>

        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import sysManageApi from '../../api/sysManageApi'
import userApi from '../../api/userApi'
import JSEncrypt from 'jsencrypt'
import RouteTitle from '@/components/RouteTitle'

export default {
  components: {
    RouteTitle
  },
  data() {
    const validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入新密码'))
      } else {
        // if (!validPassword(value)) {
        //   callback(new Error('密码是8-20位字符,至少包含数字、字母！'))
        // }
        if (this.updateForm.confirmPassword !== '') {
          this.$refs.updateForm.validateField('confirmPassword')
        }
        callback()
      }
    }
    const validateConfirmPass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.updateForm.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      pwdType: 'password',
      oldPwdType: 'password',
      confirmPwdType: 'password',
      loading: false,
      updateForm: {
        password: '',
        oldPassword: '',
        confirmPassword: ''
      },
      updateRules: {
        password: [
          { required: true, trigger: 'blur', validator: validatePass }
        ],
        oldPassword: [
          { required: true, trigger: 'blur', message: '旧密码不得为空' }
        ],
        confirmPassword: [
          { required: true, trigger: 'blur', validator: validateConfirmPass }
        ]
      }
    }
  },

  mounted() {
  },
  methods: {

    handleUpdate() {
      this.$refs.updateForm.validate((valid) => {
        if (valid) {
          if (this.updateForm.oldPassword === this.updateForm.confirmPassword) {
            this.$message.error('新旧密码不能一致')
            return
          }
          if (this.$store.getters.userInfo.userName === this.updateForm.confirmPassword) {
            this.$message.error('密码不能和用户名一致')
            return
          }
          const regStr = '^(?=.*[A-Za-z])(?=.*\\d)(?=.*[^\\da-zA-Z\\s]).{8,20}$'
          var reg = new RegExp(regStr)
          if (!reg.test(this.updateForm.confirmPassword)) {
            this.$message.error('密码长度必须是8-20位, 包含字母，数字和特殊符号3种组合')
            return
          }
          this.loading = true
          userApi.getPublicKey().then(res => {
            var publicKey = res.data
            if (publicKey) {
              const encryptStr = new JSEncrypt()
              encryptStr.setPublicKey(publicKey)
              const params = {
                oldPwd: encryptStr.encrypt(this.updateForm.oldPassword),
                newPwd: encryptStr.encrypt(this.updateForm.password),
                oriNewPwd: encryptStr.encrypt(this.updateForm.confirmPassword),
                userId: this.$store.getters.userInfo.id
              }

              sysManageApi.modifyPassword(params).then((res) => {
                this.loading = false
                if (res.code === 200) {
                  this.$message.success('修改密码成功！')
                  this.updateForm = {
                    password: '',
                    oldPassword: '',
                    confirmPassword: ''
                  }
                  this.logout()
                } else {
                  this.$message.error(res.msg)
                }
              })
                .catch((error) => {
                  this.loading = false
                  this.$message.error(error.data.msg)
                })
            }
          })
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    async logout() {
      await this.$store.dispatch('user/logout')
    }
  }
}

</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>

.password {
  width: 96%;
  height: 600px;
  margin: 20px auto 0;
  background: linear-gradient(180deg, #F0F8FF 0%, #FFFFFF 22.48%);
  box-shadow: 0px 2px 9px rgba(0, 0, 0, 0.17);
  border-radius: 8px;
}
.next {
  margin-top: 20px;

  // background-color: rgba(251, 251, 251, 1);
  width: 100%;

}
.form-width {
  width: 40%;
  margin:  auto ;
  padding-top: 56px;
}
// .active{
//   background: pink;
// }
// .el-form-item{
//   position: relative;
// }
// .show-pwd{
//   position: absolute;
//   top: auto;
//   right: 10px;
// }
</style>
