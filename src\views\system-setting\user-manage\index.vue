<template>
  <div>
    <RouteTitle />
    <div class="main-body">
      <div class="main-body-bottom">
        <div class="body-top-form">
          <el-form
            ref="searchForm"
            class="left"
            :model="mainObj.searchForm"
            label-position="left"
            inline
          >
            <el-form-item label="用户名">
              <el-input
                v-model.trim="mainObj.searchForm.loginName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
            <!-- <el-form-item label="手机号码">
          <el-input
            v-model.trim="mainObj.searchForm.phoneNo"
            placeholder="请输入"
            clearable
          />
        </el-form-item> -->
            <el-form-item>
              <el-button type="primary" @click="onSearch">查询</el-button>
              <el-button @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
          <div
            v-if="addPermission || deleteMultiplePermission"
            class="right"
          >
            <div class="main-body-bottom-btn-left">
              <el-button
                v-if="addPermission"
                type="primary"
                @click="onOperate('add')"
              >新增</el-button>
              <el-button
                v-if="deleteMultiplePermission"
                type="primary"
                @click="onOperate('deleteMultiple')"
              >删除</el-button>
            </div>
          </div>
        </div>
        <div class="table">
          <el-table
            ref="tableRef"
            :data="mainObj.list"
            fit
            border
            highlight-current-row
            :header-cell-style="{ background: '#F9F9F9' }"
          >
            <!-- <el-table-column
              type="selection"
              width="55"
            /> -->
            <el-table-column prop="userName" label="用户名" min-width="120px" />
            <el-table-column prop="userStatus" label="用户状态" width="120px" />
            <!-- <el-table-column
              prop="mobile"
              label="手机号码"
              width="150px"
            /> -->
            <el-table-column
              prop="roleDetailDtoList"
              label="角色"
              min-width="120px"
            >
              <template slot-scope="scope">
                <el-tag
                  v-for="item in scope.row.roleDetailDtoList"
                  :key="item.id"
                >{{ item.roleName }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="updateTime" label="更新时间" width="180px" />
            <el-table-column label="操作" width="280">
              <template slot-scope="scope">
                <el-button
                  v-if="sysUser_details"
                  type="text"
                  @click="onOperate('view', scope.row)"
                >查看</el-button>
                <el-button
                  v-if="updatePermission && scope.row.userStatus.code === '1'"
                  v-preventReClick
                  type="text"
                  @click="onOperate('update', scope.row)"
                >编辑</el-button>
                <el-button
                  v-if="deleteSinglePermission"
                  type="text"
                  @click="onOperate('delete', scope.row)"
                >删除</el-button>
                <el-button
                  v-if="resetPermission && scope.row.userStatus.code === '1'"
                  v-preventReClick
                  type="text"
                  @click="onOperate('resetPass', scope.row)"
                >密码重置</el-button>
                <el-button
                  v-if="scope.row.userStatus.code === '2' && sysUser_enable"
                  v-preventReClick
                  type="text"
                  @click="onOperate('switch', scope.row)"
                >启用</el-button>
                <el-button
                  v-if="scope.row.userStatus.code === '1' && sysUser_enable"
                  v-preventReClick
                  type="text"
                  @click="onOperate('switch', scope.row)"
                >禁用</el-button>
                <el-button
                  v-if="scope.row.userStatus.code === '3' && sysUser_unlock"
                  v-preventReClick
                  type="text"
                  @click="onOperate('unlock', scope.row)"
                >解锁</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination
            :current-page="mainObj.currentPage"
            :page-size="mainObj.pageSize"
            :page-sizes="mainObj.pagesizes"
            :total="mainObj.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <!-- 用户新增/编辑弹窗 -->
    <el-dialog
      :title="userDialogObj.title"
      :visible.sync="userDialogObj.visible"
      destroy-on-close
      width="800px"
      @close="userDialogObj.visible = false"
    >
      <el-form
        ref="userFormRef"
        :model="userDialogObj.form"
        :rules="userDialogObj.type === 'view' ? null : userDialogObj.rules"
        :disabled="userDialogObj.type === 'view'"
        label-width="80px"
        label-position="left"
        class="full-width"
      >
        <el-form-item label="用户名" prop="userName">
          <el-input
            v-model="userDialogObj.form.userName"
            maxlength="20"
            placeholder="请输入"
            :disabled="userDialogObj.type !== 'add'"
            clearable
          />
        </el-form-item>
        <!-- <el-form-item label="手机号码" prop="mobile">
          <el-input
            v-model="userDialogObj.form.mobile"
            maxlength="11"
            placeholder="请输入"
            clearable
          />
        </el-form-item>-->
        <el-form-item label="角色" prop="roleIdList">
          <el-select
            v-model="userDialogObj.form.roleIdList"
            placeholder="请选择"
            multiple
            filterable
            clearable
          >
            <el-option
              v-for="item in roleEnums"
              :key="item.id"
              :label="item.roleName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div v-if="userDialogObj.type !== 'view'" slot="footer">
        <el-button @click="userDialogObj.visible = false">取消</el-button>
        <el-button
          v-preventReClick
          type="primary"
          :loading="isloading"
          @click="onOperate('submit')"
        >确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import sysManageApi from '@/api/sysManageApi'
import checkPermission from '@/utils/permission'
import { obj2form } from '@/utils'
import RouteTitle from '@/components/RouteTitle'

const defaultSearchForm = {
  userName: ''
  // mobile: ''
}

const defaultUserForm = {
  userName: '',
  // mobile: '',
  roleIdList: []
}
export default {
  components: {
    RouteTitle
  },
  data() {
    //    const validatePhone = (rule, value, callback) => {
    //      if (!validPhone(value)) {
    //        callback(new Error('手机号码格式不正确'))
    //      } else {
    //        callback()
    //      }
    //    }
    const validateIds = (rule, value, callback) => {
      if (value.length === 0) {
        callback(new Error('不能为空'))
      } else {
        callback()
      }
    }
    return {
      isloading: false,
      addPermission: checkPermission(['sysUser_add']),
      deleteSinglePermission: checkPermission(['sysUser_delete']),
      deleteMultiplePermission: checkPermission(['sysUser_batchDelete']),
      updatePermission: checkPermission(['sysUser_modify']),
      resetPermission: checkPermission(['sysUser_resetPwd']),
      sysUser_enable: checkPermission(['sysUser_disable']),
      sysUser_details: checkPermission(['sysUser_details']),
      sysUser_unlock: checkPermission(['sysUser_unlock']),
      roleEnums: [],
      mainObj: {
        list: [],
        pageSize: 10,
        pagesizes: [10, 30, 50],
        currentPage: 1,
        total: 0,
        searchForm: { ...defaultSearchForm }
      },
      userDialogObj: {
        visible: false,
        title: '',
        type: '',
        form: Object.assign({}, defaultUserForm),
        rules: {
          userName: [{ required: true, message: '不能为空', trigger: 'blur' }],
          //          mobile: [
          //            { required: true, validator: validatePhone, trigger: 'blur' },
          //            { required: true, message: '不能为空', trigger: 'blur' }
          //          ],
          roleIdList: [
            { required: true, validator: validateIds, trigger: 'blur' }
          ]
        }
      }
    }
  },
  created() {
    this.getValidRole()

    this.onSearch()
  },
  methods: {
    checkPermission,
    getValidRole() {
      // sysManageApi.getTypeEnum({enumName:'UserTypeEnum' })
      sysManageApi.getValidRole().then(res => {
        this.roleEnums = res.data
      })
    },
    onSearch() {
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    onReset() {
      this.mainObj.searchForm = { ...defaultSearchForm }
      this.fetchData()
    },
    handleSizeChange(val) {
      this.mainObj.pageSize = val
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.mainObj.currentPage = val
      this.fetchData()
    },
    fetchData() {
      const params = {
        userName: this.mainObj.searchForm.loginName,
        // mobile: this.mainObj.searchForm.phoneNo,
        pageNum: this.mainObj.currentPage,
        pageSize: this.mainObj.pageSize
      }
      sysManageApi.getUserList(params).then(res => {
        this.mainObj.list = res.data.list
        this.mainObj.total = res.data.total
      })
    },
    onOperate(type, row) {
      if (type === 'add') {
        this.userDialogObj.visible = true
        this.userDialogObj.type = type
        this.userDialogObj.title = '新增'
        this.userDialogObj.form = Object.assign({}, defaultUserForm)
        this.$nextTick(() => {
          this.$refs.userFormRef.clearValidate()
        })
      } else if (type === 'update') {
        this.userDialogObj.visible = true
        this.userDialogObj.type = type
        this.userDialogObj.title = '修改'
        const params = {
          userId: row.id
        }
        sysManageApi.getUserInfo(params).then(res => {
          this.userDialogObj.form = {
            id: res.data.id,
            userName: res.data.userName,
            // mobile: res.data.mobile,
            roleIdList: res.data.roleDetailDtoList.map(e => e.id)
          }
          this.$nextTick(() => {
            this.$refs.userFormRef.clearValidate()
          })
        })
      } else if (type === 'view') {
        this.userDialogObj.visible = true
        this.userDialogObj.type = type
        this.userDialogObj.title = '查看'
        const params = {
          userId: row.id
        }
        sysManageApi.getUserInfo(params).then(res => {
          this.userDialogObj.form = {
            userName: res.data.userName,
            // mobile: res.data.mobile,
            roleIdList: res.data.roleDetailDtoList.map(e => e.id)
          }
          this.$nextTick(() => {
            this.$refs.userFormRef.clearValidate()
          })
        })
      } else if (type === 'submit') {
        this.$refs.userFormRef.validate(valid => {
          if (valid) {
            this.isloading = true
            const params = {
              userName: this.userDialogObj.form.userName,
              // mobile: this.userDialogObj.form.mobile,
              roleIdList: this.userDialogObj.form.roleIdList,
              userType: '1'
            }
            if (this.userDialogObj.type === 'update') {
              params.id = this.userDialogObj.form.id
            }
            sysManageApi.saveUser(params, this.userDialogObj.type).then(res => {
              this.$message({
                type: 'success',
                message: res.msg
              })
              this.fetchData()
              this.userDialogObj.visible = false
              this.isloading = false
            })
          }
        })
      } else if (type === 'resetPass') {
        this.$confirm('确定重置该用户的密码吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            const params = {
              userId: row.id
            }
            sysManageApi.resetUserPassword(obj2form(params)).then(res => {
              this.$message({
                type: 'success',
                message: res.msg
              })
              this.fetchData()
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            })
          })
      } else if (type === 'delete') {
        this.$confirm('确定删除该用户吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            const params = {
              userId: row.id
            }
            sysManageApi.deleteUser(obj2form(params)).then(res => {
              this.$message({
                type: 'success',
                message: res.msg
              })
              this.fetchData()
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            })
          })
      } else if (type === 'deleteMultiple') {
        const selection = this.$refs.tableRef.selection
        if (selection.length === 0) {
          this.$message({
            type: 'info',
            message: '请至少选择一条数据'
          })
          return
        }
        this.$confirm('确定删除选择的用户吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            const userIdList = []
            selection.forEach(e => {
              userIdList.push(e.id)
            })
            const params = {
              userIdList: userIdList
            }
            sysManageApi.deleteUserBatch(params).then(res => {
              this.$message({
                type: 'success',
                message: res.msg
              })
              this.fetchData()
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            })
          })
      } else if (type === 'switch') {
        const params = {
          userId: row.id
        }
        let tipsStr
        if (row.userStatus.code === '2') {
          tipsStr = '确定启用该用户吗?'
        } else {
          tipsStr = '确定禁用该用户吗?'
        }

        this.$confirm(tipsStr, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            sysManageApi.enableOrDisableUser(obj2form(params)).then(res => {
              this.$message({
                type: 'success',
                message: res.msg
              })
              this.fetchData()
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            })
          })
      } else if (type === 'unlock') {
        const params = {
          userId: row.id
        }
        let tipsStr
        if (row.userStatus.code === '3') {
          tipsStr = '确定解锁该用户吗?'
        }

        this.$confirm(tipsStr, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            sysManageApi.unlockUser(obj2form(params)).then(res => {
              this.$message({
                type: 'success',
                message: res.msg
              })
              this.fetchData()
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            })
          })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.main-body-bottom {
  box-sizing: border-box;
  padding: 20px;
}

.right {
  float: right;
}

.left {
  float: left;
}

</style>
