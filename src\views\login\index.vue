<template>
    <div class="login-container">
        <div class="login_content">
            <!-- <div class="left_content">
        <div class="left_company_name">相亲社区</div>

        <div class="welcome" style=" margin-top: 90px;">欢迎登录</div>
        <div class="welcome" style=" margin-top: 20px;">相亲社区管理平台</div>
      </div> -->
            <div class="right_content">
                <div class="welcome" style="margin-top: 50px">
                    南京银行活动管理平台
                </div>
                <!-- <div class="welcome" style="margin-top: 0px"></div> -->
                <el-form
                    ref="loginForm"
                    :model="loginForm"
                    class="login-form"
                    :rules="loginRules"
                >
                    <el-form-item prop="username">
                        <el-input
                            ref="username"
                            v-model="loginForm.username"
                            placeholder="账户"
                            name="username"
                            type="text"
                            tabindex="1"
                            autocomplete="on"
                        />
                    </el-form-item>

                    <el-tooltip
                        v-model="capsTooltip"
                        content="大写已打开"
                        placement="right"
                        manual
                    >
                        <el-form-item prop="password">
                            <el-input
                                :key="passwordType"
                                ref="password"
                                v-model="loginForm.password"
                                :type="passwordType"
                                placeholder="密码"
                                name="password"
                                tabindex="2"
                                autocomplete="on"
                                show-password
                                @keyup.native="checkCapslock"
                                @blur="capsTooltip = false"
                                @keyup.enter.native="getPublicKey"
                            />
                        </el-form-item>
                    </el-tooltip>

                    <div class="imageCode">
                        <el-form-item prop="loginImageCode">
                            <el-input
                                ref="loginImageCode"
                                v-model="loginForm.loginImageCode"
                                placeholder="点击刷新图片"
                                name="loginImageCode"
                                tabindex="3"
                            />
                        </el-form-item>
                        <div
                            style="cursor: pointer; height: 48px"
                            @click="getImgCode"
                        >
                            <img
                                style="
                                    height: 48px;
                                    width: 120px;
                                    margin-left: 5px;
                                "
                                :src="imgStr"
                                alt=""
                            />
                        </div>
                    </div>
                    <el-button
                        :loading="loading ? true : false"
                        type="primary"
                        style="
                            background: #2474ff;
                            width: 100%;
                            margin-bottom: 10px;
                            height: 48px;
                        "
                        @click.native.prevent="getPublicKey"
                        >登录</el-button
                    >
                </el-form>
            </div>
        </div>
    </div>
</template>

<script>
import userApi from "@/api/userApi";
import JSEncrypt from "jsencrypt";

export default {
    name: "Login",
    data() {
        return {
            imgStr: "",
            imgKey: "",
            publicKey: null,
            loginForm: {
                username: "",
                password: "",
                loginImageCode: "",
            },
            loginRules: {
                username: [
                    { required: true, trigger: "blur", message: "请输入账号" },
                ],
                password: [
                    { required: true, trigger: "blur", message: "请输入密码" },
                ],
                loginImageCode: [
                    {
                        required: true,
                        trigger: "blur",
                        message: "请输入图形验证码",
                    },
                ],
            },
            passwordType: "password",
            capsTooltip: false,
            loading: false,
            showDialog: false,
            redirect: undefined,
            otherQuery: {},
        };
    },
    watch: {
        $route: {
            handler: function (route) {
                const query = route.query;
                if (query) {
                    this.redirect = query.redirect;
                    this.otherQuery = this.getOtherQuery(query);
                }
            },
            immediate: true,
        },
    },
    created() {
        // console.log('api', process.env.VUE_APP_BASE_API)
        // this.getPublicKey()
        // window.addEventListener('storage', this.afterQRScan)
    },
    mounted() {
        // if (this.loginForm.username === '') {
        //   this.$refs.username.focus()
        // } else if (this.loginForm.password === '') {
        //   this.$refs.password.focus()
        // }
        this.getImgCode();
    },
    destroyed() {
        // window.removeEventListener('storage', this.afterQRScan)
    },
    methods: {
        getPublicKey() {
            userApi.getPublicKey().then((res) => {
                this.publicKey = res.data;
                if (this.publicKey) {
                    this.handleLogin();
                }
            });
        },
        getImgCode() {
            userApi.getImgCode().then((res) => {
                // console.log('getImgCode')
                const { data, headers } = res;
                const blob = new Blob([data], {
                    type: headers["content-type"],
                });
                this.imgStr = window.URL.createObjectURL(blob);
                this.imgKey = res.headers.image;
            });
        },
        checkCapslock(e) {
            const { key } = e;
            this.capsTooltip =
                key && key.length === 1 && key >= "A" && key <= "Z";
        },
        showPwd() {
            if (this.passwordType === "password") {
                this.passwordType = "";
            } else {
                this.passwordType = "password";
            }
            this.$nextTick(() => {
                this.$refs.password.focus();
            });
        },
        handleLogin() {
            this.$refs.loginForm.validate((valid) => {
                if (valid) {
                    this.loading = true;
                    const encryptStr = new JSEncrypt();
                    encryptStr.setPublicKey(this.publicKey);
                    const params = {
                        username: this.loginForm.username.trim(),
                        password: encryptStr.encrypt(
                            this.loginForm.password.toString()
                        ),
                        imgKey: this.imgKey,
                        imgValue: this.loginForm.loginImageCode.toLowerCase(),
                    };
                    this.$store
                        .dispatch("user/login", params)
                        .then(() => {
                            this.$router.push({
                                path: "/",
                                query: this.otherQuery,
                            });
                            this.loading = false;
                        })
                        .catch(() => {
                            this.getImgCode();
                            this.loading = false;
                        });
                } else {
                    return false;
                }
            });
        },
        getOtherQuery(query) {
            return Object.keys(query).reduce((acc, cur) => {
                if (cur !== "redirect") {
                    acc[cur] = query[cur];
                }
                return acc;
            }, {});
        },
    },
};
</script>

<style lang="scss" scoped>
.title-container {
    position: relative;
    padding-top: 160px;
    .title {
        font-size: 26px;
        color: black;
        margin: 0px auto 40px auto;
        text-align: center;
        font-weight: bold;
    }
}

.login-container {
    min-height: 100%;
    width: 100%;
    height: 100vh;
    // background-color: white;
    // background-image
    overflow: hidden;
    // background: url("../../assets/login/Sign_bg.svg") ;
    // background-size:100%;
    background: #278dfe;
    .login_content {
        margin: 190px auto 0;
        width: 480px;
        height: 513px;

        // background: #FFFFFF;
        box-shadow: 0px 0px 20px rgba(0, 37, 140, 0.12);
        // border-top-left-radius: 120px;
        // border-bottom-left-radius: 10px;
        // border-top-right-radius: 10px;
        // border-bottom-right-radius: 120px;
        // .left_content {
        //   background: url("../../assets/login/Sign_left_img.svg");
        //   height: 100%;
        //   width: 42.5%;
        //   float: left;
        // }
        .right_content {
            // float: left;
            height: 100%;
            width: 100%;
            float: left;
            background-color: white;
            border-radius: 8px;
            .login-form {
                position: relative;
                // max-width: 100%;
                padding: 30px 56px 0;
                margin: 0 auto;
                overflow: hidden;
                .imageCode {
                    display: flex;
                    justify-content: space-between;
                }

                .sendSMSbutton {
                    height: 40px;
                    width: 112px;
                    margin-left: 5px;
                }
                .toRegisterBtn {
                    text-align: left;
                    margin: 0;
                }
            }
        }
    }
    /deep/ .el-button {
        height: 48px;
    }

    /deep/ .el-input__inner:focus {
        // //设置阴影属性
        // box-shadow: 0px 0px 12px 0px rgba(98, 19, 254, 0.6);
        // //设置border属性
        // border: 2px solid #5813fe;

        border: 1px solid #2474ff;
        box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.15);
        border-radius: 4px;
    }
    .tips {
        font-size: 14px;
        color: #fff;
        margin-bottom: 10px;

        span {
            &:first-of-type {
                margin-right: 16px;
            }
        }
    }
}
/deep/ .el-input__inner {
    height: 48px;
}

.left_company_name {
    color: rgba(255, 255, 255, 0.55);
    margin-top: 62px;
    margin-left: 47px;
    font-family: "Source Han Sans CN";
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 20px;
}

.welcome {
    font-family: "Source Han Sans CN";
    font-style: normal;
    font-weight: 700;
    font-size: 26px;
    line-height: 40px;
    /* identical to box height, or 62% */
    color: #278dfe;
    text-align: center;
}
.en_name {
    font-family: "Source Han Sans CN";
    font-style: normal;
    font-weight: 400;
    font-size: 20px;
    line-height: 24px;
    /* or 120% */
    margin-top: 30px;
    margin-left: 47px;
    color: rgba(255, 255, 255, 0.6);
}
</style>
