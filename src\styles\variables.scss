// base color
$blue:#324157;
$light-blue:#3A71A8;
$red:#C03639;
$pink: #E65D6E;
$green: #30B08F;
$tiffany: #4AB7BD;
$yellow:#FEC171;
$panGreen: #30B08F;

// sidebar
$menuText:rgba(0, 0, 0, 0.9);
$menuActiveText:rgba(36, 116, 255, 0.9);
$subMenuActiveText:rgba(36, 116, 255, 0.9); // https://github.com/ElemeFE/element/issues/12951

$menuBg:#FFFFFF;
$menuHover: rgba(199, 227, 255, 0.4);

$subMenuBg: #FAFAFA;

$subMenuHover:rgba(199, 227, 255, 0.4);

$sideBarWidth: 226px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}
