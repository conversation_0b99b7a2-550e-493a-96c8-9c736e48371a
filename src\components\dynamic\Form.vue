<!--
	<AUTHOR>
	@date 2022-09-18
	@desc 动态表单 - 新增、编辑、查看弹窗等
	写法↓
	<dynamic-form
		:data="数据"
		@handleClickFormBtn='@handleClickFormBtn'
	></dynamic-form>
-->
<template>
  <el-form ref="dialogForm" label-width="120px" :rules="formRule" :model="formData">
    <el-form-item
      v-for="(item, index) in formItem"
      :key="index"
      :label="item.label"
      :label-width="item.labelWidth ? item.labelWidth : null"
      :prop="item.model"
    >
      <!-- 文本输入框 - 普通文字、数字等 -->
      <el-input
        v-if="item.type == 'text'"
        v-model="formData[item.model]"
        type="text"
        :maxlength="item.maxlength ? Number(item.maxlength) : null"
        size="small"
        :placeholder="item.placeholder ? item.placeholder : '请输入'"
        clearable
        :readonly="item.readonly ? item.readonly : false"
        :disabled="item.disabled ? item.disabled : false"
      />

      <!-- 文本输入框 - 多行文本 -->
      <el-input
        v-if="item.type == 'textarea'"
        v-model="formData[item.model]"
        type="textarea"
        :maxlength="item.maxlength ? Number(item.maxlength) : null"
        size="small"
        :placeholder="item.placeholder ? item.placeholder : '请输入'"
        :show-word-limit="item.showWordLimit ? item.showWordLimit : false"
        clearable
        :readonly="item.readonly ? item.readonly : false"
        :disabled="item.disabled ? item.disabled : false"
      />

      <!-- 文本框 - 关键词过滤 -->
      <el-autocomplete
        v-if="item.type == 'textFilter'"
        v-model="formData[item.model]"
        size="small"
        :value-key="item.filterLabel"
        :fetch-suggestions="(queryString, cb) => querySearchAsync(queryString, cb, item)"
        :placeholder="item.placeholder ? item.placeholder : '请输入并选择'"
        clearable
        :readonly="item.readonly ? item.readonly : false"
        :disabled="item.disabled ? item.disabled : false"
        @select="obj => handleTextSelect(obj, item)"
        @change="value => handleTextChange(value, item)"
      />

      <!-- 下拉框 - 支持选择过滤 -->
      <el-select
        v-if="item.type == 'select'"
        v-model="formData[item.model]"
        size="small"
        :placeholder="item.placeholder ? item.placeholder : '请选择'"
        :filterable="item.filterable ? item.filterable : null"
        clearable
        :readonly="item.readonly ? item.readonly : false"
        :disabled="item.disabled ? item.disabled : false"
      >
        <!-- 下拉数据 - list -->
        <template v-if="item.optionType && item.optionType == 'list'">
          <el-option
            v-for="o in item.optionList"
            :key="o[item.optionValue]"
            :label="o[item.optionLabel]"
            :value="o[item.optionValue]"
          />
        </template>
        <!-- 默认 map传参 -->
        <template v-else>
          <el-option
            v-for="k in Object.keys(item.optionList)"
            :key="k"
            :label="item.optionList[k]"
            :value="k"
          />
        </template>
      </el-select>

      <!-- 时间区间 -->
      <el-date-picker
        v-if="item.type == 'date'"
        v-model="formData[item.model]"
        size="small"
        :range-separator="item.config.separator ? item.config.separator : null"
        :value-format="item.config.format ? item.config.format : null"
        :type="item.config.type"
        :placeholder="item.placeholder ? item.placeholder : '请选择'"
        :start-placeholder="item.config.startPlaceholder ? item.config.startPlaceholder : null"
        :end-placeholder="item.config.endPlaceholder ? item.config.endPlaceholder : null"
        :picker-options="item.config.pickerOption ? item.config.pickerOption : null"
        :readonly="item.readonly ? item.readonly : false"
        :disabled="item.disabled ? item.disabled : false"
      />
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  name: 'DynamicForm',
  mixins: [],
  props: {
    // 字段配置
    formItem: {
      type: Array,
      default: () => []
    },
    // 反显数据
    formData: {
      type: Object,
      default: () => {}
    },
    // 校验规则
    formRule: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  mounted() {},
  methods: {
    /**
		 * 过滤输入词
		 * @param {Object} queryString 输入词
		 * @param {Object} cb 回调方法
		 * @param {Object} item 相关配置
		 */
    querySearchAsync(queryString, cb, item) {
      const list = item.filterList
      // 存在输入词 -> 过滤列表
      const results = queryString ? list.filter(this.createStateFilter(queryString, item)) : list

      cb(results)
    },
    createStateFilter(queryString, item) {
      return state => {
        return state[item.filterLabel].indexOf(queryString) > -1
      }
    },

    /**
		 * 文本过滤选中
		 * @param {Object} sel   选中对象
		 * @param {Object} item  相关配置
		 */
    handleTextSelect: function(sel, item) {
      if (item.modelKey) {
        this.formData[item.modelKey] = sel[item.filterValue]
      }
    },
    // 兼容关键词改变和删除
    handleTextChange: function(value, item) {
      this.formData[item.modelKey] = ''
    }
  }
}
</script>
<style lang="scss" scoped></style>
