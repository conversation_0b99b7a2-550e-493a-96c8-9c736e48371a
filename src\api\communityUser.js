import request from '@/utils/request'

export function getUserList(data) {
  return request({
    url: '/communityUser/searchUserList',
    method: 'post',
    data
  })
}

export function saveUserMaterials(data) {
  return request({
    url: '/communityUser/saveMaterials',
    method: 'post',
    data
  })
}

export function banUser(data) {
  return request({
    url: '/communityUser/banUser',
    method: 'post',
    data
  })
}

export function getUserDict(data) {
  return request({
    url: '/communityUser/getUserDict',
    method: 'post',
    data
  })
}

export function searchVirtualUserList(data) {
  return request({
    url: '/communityUser/virtualUser/searchUserList',
    method: 'post',
    data
  })
}

export function saveVirtualUser(data) {
  return request({
    url: '/communityUser/virtualUser/save',
    method: 'post',
    data
  })
}

export function deleteVirtualUser(data) {
  return request({
    url: '/communityUser/virtualUser/delete',
    method: 'post',
    data
  })
}

export function enableDisableVirtualUser(data) {
  return request({
    url: '/communityUser/virtualUser/enableDisable',
    method: 'post',
    data
  })
}

// 社区用户导出
export function exportUser(data) {
  return request({
    url: '/communityUser/exportUser',
    method: 'post',
    data
  })
}

// 马甲号发长文
export function addLongPost(data) {
  return request({
    url: '/post/addLongPost',
    method: 'post',
    data
  })
}

// 编辑马甲号
export function editLongPost(data) {
  return request({
    url: '/post/editLongPost',
    method: 'post',
    data
  })
}

export function getApply(data) {
  return request({
    url: '/post/getApply',
    method: 'post',
    data
  })
}

// 所有标识

export function getUserMarks(data) {
  return request({
    url: '/sys/image/getUserMarks',
    method: 'post',
    data
  })
}

// 修改用户标识
export function editUserMark(data) {
  return request({
    url: '/communityUser/editUserMark',
    method: 'post',
    data
  })
}

// 查看用户详细信息接口
export function getUserDetail(params) {
  return request({
    url: '/communityUser/getUserDetail',
    method: 'get',
    params
  })
}

// 注销审核处理接口
export function doCancel(data) {
  return request({
    url: '/communityUser/cancel/doCancel',
    method: 'post',
    data
  })
}

export default {
  doCancel,
  getUserList,
  banUser,
  getUserDict,
  searchVirtualUserList,
  saveVirtualUser,
  deleteVirtualUser,
  enableDisableVirtualUser,
  exportUser,
  addLongPost,
  getApply,
  getUserMarks,
  editUserMark,
  editLongPost
}
