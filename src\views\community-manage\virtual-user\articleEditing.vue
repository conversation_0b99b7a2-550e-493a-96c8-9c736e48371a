<template>
  <div>
    <el-dialog
      v-if="dialogVisible"
      title="长文编辑"
      :visible.sync="dialogVisible"
      width="80%"
      top="4vh"
      :before-close="() => (dialogVisible = false)"
    >
      <el-form ref="eidtFrom" :model="ruleForm" :rules="rules" label-width="80px">
        <el-form-item prop="title" label="编辑">
          <el-input v-model="ruleForm.title" maxlength="15" placeholder="这里输入标题最多15个字" size="small" />
        </el-form-item>
        <el-form-item required prop="content">
          <div style="border: 1px solid #ccc;">
            <Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :default-config="toolbarConfig" />
            <!-- 编辑器 -->
            <Editor
              v-model="ruleForm.content"
              style="height: 400px; overflow-y: hidden"
              :default-config="editorConfig"
              @onCreated="onCreated"
            />
          </div>
        </el-form-item>
        <el-form-item label="封面图" prop="fileIds">
          <div class="upload-box">
            <div v-if="ruleForm.fileIds" class="upload">
              <img :src="imageUrl" class="el-upload-list__item-thumbnail" style="object-fit: cover;">
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview">
                  <i class="el-icon-zoom-in" />
                </span>
                <span class="el-upload-list__item-delete" @click="handleRemove">
                  <i class="el-icon-delete" />
                </span>
              </span>
            </div>
            <el-upload
              v-else
              :class="['avatar-uploader', { uplaodto: dialogtype === 'view' }]"
              action="#"
              :http-request="uploadImage"
              :before-upload="
                file => {
                  return beforeUpload(file);
                }
              "
              accept=".jpg,.png,.jpeg,.bmp"
              :file-list="fileList"
              :show-file-list="false"
              :limit="1"
              :on-change="handleChange"
            >
              <img v-if="ruleForm.fileIds" :src="imageUrl" class="avatar" style="object-fit: cover;">
              <i v-else class="el-icon-plus avatar-uploader-icon" />
            </el-upload>
            <div slot="tip" class="el-upload__tip">
              支持：jpg、jpeg、bmp、png的格式。附件大小不超过2M。
            </div>
          </div>
        </el-form-item>

        <el-form-item label="关联话题" prop="topicId" required>
          <el-select
            v-model="ruleForm.topicId"
            :disabled="dialogtype === 'update'"
            placeholder="请选择话题类型"
            style="width:240px"
          >
            <el-option v-for="item in topicList" :key="item.id" :label="item.topicName" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="关联商品">
          <div>
            <el-button type="primary" @click="handchang('goods', ruleForm.goodsList)">选择商品</el-button>
            <div style="padding: 10px;">
              <el-table
                style="width: 50%;"
                :data="ruleForm.goodsList"
                fit
                border
                highlight-current-row
                size="mini"
                :header-cell-style="{ background: '#F9F9F9' }"
              >
                <el-table-column prop="goodsName" label="商品名称" />
                <el-table-column prop="goodsCostPrice" label="商品售价" />
              </el-table>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="关联应用">
          <div>
            <el-button type="primary" @click="handchang('app', ruleForm.appList)">选择应用</el-button>
            <div class="app-box" style="padding: 10px;">
              <div v-for="item in ruleForm.appList" :key="item.id" class="app-item">
                <img :src="item.iconUrl" alt="">
                <span>{{ item.showName }}</span>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="() => (dialogVisible = false)">取消</el-button>
        <el-button type="primary" :loading="isloading" @click="confirmChang">发布</el-button>
      </div>
    </el-dialog>

    <el-dialog title="图片预览" :visible.sync="imgView" :modal-append-to-body="false">
      <img width="100%" :src="imageUrl" alt="">
    </el-dialog>

    <Transferdialog ref="transferdialog" @currentChang="currentChang" />
  </div>
</template>
<script>
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import '@wangeditor/editor/dist/css/style.css' // 引入 css
import upload_mixin from '@/mixin/upload_mixin'
import Transferdialog from './transferdialog'
import community from '@/api/community'
import { addLongPost, editLongPost } from '@/api/communityUser'

const editorConfig = {
  placeholder: '文字最多可输入2000字',
  MENU_CONF: {
    uploadImage: {
      async customUpload(file, insertFn) {
        const formData = new FormData()
        formData.append('file', file)
        community.uploadFile(formData).then(res => {
          insertFn(res.data.url)
        })
      }
    },
    uploadVideo: {
      async customUpload(file, insertFn) {
        const formData = new FormData()
        formData.append('file', file)
        community.uploadFile(formData).then(res => {
          insertFn(res.data.url)
        })
      }

    }
  }
}

const defaultForm = {
  title: '',
  content: '',
  fileIds: '',
  resourcesType: 2,
  topicId: '',
  goodsList: [],
  appList: [],
  userId: ''
}
export default {
  components: { Editor, Toolbar, Transferdialog },
  mixins: [upload_mixin],
  data() {
    const validateHtml = (rule, value, callback) => {
      if (value === '' || value === '<p><br></p>') {
        callback(new Error('请输入正文'))
      } else if (this.editor.getText().length > 2000) {
        callback(new Error('正文最多可输入2000字'))
      } else {
        callback()
      }
    }
    return {
      dialogVisible: false,
      editor: null,
      toolbarConfig: {
        //  toolbarKeys: [ 'color']
        excludeKeys: ['group-video']
      },
      editorConfig: editorConfig,
      mode: 'default', // or 'simple'
      ruleForm: JSON.parse(JSON.stringify(defaultForm)),
      // 在mixin中合并
      // imgView: false,
      // fileList: [],
      // imageUrl: '',
      // coverImgId:'',
      isloading: false,
      dialogtype: '',
      topicList: [],
      rules: {
        content: [{ validator: validateHtml, trigger: 'change' }],
        topicId: [{ required: true, message: '请选择话题', trigger: 'change' }],
        fileIds: [
          {
            validator: (rule, value, callback) => {
              if (this.ruleForm.fileIds && this.ruleForm.fileIds !== '') {
                callback()
              } else {
                callback(new Error('请上传封面图文'))
              }
            }, trigger: 'blur'
          }
        ]
      }
    }
  },
  watch: {
    imageUrl: {
      handler(val) {
        this.ruleForm.fileIds = this.coverImgId
      },
      immediate: true,
      deep: true

    }
  },
  mounted() {
    community.searchOpTopicListByStatus({ status: '2,3' }).then(res => {
      this.topicList = res.data
    })
  },
  beforeDestroy() {
    if (this.editor === null) return
    this.editor.destroy() // 组件销毁时，及时销毁 editor ，重要！！！
  },
  methods: {
    showDialo(row, type) {
      this.dialogtype = type
      this.dialogVisible = true
      if (this.dialogtype === 'update') {
        this.$nextTick(() => {
          this.$refs.eidtFrom.clearValidate()
          this.ruleForm = JSON.parse(JSON.stringify(row))
          this.ruleForm.postId = row.id
          this.ruleForm.fileIds = row.photoUrl[0].id
          this.imageUrl = row.photoUrl.length > 0 ? row.photoUrl[0].url : ''
          this.fileList = [{ url: row.photoUrl.length > 0 ? row.photoUrl[0].url : '' }]
          this.coverImgId = row.photoUrl.length > 0 ? row.photoUrl[0].id : ''
        })
      } else {
        this.$nextTick(() => {
          this.$refs.eidtFrom.clearValidate()
          this.ruleForm = JSON.parse(JSON.stringify(defaultForm))
          this.ruleForm.userId = row.id
        })
      }
    },
    onCreated(editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
      if (this.ruleForm.content) {
        this.editor.setHtml(this.ruleForm.content)
      }
    },
    handchang(type, list) {
      this.$refs.transferdialog.showDialo(type, list)
    },
    currentChang(data) {
      const { type, list } = data
      if (type === 'goods') {
        this.ruleForm.goodsList = list
      } else {
        this.ruleForm.appList = list
      }
    },
    confirmChang() {
      let strList = []
      if (this.ruleForm.appList) {
        strList = this.ruleForm.appList.map((item) => {
          return item.applyId
        })
      }

      this.$set(this.ruleForm, 'appIdList', strList.join())
      this.$refs.eidtFrom.validate(valid => {
        if (valid) {
          this.isloading = true
          if (this.dialogtype === 'update') {
            editLongPost(this.ruleForm).then(res => {
              this.dialogVisible = false
              this.isloading = false
              this.$emit('saveDialog')
            }).catch(() => {
              this.isloading = false
            })
          } else {
            addLongPost(this.ruleForm).then(res => {
              this.dialogVisible = false
              this.isloading = false
            }).catch(() => {
              this.isloading = false
            })
          }
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  line-height: 110px;
  text-align: center;
}

::v-deep .avatar {
  width: 120px;
  height: 120px;
  display: block;
  object-fit: cover;
}

::v-deep .el-upload__tip {
  color: #999;
  margin-top: -9px;
}

.upload-box {
  width: 100%;
}

.upload {
  overflow: hidden;
  background-color: #fff;
  border: 1px solid #d6dcdf;
  border-radius: 6px;
  box-sizing: border-box;
  width: 120px;
  height: 120px;
  margin-bottom: 8px;
  display: inline-block;
  color: #606266;
  line-height: 110px;
  position: relative;

  .el-upload-list__item-thumbnail {
    width: 100%;
    height: 100%;
  }

  .el-upload-list__item-actions {
    position: absolute;
    width: 100%;
    height: 100%;
    line-height: 120px;
    left: 0;
    top: 0;
    cursor: default;
    text-align: center;
    color: #fff;
    opacity: 0;
    font-size: 20px;
    background-color: rgba(0, 0, 0, 0.5);
    transition: opacity 0.3s;

    span {
      display: none;
      cursor: pointer;
    }

    span+span {
      margin-left: 15px;
    }

    .el-upload-list__item-delete {
      right: 10px;
      top: 0;
      display: none;
      position: static;
      font-size: inherit;
      color: inherit;
    }

    ::after {
      display: inline-block;
      content: "";
      height: 100%;
      vertical-align: middle;
    }

    &:hover {
      opacity: 1;

      span {
        display: inline-block;
      }
    }
  }
}

.app-box {
  display: flex;
  flex-wrap: wrap;

  .app-item {
    display: flex;
    padding: 10px;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    img {
      width: 120px;
      height: 120px;
      object-fit: cover;
    }

    span {
      font-size: 13px;
      width: 120px;
      text-align: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

.dialog-footer {
  text-align: center;
}

::v-deep .w-e-text-placeholder {
  top: 8px
}
</style>
