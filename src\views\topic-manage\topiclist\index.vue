<template>
  <div>
    <RouteTitle />
    <div class="main-body">
      <div class="main-body-bottom">
        <div class="body-top-form">
          <el-form ref="searchForm" class="left" :model="mainObj.searchForm" label-position="left" inline>
            <el-form-item label="状态：">
              <el-select v-model="mainObj.searchForm.status" placeholder="请选择" clearable>
                <el-option v-for="item in statusEnumObj" :key="item.code" :label="item.text" :value="item.code" />
              </el-select>
            </el-form-item>
            <el-form-item label="话题名称：">
              <el-input v-model="mainObj.searchForm.topicName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="更新人：">
              <el-input v-model="mainObj.searchForm.userName" placeholder="请输入" clearable />
            </el-form-item>
            <!-- <el-form-item label="选择日期：">
              <el-date-picker
                v-model="mainObj.searchForm.dateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时刻"
                value-format="yyyy-MM-dd HH:mm:ss"
                end-placeholder="结束时刻"
                :clearable="false"
                :default-time="['00:00:00', '23:59:59']"
              />
            </el-form-item> -->

            <el-form-item>
              <el-button type="primary" @click="onSearch">查询</el-button>
              <el-button @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
          <el-button class="right" type="primary" @click="operation('add')">新增</el-button>
        </div>
        <div class="table">
          <el-table
            ref="tableRef"
            :data="mainObj.list"
            fit
            border
            highlight-current-row
            size="small"
            :header-cell-style="{ background: '#F9F9F9' }"
          >
            <el-table-column prop="ipAddress" label="序号" width="50">
              <template slot-scope="scope">
                <span>{{ transTime(scope.$index) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="id" label="话题ID" show-overflow-tooltip width="120" />
            <el-table-column prop="status" label="状态" width="100" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>
                  <!-- {{ scope.row.status === '0' ? "未上架" : "已上架" }} -->
                  <div v-if="scope.row.status === '0'">
                    {{ "待上架" }}
                  </div>
                  <div v-else-if="scope.row.status === '2'">
                    {{ "上架开展中" }}
                  </div>
                  <div v-else-if="scope.row.status === '3'">
                    {{ "上架未开展" }}
                  </div>
                  <div v-else-if="scope.row.status === '5'">
                    {{ "已下架" }}
                  </div>
                  <div v-else>
                    {{ "" }}
                  </div>
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="topicName" label="话题名称" width="150" show-overflow-tooltip />
            <el-table-column prop="isTop" label="是否置顶">
              <template slot-scope="scope">
                <span>
                  <!-- <span>{{ scope.row.isTop }}</span> -->
                  <div v-if="scope.row.isTop === '1'">
                    {{ "是" }}
                  </div>
                  <div v-else-if="scope.row.isTop === '0'">
                    {{ "否" }}
                  </div>
                  <div v-else>
                    {{ "" }}
                  </div>
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="classificationType" label="话题分类">
              <template slot-scope="scope">
                <span>{{
                  classificationType(scope.row.classificationType)
                }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="topicType" label="话题属性">
              <template slot-scope="scope">
                <!-- <span>{{
                  scope.row.topicType === "0" ? "普通话题" : "PK话题"
                }}</span> -->
                <span v-if="scope.row.topicType ==='0'">
                  普通话题
                </span>
                <span v-else-if="scope.row.topicType === '1'">
                  PK话题
                </span>
                <span v-else-if="scope.row.topicType === '2'">
                  晒图话题
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="beginTime" label="话题开展周期" width="200">
              <template slot-scope="scope">
                <span>{{ scope.row.beginTime + "~" + scope.row.endTime }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="positiveSide" label="正方观点" show-overflow-tooltip width="150" />
            <el-table-column prop="positiveNum" label="正方票数" show-overflow-tooltip width="75" />
            <el-table-column prop="negativeSide" label="反方观点" show-overflow-tooltip width="150" />
            <el-table-column prop="negativeNum" label="反方票数" show-overflow-tooltip width="75" />
            <!-- <el-table-column prop="coverImgId" label="话题封面" width="150">
              <template slot-scope="scope">
                <img :src="scope.row.coverImgId" alt="" height="35px">
              </template>
            </el-table-column> -->
            <el-table-column prop="virtualViews" label="虚拟浏览量" width="100" />
            <el-table-column prop="likeNum" label="点赞数" />
            <el-table-column prop="commentNum" label="评论数" />
            <el-table-column prop="viewNum" label="实际浏览量" width="100" />
            <el-table-column prop="hotValue" label="热度值" />
            <el-table-column prop="hotRank" label="热度排名" />
            <el-table-column prop="userName" label="更新人" width="100" />
            <el-table-column prop="updateTime" label="更新时间" width="150" />
            <el-table-column label="操作" fixed="right" width="200">
              <template slot-scope="scope">
                <el-button type="text" @click="operation('status', scope.row)">
                  <!-- {{ scope.row.status === "0" ? "上架" : "下架" }} -->
                  <div v-if="scope.row.status === '2' || scope.row.status === '3'">
                    {{ "下架" }}
                  </div>
                  <div v-else-if="scope.row.status === '5'">
                    {{ "上架" }}
                  </div>
                  <div v-else>
                    {{ "" }}
                  </div>
                </el-button>
                <el-button v-if="scope.row.topicType ==='2' " type="text" @click="ranking(scope.row)">排行榜</el-button>
                <el-button v-if="scope.row.topicType ==='2' " type="text" @click="prize(scope.row)">奖品配置</el-button>
                <el-button type="text" @click="operation('edit', scope.row)">编辑</el-button>
                <!-- <el-button
                  type="text"
                  @click="operation('del', scope.row)"
                >删除</el-button> -->
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination
            :current-page="mainObj.currentPage"
            :page-size="mainObj.pageSize"
            :page-sizes="mainObj.pageSizes"
            :total="mainObj.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <detail ref="detail" :type="type" :row="row" @addlist="onSearch" />

    <Ranking ref="ranking" @success="fetchData" />

    <Prize ref="prize" />
  </div>
</template>

<script>
import sysManageApi from '@/api/sysManageApi'
import community from '@/api/community'
import checkPermission from '@/utils/permission'
import RouteTitle from '@/components/RouteTitle'
import detail from './detail'
import Ranking from './ranking.vue'
import Prize from './prize.vue'

const defaultSearchForm = {
  status: '',
  userName: '',
  topicName: ''
}

const defaultUserForm = {
  userName: '',
  mobile: '',
  roleIdList: []
}
export default {
  components: {
    RouteTitle,
    detail,
    Ranking,
    Prize
  },
  data() {
    return {
      row: {},
      type: '',
      // token: getToken(),
      details: checkPermission(['topic_detail']),

      infoId: '',
      dialogTableVisible: false,
      classificationTypeObj: [],
      typeEnums: [],
      statusEnumObj: [],
      mainObj: {
        list: [],
        pageSize: 10,
        pageSizes: [10, 30, 50],
        currentPage: 1,
        total: 0,
        searchForm: { ...defaultSearchForm }
      },
      userDialogObj: {
        visible: false,
        title: '',
        type: '',
        form: Object.assign({}, defaultUserForm)
      },
      value: 1
    }
  },
  computed: {
    classificationType() {
      return (type) => {
        const list = this.classificationTypeObj.filter(item => {
          return item.dictCode === type
        })
        return list[0] && list[0].dictName ? list[0].dictName : ''
      }
    },
    transTime() {
      return (index) => {
        return (this.mainObj.currentPage - 1) * this.mainObj.list.length + index + 1
      }
    }
  },
  created() {
    this.getTopicStatusEnum()
    this.getClassificationList()
    this.onSearch()
  },
  methods: {
    checkPermission,
    getTopicStatusEnum() {
      sysManageApi.getTypeEnum({ enumName: 'TopicStatusEnum' }).then(res => {
        this.statusEnumObj = res.data
      })
    },
    getClassificationList() {
      community.getClassificationList().then(res => {
        this.classificationTypeObj = res.data
      })
    },
    onSearch() {
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    onReset() {
      this.mainObj.searchForm = { ...defaultSearchForm }
      this.fetchData()
    },
    handleSizeChange(val) {
      this.mainObj.pageSize = val
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.mainObj.currentPage = val
      this.fetchData()
    },
    getDetailInfo(row) {
      this.infoId = row.id.toString()
      this.dialogTableVisible = true
      if (this.$refs.child) {
        this.$refs.child.updatedInfo(row.id)
      }
    },
    fetchData() {
      const params = {
        status: this.mainObj.searchForm.status,
        topicName: this.mainObj.searchForm.topicName,
        userName: this.mainObj.searchForm.userName,
        pageNum: this.mainObj.currentPage,
        pageSize: this.mainObj.pageSize
      }
      community.getTopicList(params).then((res) => {
        this.mainObj.list = res.data.list
        this.mainObj.total = Number(res.data.total)
      })
    },
    operation(type, row = {}) {
      this.type = type
      this.row = row
      if (type === 'add' || type === 'view' || type === 'edit') {
        this.$nextTick(() => {
          this.$refs.detail.dialogVisible = true
          this.$refs.detail.getdetail()
        })
      }

      if (type === 'status') {
        this.$confirm(
          `确定要将此话题${row.status === '2' || row.status === '3' ? '下架' : '上架'}吗？`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(() => {
            const obj = {
              status: row.status,
              id: row.id
            }
            community.changeStatus(obj).then(res => {
              this.handleSizeChange()
              this.$message({
                type: 'success',
                message: obj.status === '2' || obj.status === '3' ? '下架成功！' : '上架成功！'
              })
            })
          })
          .catch(() => { })
      }

      if (type === 'del') {
        this.$confirm(`确定要删除此话题吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            const obj = {
              id: row.id
            }
            community.topicDelete(obj).then(res => {
              this.handleSizeChange()
              this.$message({
                type: 'success',
                message: res.data
              })
            })
          })
          .catch(() => { })
      }
    },
    // 排行榜
    ranking(row = {}) {
      this.$refs.ranking.showDialo(row)
    },
    // 奖品配置
    prize(row = {}) {
      this.$refs.prize.showDialo(row)
    }
  }
}
</script>

<style lang="scss" scoped>
.main-body-bottom {
  box-sizing: border-box;
  padding: 20px;
}

.right {
  float: right;
  margin-bottom: 20px;
}

.left {
  float: left;
}

.body-top-form {
  overflow: hidden;
}
</style>
