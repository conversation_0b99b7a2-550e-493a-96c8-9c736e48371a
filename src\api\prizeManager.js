import request from '@/utils/request'

// 奖品分页查询
export function searchPrize(data) {
  return request({
    url: '/communityPrize/search',
    method: 'post',
    data
  })
}

// 奖品新增
export function prizeAdd(data) {
  return request({
    url: '/communityPrize/add',
    method: 'post',
    data
  })
}

// 奖品编辑
export function prizeEdit(data) {
  return request({
    url: '/communityPrize/edit',
    method: 'post',
    data
  })
}

// 删除奖品
export function prizeDelete(data) {
  return request({
    url: '/communityPrize/delete',
    method: 'post',
    data
  })
}

// 奖品类型查询
export function prizeAwardType(data) {
  return request({
    url: '/topic/awardType',
    method: 'post',
    data
  })
}

// 关联话题
export function prizedTopicType(data) {
  return request({
    url: '/topic/topicType',
    method: 'post',
    data
  })
}

// 话题奖品发放
export function communityPrizeAward(data) {
  return request({
    url: '/communityPrize/award',
    method: 'post',
    data
  })
}

// 话题奖品配置
export function topicPrizeConfig(data) {
  return request({
    url: '/communityPrize/topicPrizeConfig',
    method: 'post',
    data
  })
}
// 话题奖品配置详情
export function searchTopicConfig(data) {
  return request({
    url: '/topic/searchTopicConfig',
    method: 'post',
    data
  })
}

// 中奖信息导出
export function exportWinPrize(data) {
  return request({
    url: '/topic/exportPrize',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

