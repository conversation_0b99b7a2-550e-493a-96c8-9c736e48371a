# 菜单修改接口更新说明

## 修改概述

根据新的接口规范，已更新菜单修改相关的代码，包括接口地址和参数格式。

## 新接口规范

### 接口地址
```
/activity-manager-api/sys/menu/modifyMenu
```

### 请求方式
```
POST
```

### 请求参数
| 参数名 | 类型 | 描述 | 是否必填 | 说明 |
|--------|------|------|----------|------|
| oldMenuId | string | 菜单id | 是 | 修改前的前端地址 |
| newMenuId | string | 前端地址 | 是 | 修改后的前端地址 |
| menuName | string | 菜单名称 | 是 |  |
| pid | string | 上级菜单id | 是 |  |
| sort | int | 排序 | 是 |  |
| permIcon | string | 图标 | 是 |  |
| permDesc | string | 描述 | 是 |  |
| serverPermPath | string | 后台权限地址 | 是 |  |
| permType | string | 类型 | 是 | CATALOG-目录<br>MENU-菜单<br>FUNCTION-按钮 |

### 响应参数
```
无特定返回数据
```

## 修改的文件

### 1. API接口文件
- **文件**: `src/api/menuApi.js`
- **修改内容**: 更新 `modifyMenu` 函数的接口地址

```javascript
// 修改菜单
export function modifyMenu(data) {
  return request({
    url: '/activity-manager-api/sys/menu/modifyMenu',
    method: 'post',
    data
  })
}
```

### 2. 系统管理API文件
- **文件**: `src/api/sysManageApi.js`
- **修改内容**: 更新 `addOrModifyMenu` 函数，区分新增和修改操作

```javascript
// 修改菜单
export function addOrModifyMenu(data, url) {
  if (url === 'modifyMenu') {
    // 使用新的修改菜单接口
    return request({
      url: '/activity-manager-api/sys/menu/modifyMenu',
      method: 'post',
      data
    })
  } else {
    // 新增菜单使用原接口
    return request({
      url: `/sys/menu/${url}`,
      method: 'post',
      data
    })
  }
}
```

### 3. 菜单管理主页面
- **文件**: `src/views/system-setting/menu-manage/index.vue`
- **修改内容**: 
  - 更新修改菜单的数据构造逻辑
  - 添加权限类型转换函数

```javascript
// 提交
handleSubmit() {
  this.$refs.menuFormRef.validate((valid) => {
    if (valid) {
      this.isloading = true
      
      if (this.dialogObj.type === 'add') {
        // 新增菜单逻辑保持不变
        menuApi.addMenu(this.dialogObj.form).then((res) => {
          // 处理成功响应
        })
      } else {
        // 修改菜单 - 根据新的接口参数要求构造数据
        const modifyData = {
          oldMenuId: this.dialogObj.form.menuId,
          newMenuId: this.dialogObj.form.frontendUrl || this.dialogObj.form.menuId,
          menuName: this.dialogObj.form.menuName,
          pid: this.dialogObj.form.parentId || 'root',
          sort: parseInt(this.dialogObj.form.sort) || 1,
          permIcon: this.dialogObj.form.icon || '',
          permDesc: this.dialogObj.form.description || '',
          serverPermPath: this.dialogObj.form.backendUrl || this.dialogObj.form.frontendUrl || '',
          permType: this.getPermTypeValue(this.dialogObj.form.type)
        }
        
        menuApi.modifyMenu(modifyData).then((res) => {
          // 处理成功响应
        })
      }
    }
  })
}

// 获取权限类型值
getPermTypeValue(type) {
  const typeMap = {
    'directory': 'CATALOG',
    'menu': 'MENU',
    'button': 'FUNCTION'
  }
  return typeMap[type] || 'MENU'
}
```

### 4. 菜单新增/修改组件
- **文件**: `src/views/system-setting/components/menu-add-or-update.vue`
- **修改内容**: 更新表单提交逻辑，区分新增和修改的数据格式

```javascript
// 表单提交
dataFormSubmit() {
  this.$refs['dataForm'].validate(valid => {
    if (valid) {
      const url = this.pageType === 'add' ? 'addMenu' : 'modifyMenu'
      let data = {}
      
      if (this.pageType === 'add') {
        // 新增菜单
        data = {
          ...this.dataForm,
          menuId: this.dataForm.path
        }
      } else {
        // 修改菜单 - 根据新的接口参数要求构造数据
        data = {
          oldMenuId: this.dataForm.menuId,
          newMenuId: this.dataForm.path || this.dataForm.menuId,
          menuName: this.dataForm.menuName,
          pid: this.dataForm.pid || 'root',
          sort: parseInt(this.dataForm.sort) || 1,
          permIcon: '',
          permDesc: '',
          serverPermPath: this.dataForm.path || '',
          permType: 'MENU'
        }
      }
      
      addOrModifyMenu(data, url).then(res => {
        // 处理成功响应
      })
    }
  })
}
```

## 主要变更点

### 1. 接口地址变更
- 原地址: `/sys/menu/modifyMenu`
- 新地址: `/activity-manager-api/sys/menu/modifyMenu`

### 2. 参数格式变更
修改菜单时需要传递的参数从原来的表单数据格式改为新的规范格式：

**原参数格式**:
```javascript
{
  menuId: "menu001",
  menuName: "菜单名称",
  parentId: "parent001",
  // ... 其他表单字段
}
```

**新参数格式**:
```javascript
{
  oldMenuId: "menu001",        // 修改前的前端地址
  newMenuId: "menu001_new",    // 修改后的前端地址
  menuName: "菜单名称",
  pid: "parent001",            // 上级菜单id
  sort: 1,                     // 排序
  permIcon: "icon-name",       // 图标
  permDesc: "描述信息",         // 描述
  serverPermPath: "/path",     // 后台权限地址
  permType: "MENU"             // 类型：CATALOG/MENU/FUNCTION
}
```

### 3. 权限类型映射
添加了前端表单类型到后端接口类型的映射：
- `directory` → `CATALOG`
- `menu` → `MENU`
- `button` → `FUNCTION`

## 注意事项

1. **向后兼容**: 新增菜单的接口和参数格式保持不变，只修改了修改菜单的部分
2. **数据验证**: 确保所有必填参数都有默认值或验证逻辑
3. **错误处理**: 保持原有的错误处理机制
4. **类型转换**: 注意数字类型的转换（如 sort 字段）

## 测试建议

1. 测试新增菜单功能是否正常
2. 测试修改菜单功能是否按新接口规范工作
3. 验证权限类型映射是否正确
4. 检查必填参数是否都有适当的默认值
