<template>
  <div>
    <el-dialog
      :visible.sync="dialogVisible"
      top="4vh"
      width="1260px"
      :title="config.title"
      :before-close="handleClose"
    >
      <table v-loading="tableLoading" border="1" cellpadding="10" cellspacing="0" class="detail-table">
        <tbody v-for="(val, key, i) in tableData" :key="i">
          <template v-if="key != 'verifyTime'">
            <tr v-if="key != 'interestInfo'" class="label" style="backgroundColor:#f5f5f5;">
              <td>{{ zh[key] }}</td>
              <td />
            </tr>
            <tr v-for="(v, k, j) in tableData[key]" :key="j">
              <template v-if="zh[k]">
                <td class="label">{{ typeof zh[k] === 'string' ? zh[k] : zh[k].label }}</td>
                <td class="content">
                  <template v-if="config.isEdit && isSpecialKey(k)">
                    <el-select v-if="editMap[k].options" v-model="formData[k].value" placeholder="请选择" style="flex:1;">
                      <el-option v-for="opt in getOptions(k)" :key="opt.value" :label="opt.label" :value="opt.value" :disabled="opt.label == '计划购车'" />
                    </el-select>
                    <el-input v-else v-model="formData[k].value" placeholder="请输入内容" style="flex:1;" :maxlength="45" />
                    <div class="cailiao">
                      <span class="mrrr">佐证材料</span>
                      <div v-for="file in formData[k].fileVos" :key="file.uid" class="mrrr preview" @click="handlePreview(file, k)">
                        <i v-if="isFilePDF(file.url)" class="el-icon-document" style="font-size: 30px;" />
                        <img v-else class="img" :src="file.url">
                      </div>
                      <div class="preview mrrr" @click="showUploadDialog(k)">
                        <i class="el-icon-plus" />
                      </div>

                      <span v-if="getVerifyTime(k)" class="mrrr">认证时间：{{ getVerifyTime(k) }}</span>
                      <span v-if="getArtificialInfo(k) && getArtificialInfo(k).artificialVerifyTime" class="mrrr">变更时间：{{ getArtificialInfo(k).artificialVerifyTime }}</span>
                      <span v-if="getArtificialInfo(k) && getArtificialInfo(k).artificialVerifyUser">编辑人：{{ getArtificialInfo(k).artificialVerifyUser }}</span>
                    </div>

                  </template>
                  <template v-else>
                    <span v-if="getVerifyTime(k)">认证时间：{{ getVerifyTime(k) }}</span>
                    <div class="content-v">{{ typeof zh[k] === 'string' ? v :zh[k].options[v] }}</div>
                  </template>
                </td>
              </template>
            </tr>
          </template>
        </tbody>
      </table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">返 回</el-button>
        <el-button v-if="config.isEdit" type="primary" @click="handleSave">保 存</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :visible.sync="showUpload"
      :title="previewTitle"
    >
      <el-upload
        action="#"
        list-type="picture-card"
        :auto-upload="false"
        :on-change="onChangeFile"
        :on-exceed="handleExceed"
        accept=".jpg,.png,.jpeg,.pdf"
        :file-list="fileList"
        :limit="3"
      >
        <i slot="default" class="el-icon-plus" />
        <div slot="file" slot-scope="{ file }">
          <i v-if="isFilePDF(file.url) || (file.raw && file.raw.type == 'application/pdf')" class="el-icon-document big" />
          <img v-else class="el-upload-list__item-thumbnail" :src="file.url">
          <span class="el-upload-list__item-actions">
            <span class="el-upload-list__item-preview" @click="handlePreview(file)">
              <i class="el-icon-zoom-in" />
            </span>
            <span class="el-upload-list__item-delete" @click="handleRemove(file)">
              <i class="el-icon-delete" />
            </span>
          </span>
        </div>
      </el-upload>
      <div>支持的文件格式：pdf、png、jpg、jpeg，2M以内</div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showUpload=false">取 消</el-button>
        <el-button type="primary" @click="uploadFiles">保 存</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :visible.sync="showPreview"
      :title="previewTitle"
    >
      <div v-if="isFilePDF(previewUrl)" id="previewPdf" v-loading="loading" />
      <img v-else :src="previewUrl">
    </el-dialog>
  </div>
</template>

<script>
import zh from './infoZh.json'
import { getUserDetail, saveUserMaterials } from '@/api/communityUser'
import { uploadBatchFiles } from '@/api/community'
import jsPreViewPdf from '@js-preview/pdf'

export default {
  name: 'SignUpDialog',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    config: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + '/activity-manager-api/file/upload/file',
      tableData: {},
      showUpload: false,
      uploadKey: 'carStatus',
      fileList: [],
      formData: {
        carStatus: {
          value: null,
          fileVos: []
        },
        marrierStatus: {
          value: null,
          fileVos: []
        },
        workUnit: {
          value: null,
          fileVos: []
        },
        educationStatus: {
          value: null,
          fileVos: []
        }
      },
      zh: zh,
      tableLoading: false,
      editMap: {
        carStatus: {
          options: true,
          materialType: '2', // 保存信息时需要
          infoKey: 'carArtificialInfo',
          verifyTKey: 'carVerifyTime'
        },
        marrierStatus: {
          options: true,
          materialType: '1',
          infoKey: 'marrierArtificialInfo',
          verifyTKey: 'marrierVerifyTime'
        },
        workUnit: {
          options: false,
          materialType: '4',
          infoKey: 'workUnitArtificialInfo',
          verifyTKey: 'workUnitVerifyTime'
        },
        educationStatus: {
          options: true,
          materialType: '3',
          infoKey: 'eduArtificialInfo',
          verifyTKey: 'workUnitVerifyTime'
        }
      },
      showPreview: false,
      previewTitle: '',
      previewUrl: '',
      loading: true
    }
  },
  created() {
    this.getUserDetailInfo(this.config.userId)
  },
  methods: {
    getUserDetailInfo(userId) {
      this.tableLoading = true
      getUserDetail({ userId: userId }).then(res => {
        this.tableData = res.data
        if (this.config.isEdit) {
          this.initFormData(res.data.identityInfo)
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    handleCancel() {
      this.$emit('cancel')
    },
    handleConfirm() {
      this.$emit('confirm')
    },
    handleClose() {
      this.$emit('handleClose')
    },
    isSpecialKey(key) {
      return ['carStatus', 'marrierStatus', 'workUnit', 'educationStatus'].indexOf(key) >= 0
    },
    getOptions(k) {
      var newOpts = []
      const options = zh[k].options
      for (let i = 0; i < options.length; i++) {
        const item = options[i]
        if (item && item.length > 0) {
          newOpts.push({
            value: i,
            label: item
          })
        }
      }
      return newOpts
    },
    isFilePDF(url) {
      const index = url.lastIndexOf('.')
      const ext = url.substr(index + 1)
      return ext == 'pdf'
    },
    initFormData(identityInfo) {
      Object.keys(this.editMap).forEach(key => {
        const mapInfo = this.editMap[key]
        const kValue = identityInfo[key]
        const kMoreInfo = identityInfo[mapInfo.infoKey]
        var temp = {
          value: null,
          fileVos: []
        }
        if (kValue) {
          temp.value = mapInfo.options ? Number(kValue) : kValue
        }
        if (kMoreInfo && kMoreInfo.verifyUrl && kMoreInfo.verifyUrl.length > 0) {
          kMoreInfo.verifyUrl.forEach(item => {
            temp.fileVos.push({
              url: item
            })
          })
        }
        this.$set(this.formData, key, temp)
      })
    },
    getVerifyTime(key) {
      if (!this.editMap[key]) return null
      const k = this.editMap[key].verifyTKey
      if (k && this.tableData.verifyTime && this.tableData.verifyTime[k]) {
        return this.tableData.verifyTime[k]
      }
      return null
    },
    getArtificialInfo(key) {
      if (!this.editMap[key]) return null
      const k = this.editMap[key].infoKey
      if (k && this.tableData.identityInfo && this.tableData.identityInfo[k]) {
        return this.tableData.identityInfo[k]
      }
      return null
    },
    showUploadDialog(key) {
      this.uploadKey = key
      this.previewTitle = `${typeof zh[key] === 'string' ? zh[key] : zh[key].label}  佐证材料`
      this.fileList = [...this.formData[key].fileVos]
      this.showUpload = true
    },
    handleExceed(files, fileList) {
      this.$message.error('佐证材料最多能上传3个！')
    },
    onChangeFile(file, fileList) {
      if (['image/png', 'image/jpg', 'image/jpeg', 'application/pdf'].indexOf(file.raw.type) < 0) {
        this.$message.error('支持的文件格式：pdf、png、jpg、jpeg，2M以内')
        fileList.pop()
      } else if (file.size > 2 * 1024 * 1024) {
        this.$message.error('支持的文件格式：pdf、png、jpg、jpeg，2M以内')
        fileList.pop()
      }
      this.fileList = fileList
    },
    uploadFiles() {
      var formData = new FormData()
      this.fileList.forEach(item => {
        if (item.raw) {
          formData.append('files', item.raw)
        }
      })

      uploadBatchFiles(formData)
        .then(res => {
          if (res.code === 200) {
            const data = JSON.parse(JSON.stringify(res.data))
            const key = this.uploadKey
            var temp = []
            // 老数据
            temp.push(...this.fileList.filter(item => {
              return !item.raw
            }))
            // 添加新数据
            temp.push(...data.map(item => {
              return {
                url: item.url,
                name: item.name,
                uid: item.id,
                upload: true
              }
            }))
            this.formData[key].fileVos = temp
            this.showUpload = false
          } else {
            this.$message.error('上传失败')
          }
        })
        .finally(() => {
        })
    },
    handleRemove(file) {
      this.fileList.splice(
        this.fileList.findIndex(item => {
          return file.url === item.url || item.uid === file.uid
        }),
        1
      )
    },
    handlePreview(file, k = this.uploadKey) {
      this.previewTitle = `${typeof zh[k] === 'string' ? zh[k] : zh[k].label}  佐证材料`
      this.previewUrl = file.url
      this.showPreview = true
      if (!this.isFilePDF(file.url)) return

      this.$nextTick(() => {
        const previewer = jsPreViewPdf.init(document.getElementById('previewPdf'))
        previewer
          .preview(file.url)
          .then((res) => {
            this.loading = false
          })
          .catch((e) => {
            this.loading = false
          })
      })
    },
    checkValid() {
      const keys = Object.keys(this.formData)
      var verifyUrlReq = []
      for (const key of keys) {
        const formInfo = this.formData[key]
        const originV = this.tableData.identityInfo[key]

        // 获取本次上传的佐证材料
        var verifyUrl = []
        formInfo.fileVos.forEach(file => {
          // 存在upload代表为本次上传的材料
          if (file.upload) {
            verifyUrl.push(file.url)
          }
        })

        if (verifyUrl.length === 0) {
          // 内容有变更，必须上传佐证
          if (originV != formInfo.value) {
            this.$message.error('本次修改未上传必须的佐证材料')
            return false
          }
        } else {
          verifyUrlReq.push({
            materialType: this.editMap[key].materialType,
            modifyValue: formInfo.value,
            verifyUrl
          })
        }
      }
      return verifyUrlReq
    },
    handleSave() {
      // 校验是否修改？修改是否上传佐证材料
      var verifyUrlReq = this.checkValid()

      if (verifyUrlReq === false) return

      if (verifyUrlReq.length === 0) {
        this.$message.error('本次未修改信息')
        return
      }
      var params = {
        userId: this.config.userId,
        verifyUrlReq
      }
      saveUserMaterials(params).then(res => {
        this.$message.success('信息保存成功')
        this.handleClose()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-table {
  width: 1200px;
  min-height: 500px;
  margin: 0 auto;
  border-color: rgba(204, 204, 204, 0.2);
  .label {
    width: 100px;
    text-align: center;
    font-weight: 700;
  }
  .content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-v {
      min-height: 16px;
    }

    .company {
      width: 25%;
    }
  }

  .cailiao {
    display: flex;
    flex: 3;
    align-items: center;
    padding-left: 20px;

    .preview {
      width: 40px;
      height: 40px;
      border: 1px solid #c0ccda;
      border-radius: 6px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .img {
      width: 100%;
      height: 100%;
    }
  }

  .mrrr {
    margin-right: 8px;
  }
}

.el-dialog__body {
  text-align: center;
}

.big {
  font-size: 120px;
  margin: 10px;
}

::v-deep {
  .el-upload--picture-card {
    margin-bottom: 10px;
  }
}
</style>
