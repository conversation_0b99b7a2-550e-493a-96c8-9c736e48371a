<template>
  <div>
    <el-dialog
      :visible.sync="dialogVisible"
      top="4vh"
      width="1260px"
      :title="config.title"
      :before-close="handleClose"
    >
      <table v-loading="tableLoading" border="1" cellpadding="10" cellspacing="0" class="detail-table">
        <tbody v-for="(item, i) in zh" :key="i">
          <tr style="backgroundColor:#f5f5f5;">
            <td class="label">{{ item.title }}</td>
            <td class="content" />
          </tr>
          <tr v-for="(citem, ci) in item.children" :key="`${i}-${ci}`">
            <td class="label">{{ citem.title }}</td>
            <td class="content">{{ getOptionsValue(citem) }}</td>
          </tr>
        </tbody>
      </table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">返 回</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import zh from './info-parent-zh.json'
import { getUserDetail } from '@/api/communityUser'

export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    config: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableData: {},
      tableLoading: false,
      zh: zh
    }
  },
  created() {
    this.getUserDetailInfo(this.config.userId)
  },
  methods: {
    getUserDetailInfo(userId) {
      this.tableLoading = true
      getUserDetail({ userId: userId }).then(res => {
        this.tableData = res.data
      }).finally(() => {
        this.tableLoading = false
      })
    },
    handleClose() {
      this.$emit('handleClose')
    },
    getOptionsValue(item) {
      if (!this.tableData || !this.tableData[item.group]) return null

      let temp = null
      if (item.keys && item.keyBind) {
        const transArray = []
        item.keys.forEach(key => {
          const vv = this.tableData[item.group][key]
          if (vv) {
            transArray.push(vv)
          }
        })
        temp = transArray.join(item.keyBind)
      } else {
        temp = this.tableData[item.group][item.key]
        if (item.options && temp) {
          temp = item.options[Number(temp)]
        }
      }
      return temp
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-table {
  width: 1200px;
  min-height: 500px;
  margin: 0 auto;
  border-color: rgba(204, 204, 204, 0.2);
  .label {
    width: 120px;
    text-align: center;
    font-weight: 700;
  }
  .content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 38px;
  }
}

.el-dialog__body {
  text-align: center;
}

</style>
