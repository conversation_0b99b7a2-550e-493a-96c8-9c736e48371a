<template>
  <div>
    <el-dialog
      :visible.sync="dialogVisible"
      top="4vh"
      width="820px"
      :title="config.title"
      :before-close="handleClose"
    >
      <div style="margin-bottom: 10px;">申请编号：{{ config.auditId }}</div>
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column label="序号">
          {{ config.index + 1 }}
        </el-table-column>
        <el-table-column prop="" label="审核内容">
          <el-image
            style="width: 100px; height: 100px"
            :src="config.auditData.auditContent"
            :preview-src-list="[config.auditData.auditContent]"
          />
        </el-table-column>
        <el-table-column label="请求时间">
          {{ config.auditData.createTime }}
        </el-table-column>
        <el-table-column prop="robotId" label="机审流水ID" />
        <el-table-column prop="robotStatus" label="检测状态">
          <template slot-scope="scope">
            {{ robotList[scope.row.robotStatus] }}
          </template>
        </el-table-column>
        <el-table-column prop="auditDetail" label="审核结果说明">
          <template slot-scope="scope">
            <span :style="{color:(config.auditData.auditStatus == 3 ? 'red':'')}">{{ statusMap[config.auditData.auditStatus] }}</span>
            {{ scope.row.auditDetail }}
          </template>
        </el-table-column>
        <el-table-column prop="auditTime" label="审核时间" />
        <el-table-column label="审核者">
          {{ config.auditData.auditUser }}
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getRecord } from '@/api/hobbyAndPic'
export default {
  name: 'SignUpDialog',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    config: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableData: [],
      statusMap: {
        1: '待审核',
        2: '审核通过',
        3: '审核不通过'
      },
      robotList: {
        pass: '通过',
        reject: '拒绝',
        force_manual: '可疑'
      }
    }
  },
  created() {
    this.formData = {
      ...this.config.formData
    }
    this.queryRecordList()
  },
  methods: {
    queryRecordList() {
      const params = {
        id: this.config.auditId
      }
      getRecord(params).then(res => {
        this.tableData = res.data
      })
    },
    handleCancel() {
      this.$emit('cancel')
    },
    handleConfirm() {
      this.$emit('confirm')
    },
    handleClose() {
      this.$emit('handleClose')
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-table {
  width: 740px;
  margin: 0 auto;
  border-color: rgba(204, 204, 204, 0.2);
  .label {
    width: 100px;
    text-align: center;
    font-weight: 700;
  }
}
</style>
