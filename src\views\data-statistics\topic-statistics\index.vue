<template>
  <div>
    <RouteTitle />
    <div class="main-body">
      <div class="main-body-bottom">
        <el-form inline label-position="left">
          <el-form-item label="话题选择：">
            <el-select v-model="value1" multiple :multiple-limit="10" placeholder="请选择" style="width:300px;" @change="getSelectedTopics">
              <el-option
                v-for="item in topicList"
                :key="item.id"
                :label="item.topicName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="日期：">
            <el-date-picker
              v-model="time"
              type="daterange"
              :picker-options="setDateRange"
              range-separator="至"
              value-format="yyyy-MM-dd"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              start-placeholder="开始日期"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="content">
          <div class="header">
            话题浏览次数（曝光）
            <p style="font-size: 16px;color: rgb(171 168 168);">
              该话题浏览数据，统计截止至昨日24时
            </p>
          </div>
          <div style="width:100%">
            <ul class="tabs">
              <li :class="topicView === 0 ? 'col' : ''" @click="changeTab('topicView', 0)">新增次数</li>
              <li :class="topicView === 1 ? 'col' : ''" @click="changeTab('topicView', 1)">累积次数</li>
              <div class="line" :style="'transform: translateX(' + 100 * topicView + 'px);'" />
            </ul>
            <div id="chartColumn" ref="chartColumn" key="chartColumn" style="width:100%;height:400px;margin-bottom:20px;" />
          </div>
          <div class="header">
            参与话题发布数
          </div>
          <div style="width:100%">
            <ul class="tabs">
              <li :class="topicJoin === 0 ? 'col' : ''" @click="changeTab('topicJoin', 0)">新增发布数</li>
              <li :class="topicJoin === 1 ? 'col' : ''" @click="changeTab('topicJoin', 1)">累计发布数</li>
              <div class="line" :style="'transform: translateX(' + 100 * topicJoin + 'px);'" />
            </ul>
            <div id="chartColumn1" ref="chartColumn1" key="chartColumn1" style="width:100%;height:400px;margin-bottom:20px;" />
          </div>
          <div class="header">
            参与话题用户数
          </div>
          <div style="width:100%">
            <ul class="tabs">
              <li :class="topicJoinUser === 0 ? 'col' : ''" @click="changeTab('topicJoinUser', 0)">新增用户数</li>
              <li :class="topicJoinUser === 1 ? 'col' : ''" @click="changeTab('topicJoinUser', 1)">累积用户数</li>
              <div class="line" :style="'transform: translateX(' + 100 * topicJoinUser + 'px);'" />
            </ul>
            <div id="chartColumn2" ref="chartColumn2" key="chartColumn2" style="width:100%;height:400px;margin-bottom:20px;" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import RouteTitle from '@/components/RouteTitle'
import echarts from 'echarts'
import { searchTopicStatisticsList, searchTopicReleaseStatisticsList, searchTopicUserStatisticsList, searchOpTopicList } from '@/api/community'
export default {
  name: 'UserStatistics',
  components: {
    RouteTitle
  },
  data() {
    return {
      topicList: [],
      topicView: 0,
      topicJoin: 0,
      topicJoinUser: 0,
      value1: '',
      time: [],
      setDateRange: {
        disabledDate: time => {
          // 禁用今天之后的日期【当前天可选】
          return time.getTime() > Date.now()
        }
      },
      options: [],
      option: {
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: []
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: []
        },
        yAxis: {
          type: 'value'
        },
        series: []
      },
      resetOption: {
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: []
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: []
        },
        yAxis: {
          type: 'value'
        },
        series: []
      },
      topicNames: []
    }
  },

  mounted() {
    this.time = [this.ShowDate(30), this.ShowDate(1)]
    this.searchOpTopicList()
  },

  methods: {
    getSelectedTopics(val) {
      this.topicNames = []
      this.topicList.map(item => {
        val.map(va => {
          if (item.id === va) {
            this.topicNames = [...this.topicNames, item.topicName]
          }
        })
      })
    },
    searchOpTopicList() {
      searchOpTopicList().then(res => {
        this.topicList = res.data
        this.value1 = [this.topicList[0].id]
        this.topicNames = [this.topicList[0].topicName]
        this.searchTopicStatisticsList('1', 'topicView')
        this.searchTopicStatisticsList('1', 'topicJoin')
        this.searchTopicStatisticsList('1', 'topicJoinUser')
      })
    },
    resetCharts() {
      echarts.init(this.$refs['chartColumn']).setOption(this.resetOption, true)
      echarts.init(this.$refs['chartColumn1']).setOption(this.resetOption, true)
      echarts.init(this.$refs['chartColumn2']).setOption(this.resetOption, true)
    },
    searchTopicStatisticsList(type, key) {
      const obj = {
        topicView: searchTopicStatisticsList,
        topicJoin: searchTopicReleaseStatisticsList,
        topicJoinUser: searchTopicUserStatisticsList
      }
      const obj1 = {
        topicView: 'chartColumn',
        topicJoin: 'chartColumn1',
        topicJoinUser: 'chartColumn2'
      }
      obj[key]({
        endDate: this.time[1],
        startDate: this.time[0],
        topicId: this.value1.join(','),
        type: type
      }).then(res => {
        const list = res.data || []
        let topicLi
        if (this.value1.length > 0 && list.length < this.value1.length) {
          topicLi = this.topicList.filter(item => {
            if (this.value1.includes(item.id)) return item
          })
          topicLi.forEach(item => {
            const isFlag = list.some(elitem => {
              if (elitem.topicId === item.id) {
                return item
              }
            })
            if (!isFlag) {
              list.push({
                topicId: item.id,
                topicName: item.topicName,
                result: []
              })
            }
          })
        }
        const dateList = this.formatEveryDay(this.time[0], this.time[1], '-')
        const option = JSON.parse(JSON.stringify(this.option))
        option.legend.data = this.topicNames
        option.xAxis.data = dateList
        option.series = []
        list.forEach((item, index) => {
          option.series.push({
            // name: this.topicNames[index],
            type: 'line',
            stack: 'Total' + index,
            data: []
          })
          dateList.forEach((dateItem, dateIndex) => {
            const flag = item.result.filter(item => item.statDate.substring(5) === dateItem)
            const max = Math.max(...item.result.map(item => item.count))
            if (flag.length > 0) {
              option.series[index].data.push(flag[0].count)
            } else {
              if (type === '2') {
                option.series[index].data.includes(max) ? option.series[index].data.push(max) : option.series[index].data.push(0)
              } else {
                option.series[index].data.push(0)
              }
            }
          })
        })
        option.series.forEach((item, index) => {
          console.log(index, this.topicNames[index])
          item.name = this.topicNames[index]
        })
        echarts.init(this.$refs[obj1[key]]).setOption(option, true)
      })
    },
    getDate(datestr) {
      const temp = datestr.split('-')
      const date = new Date(temp[0], temp[1] - 1, temp[2])
      return date
    },
    onReset() {
      this.value1 = ''
      this.time = [this.ShowDate(30), this.ShowDate(1)]
      this.resetCharts()
    },
    formatEveryDay(start, end, Spacer) {
      // const weekArr = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      const dateList = []
      const startTime = this.getDate(start)
      const endTime = this.getDate(end)

      while ((endTime.getTime() - startTime.getTime()) >= 0) {
        // const year = startTime.getFullYear()
        const month = startTime.getMonth() + 1 < 10 ? '0' + (startTime.getMonth() + 1) : startTime.getMonth() + 1
        const day = startTime.getDate().toString().length === 1 ? '0' + startTime.getDate() : startTime.getDate()
        // const timeDay = startTime.getDay(startTime)
        // dateList.push({
        //   time: year + '-' + month + '-' + day,
        //   week: weekArr[startTime.getDay(startTime)],
        //   isWeekend: timeDay === 0 || timeDay === 6
        // })
        if (Spacer) {
          // 如果不需要间隔符则直接拼接返回
          if (Spacer === 'null') {
            dateList.push(month + day) // 如20220422,20220423
          } else {
            // 需要自定义间隔符
            dateList.push(month + Spacer + day)
          }
        } else {
          dateList.push(month + '-' + day) // 2022-04-22,2022-04-23
        }
        startTime.setDate(startTime.getDate() + 1)
      }
      return dateList
    },
    changeTab(key, value) {
      if (this.value1.length === 0) {
        this.$message({
          message: '请选择话题',
          type: 'warning'
        })
        return false
      }
      if (!this.time) {
        this.$message({
          message: '请选择日期氛围',
          type: 'warning'
        })
        return false
      }
      this[key] = value
      if (key === 'topicView') {
        this.searchTopicStatisticsList(String(value + 1), key)
      }
      if (key === 'topicJoin') {
        this.searchTopicStatisticsList(String(value + 1), key)
      }
      if (key === 'topicJoinUser') {
        this.searchTopicStatisticsList(String(value + 1), key)
      }
    },

    onSearch() {
      if (this.value1.length === 0) {
        this.$message({
          message: '请选择话题',
          type: 'warning'
        })
        return false
      }
      if (!this.time) {
        this.$message({
          message: '请选择日期氛围',
          type: 'warning'
        })
        return false
      }
      this.topicView = 0
      this.topicJoin = 0
      this.topicJoinUser = 0
      this.searchTopicStatisticsList('1', 'topicView')
      this.searchTopicStatisticsList('1', 'topicJoin')
      this.searchTopicStatisticsList('1', 'topicJoinUser')
    },
    ShowDate(date) {
      var num = date
      const n = num
      const d = new Date()
      let year = d.getFullYear()
      let mon = d.getMonth() + 1
      let day = d.getDate()
      if (day <= n) {
        if (mon > 1) {
          mon = mon - 1
        } else {
          year = year - 1
          mon = 12
        }
      }
      d.setDate(d.getDate() - n)
      year = d.getFullYear()
      mon = d.getMonth() + 1
      day = d.getDate()
      const s = year + '-' + (mon < 10 ? ('0' + mon) : mon) + '-' + (day < 10 ? ('0' + day) : day)
      return s
    }
  }
}
</script>

<style lang="scss" scoped>
.main-body-bottom {
  box-sizing: border-box;
  padding: 20px;
  width: 100%;
}

.header{
  width:100%;
  box-sizing: border-box;
  margin-left:10px;
  position: relative;

  &::before{
    width:5px;
    height:18px;
    content: ' ';
    display: block;
    position: absolute;
    top:0;
    left:-13px;
    background:#ddd;
    border-radius: 2px;
  }
}

.content{
  margin-top:10px;
  width: 100%;
}

.tabs{
  display:flex;
  justify-content: flex-start;
  align-items: center;
  margin-left:135px;
  margin-top:25px;
  padding:0;
  position:relative;
  li {
    width:80px;
    list-style:none;
    font-size: 14px;
    position: relative;
    margin-right:20px;
    text-align: center;
    cursor: pointer;
  }
}

.col {
  color:#1890ff;
}
.line{
    width:80px;
    height:3px;
    background:#1890ff;
    position: absolute;
    bottom:-10px;
    left:0;
    border-radius: 4px;
    transition: transform .3s cubic-bezier(.645,.045,.355,1);
}
canvas {
  width: 100% !important;
}
</style>
