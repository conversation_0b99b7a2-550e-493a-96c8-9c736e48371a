<template>
  <div>
    <RouteTitle />
    <div class="main-body">
      <div class="main-body-top">
        <file
          :types="queryData.type"
          @uploadSuccess="getList"
        />
        <!-- 页面列表数据 + 分页条 -->
        <dynamic-table
          :config="tableConfig"
          :data="tableData"
          :is-loading.sync="tableLoading"
          @pagination="handlePageChange"
        >
          <!-- S 处理表格自定义插槽 -->
          <template #statusTxt="{rowData}">
            {{
              rowData.status == 0 ? "未生效" : rowData.status == 1 ? "生效" : ""
            }}
          </template>
          <template v-slot:operate="{ rowData, index }">
            <el-button
              type="text"
              @click.stop="handleDown(rowData)"
            >下载</el-button>
          </template>
        </dynamic-table>
      </div>
    </div>
  </div>
</template>

<script>
import RouteTitle from '@/components/RouteTitle'
import DynamicTable from '@/components/dynamic/Table.vue'
import file from './components/fileOperate.vue'
import mixin from '../mixin'
import { downLoad } from '@/api/hobbyAndPic'

export default {
  components: {
    RouteTitle,
    DynamicTable,
    file
  },
  mixins: [mixin],
  data() {
    return {
      queryUrl: '/hobby/query',
      // 列表字段
      tableConfig: {
        tableColumn: [
          {
            prop: '',
            label: '序号',
            isIndex: true
          },
          {
            prop: 'fileName',
            label: '上传文件名'
          },
          {
            prop: 'fileNum',
            label: '问题数量'
          },
          {
            prop: 'status',
            label: '生效状态',
            slotName: 'statusTxt'
          },
          {
            prop: 'createTime',
            label: '上传时间'
          },
          {
            prop: 'createUser',
            label: '操作者'
          },
          {
            prop: '',
            label: '操作',
            slotName: 'operate', // 操作 - 自定义操作，和operateList二选一
            fixed: 'right',
            minWidth: 160
          }
        ]
      },
      queryData: {
        type: 2
      },
      // 列表数据
      tableData: {
        list: [],
        // 分页数据
        pageTotal: 0, // 列表总数
        pageSize: 10, // 每页条数
        pageNum: 1 // 当前页码
      }
    }
  },
  methods: {
    handleDown(row) {
      const _this = this
      const params = {
        type: _this.queryData.type,
        fileId: row.id
      }
      downLoad(params).then(res => {
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = res.data
        link.setAttribute('download', `${row.fileName}1111.xlsx`)
        document.body.appendChild(link)
        link.click()
        URL.revokeObjectURL(link.href)
      })
    }
  }
}
</script>

<style></style>
