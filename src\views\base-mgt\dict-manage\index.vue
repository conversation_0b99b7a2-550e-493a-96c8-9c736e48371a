<template>
  <div>
    <RouteTitle />
    <div class="main-body">
      <div class="main-body-bottom">
        <div class="body-top-form">
          <el-form
            ref="searchForm"
            :model="mainObj.searchForm"
            label-width="75px"
            class="left"
            label-position="left"
            inline
          >
            <el-form-item label="字典类型">
              <el-select
                v-model="mainObj.searchForm.dictType"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in dictTypeObj"
                  :key="item.code"
                  :label="item.text"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="字典代码">
              <el-input
                v-model.trim="mainObj.searchForm.dictCode"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
            <el-form-item label="字典名称">
              <el-input
                v-model.trim="mainObj.searchForm.dictName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="onSearch"
              >查询</el-button>
              <el-button @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
          <div v-if="addPermission" class="right">
            <div class="main-body-bottom-btn-left">
              <el-button
                v-if="addPermission"
                type="primary"
                @click="onOperate('add')"
              >新增</el-button>
            </div>
          </div>
        </div>
        <div class="table">
          <el-table
            ref="tableRef"
            :data="mainObj.list"
            fit
            border
            highlight-current-row
            :header-cell-style="{'background': '#F9F9F9'}"
          >
            <el-table-column
              prop="dictType"
              label="字典类型"
            />
            <el-table-column
              prop="dictCode"
              label="字典代码"
            />
            <el-table-column
              prop="dictName"
              label="字典名称"
            />
            <el-table-column
              prop="dictRemark"
              label="备注信息"
            />
            <el-table-column
              prop="sort"
              label="排序"
            />
            <el-table-column
              prop="updateTime"
              label="更新时间"
            />
            <el-table-column label="操作" width="180">
              <template slot-scope="scope">
                <el-button
                  v-preventReClick
                  type="text"
                  @click="onOperate('update', scope.row)"
                >编辑</el-button>
                <el-button
                  type="text"
                  @click="onOperate('delete', scope.row)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination
            :current-page="mainObj.currentPage"
            :page-size="mainObj.pageSize"
            :page-sizes="mainObj.pageSizes"
            :total="mainObj.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <el-dialog
      :title="dictDialogObj.title"
      :visible.sync="dictDialogObj.visible"
      destroy-on-close
      width="600px"
      @close="dictDialogObj.visible = false"
    >
      <el-form
        ref="dictFormRef"
        :model="dictDialogObj.form"
        :rules="dictDialogObj.type === 'view' ? null : dictDialogObj.rules"
        :disabled="dictDialogObj.type === 'view'"
        label-width="80px"
        label-position="left"
        class="full-width"
      >
        <el-form-item label="字典类型" prop="dictType">
          <el-select
            v-model="dictDialogObj.form.dictType"
            placeholder="请选择"
            :disabled="dictDialogObj.type !=='add'"
          >
            <el-option
              v-for="item in dictTypeObj"
              :key="item.code"
              :label="item.text"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="字典代码" prop="dictCode">
          <el-input
            v-model="dictDialogObj.form.dictCode"
            maxlength="100"
            placeholder="请输入"
            clearable
            :disabled="dictDialogObj.type !=='add'"
          />
        </el-form-item>
        <el-form-item label="字典名称" prop="dictName">
          <el-input
            v-model="dictDialogObj.form.dictName"
            maxlength="100"
            placeholder="请输入"
            clearable
          />
        </el-form-item>
        <el-form-item label="备注信息" prop="dictRemark">
          <el-input
            v-model="dictDialogObj.form.dictRemark"
            maxlength="200"
            placeholder="请输入"
            clearable
          />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input
            v-model="dictDialogObj.form.sort"
            maxlength="5"
            placeholder="请输入"
            clearable
          />
        </el-form-item>
      </el-form>
      <div v-if="dictDialogObj.type !== 'view'" slot="footer">
        <el-button @click="dictDialogObj.visible = false">取消</el-button>
        <el-button
          v-preventReClick
          type="primary"
          :loading="isloading"
          @click="onOperate('submit')"
        >确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import sysManageApi from '@/api/sysManageApi'
import checkPermission from '@/utils/permission'
import RouteTitle from '@/components/RouteTitle'

const defaultSearchForm = {
  dictType: '',
  dictCode: '',
  dictName: ''
}

const defaultDictForm = {
  id: '',
  dictType: '',
  dictCode: '',
  dictName: '',
  dictRemark: '',
  sort: ''
}
export default {
  components: {
    RouteTitle
  },
  data() {
    const validateConfirmSort = (rule, value, callback) => {
      if (!value || (value && (/(^[1-9]\d*$)/.test(value)))) {
        callback()
      } else {
        callback(new Error('请输入正整数!'))
      }
    }
    return {
      isloading: false,
      addPermission: checkPermission(['glDict_add']),
      deletePermission: checkPermission(['glDict_delete']),
      updatePermission: checkPermission(['glDict_modify']),
      dictTypeObj: [],
      mainObj: {
        list: [],
        pageSize: 10,
        pageSizes: [10, 30, 50],
        currentPage: 1,
        total: 0,
        searchForm: { ...defaultSearchForm }
      },
      dictDialogObj: {
        visible: false,
        tile: '',
        type: '',
        form: Object.assign({}, defaultDictForm),
        rules: {
          dictType: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          dictCode: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          dictName: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          sort: [
            { trigger: 'blur', validator: validateConfirmSort }
          ]
        }
      }
    }
  },
  created() {
    this.onSearch()
    this.getTypeEnum()
  },
  methods: {
    checkPermission,

    getTypeEnum() {
      sysManageApi.getTypeEnum({ enumName: 'DictTypeEnum' }).then(res => {
        this.dictTypeObj = res.data
      })
    },
    onSearch() {
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    onReset() {
      this.mainObj.searchForm = { ...defaultSearchForm }
      this.fetchData()
    },
    handleSizeChange(val) {
      this.mainObj.pageSize = val
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.mainObj.currentPage = val
      this.fetchData()
    },
    fetchData() {
      const params = {
        dictCode: this.mainObj.searchForm.dictCode,
        dictType: this.mainObj.searchForm.dictType,
        dictName: this.mainObj.searchForm.dictName,
        pageNum: this.mainObj.currentPage,
        pageSize: this.mainObj.pageSize
      }
      sysManageApi.getDictList(params).then((res) => {
        this.mainObj.list = res.data.list
        this.mainObj.total = res.data.total
      })
    },
    onOperate(type, row) {
      if (type === 'add') {
        this.dictDialogObj.visible = true
        this.dictDialogObj.type = type
        this.dictDialogObj.title = '新增'
        this.dictDialogObj.form = Object.assign({}, defaultDictForm)
        this.$nextTick(() => {
          this.$refs.dictFormRef.clearValidate()
        })
      } else if (type === 'update') {
        this.dictDialogObj.visible = true
        this.dictDialogObj.type = type
        this.dictDialogObj.title = '修改'
        this.dictDialogObj.form = {
          id: row.id,
          dictType: row.dictType,
          dictCode: row.dictCode,
          dictName: row.dictName,
          dictRemark: row.dictRemark,
          sort: row.sort
        }
      } else if (type === 'view') {
        this.dictDialogObj.visible = true
        this.dictDialogObj.type = type
        this.dictDialogObj.title = '查看'
      } else if (type === 'submit') {
        this.$refs.dictFormRef.validate((valid) => {
          if (valid) {
            this.isloading = true
            const params = {
              dictType: this.dictDialogObj.form.dictType,
              dictCode: this.dictDialogObj.form.dictCode,
              dictName: this.dictDialogObj.form.dictName,
              dictRemark: this.dictDialogObj.form.dictRemark,
              sort: this.dictDialogObj.form.sort
            }
            if (this.dictDialogObj.type === 'update') {
              params.id = this.dictDialogObj.form.id
              sysManageApi.modifyDict(params).then(e => {
                this.fetchData()
                this.dictDialogObj.visible = false
                this.isloading = false
              })
            } else {
              sysManageApi.addDict(params).then(e => {
                this.fetchData()
                this.dictDialogObj.visible = false
                this.isloading = false
              })
            }
          }
        })
      } else if (type === 'delete') {
        this.$confirm('确定删除该字典参数吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const params = {
            dictId: row.id
          }
          sysManageApi.deleteDict(params).then((res) => {
            this.$message({
              type: 'success',
              message: res.msg
            })
            this.fetchData()
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.main-body-bottom {
  box-sizing: border-box;
  padding: 20px;
}

.right {
  float: right;
}

.left {
  float: left;
}

</style>

