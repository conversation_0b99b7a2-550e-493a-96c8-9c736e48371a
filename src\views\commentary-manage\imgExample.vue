<template>
  <div>
    <RouteTitle />
    <!-- 条件查询 -->
    <div class="main-body">
      <div class="main-body-top">
        <!-- 页面条件搜索 -->
        <dynamic-query
          :data="queryConfig"
          @queryChanged="handleQueryDataChanged"
          @search="handleSearch"
        />
        <!-- 页面列表数据 + 分页条 -->
        <dynamic-table
          :config="tableConfig"
          :data="tableData"
          :is-loading.sync="tableLoading"
          @pagination="handlePageChange"
        >
          <!-- S 处理表格自定义插槽 -->
          <template #auditContent="{ rowData, index }">
            <el-image
              style="width: 100px; height: 100px"
              :src="rowData.auditContent || ''"
              :preview-src-list="[rowData.auditContent]"
            />
          </template>
          <template #statusTxt="{ rowData, index }">
            {{ statusMap[rowData.auditStatus] }}
          </template>
          <template v-slot:operate="{ rowData, index }">
            <el-button
              type="text"
              @click="handleExample(rowData)"
            >人工审核</el-button>
            <el-button
              type="text"
              @click="auditRecords(rowData,index)"
            >审核记录</el-button>
          </template>
        </dynamic-table>
      </div>
    </div>
    <!-- 配置dialog -->
    <RecordDialog
      v-if="dialogVisible"
      :dialog-visible="dialogVisible"
      :config="dialogConfig"
      @handleClose="handleClose"
    />
    <!-- 审核dialog -->
    <el-dialog
      title="人工审核结果"
      center
      width="400px"
      :visible.sync="exampleVisible"
      class="example-dialog"
      @close="handleCloseExample"
    >
      <el-form
        ref="ruleForm"
        :model="exampleData"
        :rules="rules"
        class="demo-ruleForm"
      >
        <el-form-item label="" prop="auditStatus">
          <el-radio-group
            v-model="exampleData.auditStatus"
            class="example-radio"
          >
            <el-radio :label="2">审核通过</el-radio>
            <el-radio :label="3">审核拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="exampleData.auditStatus === 3"
          label=""
          prop="auditDetail"
        >
          <el-input
            v-model="exampleData.auditDetail"
            type="textarea"
            placeholder="请输入审核不通过的原因..."
          />
        </el-form-item>
      </el-form>

      <span slot="footer" style="text-algin:center" class="dialog-footer">
        <el-button @click="exampleVisible = false">取 消</el-button>
        <el-button type="primary" @click="exampleSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import RouteTitle from '@/components/RouteTitle'
import DynamicQuery from '@/components/dynamic/Query.vue'
import DynamicTable from '@/components/dynamic/Table.vue'
import RecordDialog from './components/RecordDialog.vue'
import { imgAudit } from '@/api/hobbyAndPic'
import mixin from '../mixin'
const statusMap = {
  1: '待审核',
  2: '审核通过',
  3: '审核不通过'
}
export default {
  components: {
    RouteTitle,
    DynamicQuery,
    DynamicTable,
    RecordDialog
  },
  mixins: [mixin],
  data() {
    return {
      queryUrl: '/pic/query',
      dialogVisible: false,
      dialogConfig: {},
      queryConfig: {
        queryItem: [
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '申请编号:',
            model: 'id',
            clearable: true, // 默认 true
            size: 'small' // 默认 small
          },
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '用户ID:',
            model: 'userId',
            clearable: true, // 默认 true
            size: 'small' // 默认 small
          },
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '机审流水ID:',
            model: 'robotId',
            clearable: true, // 默认 true
            size: 'small' // 默认 small
          },
          {
            type: 'date', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '审核时间:',
            model: 'exampleDateArr',
            config: {
              type: 'datetimerange', // 时间区间 - 返回数组 需业务重写字段
              separator: '-',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
              format: 'yyyy-MM-dd HH:mm:ss'
            }
          },
          {
            type: 'select',
            label: '审核状态',
            model: 'auditStatus',
            optionValue: 'value',
            optionLabel: 'label',
            optionList: statusMap
          },
          {
            type: 'option'
          },
          {
            type: 'option',
            slotName: 'handleExport'
          }
        ]
      },
      statusMap: statusMap,
      exampleDateArr: [],
      // 列表字段
      tableConfig: {
        tableColumn: [
          {
            prop: '',
            label: '序号',
            isIndex: true
          },
          {
            prop: 'id',
            label: '申请编号'
          },
          {
            prop: 'userId',
            label: '用户ID'
          },
          {
            prop: 'userName',
            label: '用户昵称'
          },
          {
            prop: 'auditContent',
            label: '审核内容',
            slotName: 'auditContent',
            width: 140
          },
          {
            prop: 'createTime',
            label: '请求时间'
          },
          {
            prop: 'auditStatus',
            label: '审核状态',
            slotName: 'statusTxt'
          },
          {
            prop: 'auditTime',
            label: '最近审核时间'
          },
          {
            prop: 'auditUser',
            label: '操作者'
          },
          {
            prop: '',
            label: '操作',
            slotName: 'operate', // 操作 - 自定义操作，和operateList二选一
            fixed: 'right',
            minWidth: 160
          }
        ]
      },

      // 列表数据
      tableData: {
        list: [],
        // 分页数据
        pageTotal: 0, // 列表总数
        pageSize: 10, // 每页条数
        pageNum: 1 // 当前页码
      },
      exampleVisible: false,
      exampleData: {
        id: '',
        auditDetail: '',
        auditStatus: 2
      },
      rules: {}
    }
  },
  methods: {
    auditRecords(rowData, index) {
      this.dialogVisible = true
      this.dialogConfig = {
        title: '审核记录',
        auditId: rowData.id,
        auditData: rowData,
        index
      }
    },
    // 人工审核
    handleExample(rowData) {
      const _this = this
      _this.exampleVisible = true
      _this.exampleData.id = rowData.id
    },
    handleCloseExample() {
      this.$refs.ruleForm.resetFields()
      this.exampleData.auditStatus = 2
      this.exampleData.auditDetail = ''
    },
    // 提交审核
    exampleSubmit() {
      const params = {
        ...this.exampleData
      }
      imgAudit(params).then(res => {
        this.exampleVisible = false
        this.$message.success(res.msg)
        this.getList()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.example-dialog {
  .example-radio {
    display: block;
    margin: 20px auto;
    text-align: center;
  }
  ::v-deep .el-dialog__footer {
    text-align: center;
  }
}
</style>
