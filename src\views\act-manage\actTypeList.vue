<template>
  <div>
    <RouteTitle />
    <!-- 条件查询 -->
    <div class="main-body">
      <div class="main-body-top">
        <!-- 页面条件搜索 -->
        <dynamic-query
          :data="queryConfig"
          @queryChanged="handleQueryDataChanged"
          @search="handleSearch"
        >
          <template #addBtn>
            <el-button
              type="primary"
              icon="el-icon-plus"
              @click="handleAddOrEdit('add')"
            >新增</el-button>
          </template>
        </dynamic-query>
        <!-- 页面列表数据 + 分页条 -->
        <dynamic-table
          :config="tableConfig"
          :data="tableData"
          :is-loading.sync="tableLoading"
          @pagination="handlePageChange"
        >
          <!-- S 处理表格自定义插槽 -->
          <template #actTypeStatusText="{ rowData, index }">
            {{ actTypeStatusMap[rowData.actTypeStatus] }}
          </template>
          <template v-slot:operate="{ rowData, index }">
            <el-button
              type="text"
              @click.stop="handleAddOrEdit('detail', rowData)"
            >详情</el-button>
            <el-button
              type="text"
              @click.stop="handleAddOrEdit('edit', rowData)"
            >修改</el-button>
            <el-button
              v-if="rowData.actTypeStatus == 0"
              type="text"
              style="color: #f56c6c"
              @click.stop="handleDelete(rowData)"
            >删除</el-button>
          </template>
        </dynamic-table>
      </div>
    </div>
    <!-- 配置dialog -->
    <ActivityTypeDialog
      v-if="dialogVisible"
      :dialog-visible.sync="dialogVisible"
      :config="dialogConfig"
      @handleClose="handleClose"
      @saveSuccess="saveSuccess"
    />
  </div>
</template>

<script>
import RouteTitle from '@/components/RouteTitle'
import DynamicQuery from '@/components/dynamic/Query.vue'
import DynamicTable from '@/components/dynamic/Table.vue'
import ActivityTypeDialog from './components/activityTypeDialog.vue'
import { deleteActivityType } from '@/api/actManage'

const actTypeStatusMap = {
  0: '关闭',
  1: '开启'
}

import mixin from '../mixin'
export default {
  components: {
    RouteTitle,
    DynamicQuery,
    DynamicTable,
    ActivityTypeDialog
  },
  mixins: [mixin],
  data() {
    return {
      queryUrl: '/act-manage/activityType/getActivityType',
      dialogVisible: false,
      dialogConfig: {},
      queryConfig: {
        queryItem: [
          {
            type: 'text',
            label: '类型ID:',
            model: 'typeId',
            clearable: true,
            size: 'small'
          },
          {
            type: 'text',
            label: '类型名称:',
            model: 'typeTitle',
            clearable: true,
            size: 'small'
          },
          {
            type: 'select',
            label: '状态:',
            model: 'actTypeStatus',
            optionValue: 'id',
            optionLabel: 'value',
            optionList: actTypeStatusMap
          },
          {
            type: 'date',
            label: '创建时间:',
            model: 'createTimeArr',
            config: {
              type: 'datetimerange',
              format: 'yyyy-MM-dd HH:mm:ss',
              valueFormat: 'yyyy-MM-ddTHH:mm:ss',
              separator: '至',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期'
            }
          },
          {
            type: 'option'
          },
          {
            type: 'option',
            slotName: 'addBtn'
          }
        ]
      },
      // 列表字段
      tableConfig: {
        tableColumn: [
          {
            prop: '',
            label: '序号',
            isIndex: true
          },
          {
            prop: 'typeId',
            label: '活动类型ID'
          },
          {
            prop: 'typeTitle',
            label: '活动类型名称',
            minWidth: 150
          },
          {
            prop: 'sort',
            label: '排序'
          },
          {
            prop: 'createTime',
            label: '创建时间',
            width: 160
          },
          {
            prop: 'updateTime',
            label: '更新时间',
            width: 160
          },
          {
            prop: 'updateId',
            label: '更新者'
          },
          {
            prop: 'actTypeStatus',
            label: '状态',
            slotName: 'actTypeStatusText',
            width: 100
          },
          {
            prop: '',
            label: '操作',
            slotName: 'operate',
            fixed: 'right',
            minWidth: 160
          }
        ]
      },

      // 列表数据
      tableData: {
        list: [],
        // 分页数据
        pageTotal: 0, // 列表总数
        pageSize: 10, // 每页条数
        pageNum: 1 // 当前页码
      },
      actTypeStatusMap: actTypeStatusMap
    }
  },
  methods: {
    handleAddOrEdit(type, rowData) {
      this.dialogVisible = true
      if (type === 'add') {
        this.dialogConfig = {
          title: '新增活动类型',
          type
        }
      } else if (type === 'edit') {
        this.dialogConfig = {
          title: '修改活动类型',
          formData: rowData,
          type
        }
      } else if (type === 'detail') {
        this.dialogConfig = {
          title: '活动类型详情',
          formData: rowData,
          type
        }
      }
    },
    // 删除活动类型
    handleDelete(rowData) {
      const msg = `确定对【${rowData.typeTitle}】进行删除操作吗？删除后对应活动将自动下架。`
      this.$confirm(msg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 逻辑删除，传递完整的数据并设置delFlag为1
          const deleteData = {
            ...rowData,
            delFlag: '1'
          }
          deleteActivityType(deleteData).then(res => {
            this.$message({
              type: 'success',
              message: res.msg || '删除成功'
            })
            this.getList()
          }).catch(error => {
            console.error('删除失败:', error)
            this.$message({
              type: 'error',
              message: error.msg || '删除失败，请重试'
            })
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }
  }
}
</script>

<style lang="scss" scoped>
// 解决操作列下方小横条问题
::v-deep .el-table__fixed-right {
  height: auto !important;
  bottom: 0 !important;
}

// 可选：调整表格边框样式
::v-deep .el-table {
  border-bottom: none;
}

// 添加状态列右侧边框，解决与固定操作列之间没有分隔线的问题
// 主表格状态列的右侧边框
::v-deep .el-table__body {
  tr td:last-child {
    border-right: 1px solid #ebeef5 !important;
  }
}

// 表头状态列的右侧边框
::v-deep .el-table__header {
  th:last-child {
    border-right: 1px solid #ebeef5 !important;
  }
}

// 确保固定列与主表格之间的分隔线
::v-deep .el-table__fixed-right {
  border-left: 1px solid #ebeef5 !important;
}
</style>
