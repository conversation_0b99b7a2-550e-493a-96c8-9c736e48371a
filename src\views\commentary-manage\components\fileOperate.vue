<template>
  <div class="file">
    <el-button type="text" @click="handleDown">下载表格模板</el-button>
    <img class="u115_img" src="@/assets/u115.svg">
    <el-upload
      ref="upload"
      class="upload-demo"
      list-type="picture-card"
      action="#"
      :on-change="handleSuccess"
	  :before-upload="beforeUpload"
      :file-list="fileList"
      :show-file-list="false"
      accept=".xlsx"
      :limit="1"
      :auto-upload="false"
    >
      <div v-if="!file.name" class="el-upload__text">
        <i class="el-icon-plus" />
        <span>选择文件</span>
      </div>
      <div v-else class="file-box" @click.stop="removeFile">
        <img class="u117_img" src="@/assets/u117.png" alt="">
        <div class="demo-upload-list-cover">
          <i class="el-icon-delete" />
        </div>
      </div>
    </el-upload>

    {{ file.name }}
    <el-button
      style="margin-left: 10px;"
      size="small"
      type="primary"
      :disabled="!file.name"
      @click="submitUpload"
    >提交上传</el-button>
  </div>
</template>

<script>
import { downTemplate, uploadInterest } from '@/api/hobbyAndPic'
import { downloadFile } from '@/utils/index'

export default {
  props: ['types'],
  data() {
    return {
      fileList: [],
      file: {}
    }
  },
  mounted() {
  },
  methods: {
	beforeUpload(file) {  
	  let regex = /(.xlsx)$/;
	  if (!regex.test(file.type)) {
		this.fileList = []
	  	this.$message.error('仅支持.xlsx格式的文件！');
	  	return false;
	  }
	  return true
	},
    submitUpload() {
      const _this = this
      const formData = new FormData()
      formData.append('file', _this.file.raw)
      formData.append('type', _this.types)
      uploadInterest(formData).then(res => {
        _this.$message.success('已成功上传')
        _this.file = {}
        this.$refs.upload.uploadFiles = []
        this.$emit('uploadSuccess')
      })
    },
    removeFile() {
      this.file = {}
      this.$refs.upload.uploadFiles = []
    },
    handleSuccess(file) {
		if (!file.name.toLowerCase().endsWith('.xlsx')) {
			this.fileList = []
			this.file = {}
			this.$message.error('仅支持.xlsx格式的文件！');
			return
		}
		this.file = file
    },
    // 下载表格模板
    handleDown() {
      downTemplate({ type: this.types }).then(res => {
        const name = this.types === 1 ? '兴趣标签库模板' : '互聊问题库模板'
        downloadFile(
          res.data,
          name,
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-demo {
  margin-right: 10px;
}
.file {
  height: 140px;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  .u115_img {
    width: 50px;
    height: 22px;
    margin: 0 50px;
  }

  ::v-deep .el-upload--picture-card {
    width: 120px;
    height: 120px;
    line-height: 120px;
  }
  .file-box {
    width: 120px;
    height: 120px;
    border: 1px dashed #c0ccda;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    .u117_img {
      width: 110px;
      height: 110px;
    }
    .demo-upload-list-cover {
      display: none;
      position: absolute;
      top: 0;
      bottom: 0;
      text-align: center;
      left: 0;
      right: 0;
      background: rgba(0, 0, 0, 0.6);
      font-size: 26px;
      color: #fff;
      i {
        cursor: pointer;
        color: #fff;
      }
    }
  }
  .file-box:hover .demo-upload-list-cover {
    display: block;
  }
  .el-upload__text {
    position: relative;
    span {
      position: absolute;
      color: #c0ccda;
      top: 62%;
      left: 50%;
      line-height: 22px;
      width: 100%;
      transform: translateX(-50%);
    }
  }
}
</style>
