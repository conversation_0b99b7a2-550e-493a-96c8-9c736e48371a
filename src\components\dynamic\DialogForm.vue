<!--
	<AUTHOR>
	@date 2022-09-19
	@desc 弹窗
-->
<template>
  <el-dialog
    :title="formConfig.title"
    :visible.sync="dialogVisible"
    width="680px"
    :close-on-click-modal="false"
    @closed="handleCancel"
  >
    <!-- 动态表单 -->
    <dynamic-form
      ref="dynamicForm"
      :form-data.sync="formConfig.formData"
      :form-item="formConfig.formItem"
      :form-rule="formConfig.formRule"
    />

    <div slot="footer" class="dialog-footer">
      <!-- 交互按钮 自定义 -->
      <slot v-if="formConfig.operateSlot" :name="formConfig.operateSlot" :formData="formConfig.formData" />

      <!-- 交互按钮 默认 -->
      <template v-else>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="btnLoading" @click="handleEvent">确认</el-button>
      </template>
    </div>
  </el-dialog>
</template>

<script>
import DynamicForm from './Form.vue'
export default {
  name: 'DynamicDialogForm',
  components: {
    DynamicForm
  },
  props: {
    // 是否显示
    show: {
      type: Boolean,
      default: false
    },
    // 提交按钮Loading
    btnLoading: {
      type: Boolean,
      default: false
    },
    // // 页面数据
    // data:{
    // 	type:Array,
    // 	default:()=>[]
    // },
    // 表单配置
    formConfig: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  computed: {
    dialogVisible: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    }
  },
  mounted() {},
  methods: {
    /**
		 * 按钮事件
		 * @param {Object} eventData
		 * @param {Object} eventName
		 */
    handleEvent: function(eventData, eventName) {
      this.$emit('event')
    },

    /**
		 * 关闭弹窗
		 */
    handleCancel: function() {
      // 重置表单
      this.$refs.dynamicForm.$refs.dialogForm.resetFields()
      this.dialogVisible = false
    }
  }
}
</script>
<style lang="scss" scoped></style>
